import React from 'react';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface TreatmentApproachesSectionProps {
  treatmentApproaches: {
    title: string;
    description: string;
    approaches: {
      title: string;
      description: string;
      features: string[];
    }[];
  };
}

const TreatmentApproachesSection: React.FC<TreatmentApproachesSectionProps> = ({ treatmentApproaches }) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "bg-muted/20",
      deviceInfo.isMobile ? "mobile-section" : "py-16"
    )}>
      <div className={deviceInfo.isMobile ? "mobile-container" : "container"}>
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className={cn(
            "font-bold mb-mobile-md",
            deviceInfo.isMobile ? "mobile-heading" : "text-3xl mb-6"
          )}>
            {treatmentApproaches.title}
          </h2>
          <p className={cn(
            "text-muted-foreground",
            deviceInfo.isMobile ? "mobile-text" : ""
          )}>
            {treatmentApproaches.description}
          </p>
        </div>

        <div className={cn(
          "grid gap-mobile-lg",
          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3 gap-8"
        )}>
          {treatmentApproaches.approaches.map((approach, index) => (
            <div key={index} className="card p-6 rounded-lg shadow-md medical-card">
              <h3 className={cn(
                "font-semibold mb-mobile-sm text-primary",
                deviceInfo.isMobile ? "mobile-subheading" : "text-xl mb-3"
              )}>
                {approach.title}
              </h3>
              <p className={cn(
                "text-muted-foreground mb-mobile-sm",
                deviceInfo.isMobile ? "mobile-text" : "mb-4"
              )}>
                {approach.description}
              </p>
              <ul className="space-y-2">
                {approach.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className={cn(
                    "text-muted-foreground",
                    deviceInfo.isMobile ? "mobile-text" : "text-sm"
                  )}>
                    • {feature}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

TreatmentApproachesSection.displayName = 'TreatmentApproachesSection';

export default TreatmentApproachesSection;
