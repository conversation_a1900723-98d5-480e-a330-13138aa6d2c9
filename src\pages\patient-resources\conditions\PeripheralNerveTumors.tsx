import React from 'react';
import { Helmet } from 'react-helmet-async';

import {
  NerveTumorAnatomySection,
  NerveTumorTreatmentComparison,
  NerveTumorExerciseSection,
  NerveTumorErgonomicsSection,
  NerveTumorWarningSignsSection
} from '@/components/medical-conditions/peripheral-nerve-tumors';
import { 
  ConditionHero,
  ConditionQuickFacts,
  ConditionOverviewSection,
  ConditionCauses,
  ConditionSymptoms,
  ConditionTreatment
} from '@/components/medical-conditions/shared';
import StandardPageLayout from '@/components/StandardPageLayout';
import { peripheralNerveTumorsData } from '@/data/conditions/peripheral-nerve-tumors';
import { useScrollToTop } from '@/hooks/useScrollToTop';

/**
 * Peripheral Nerve Tumors Component
 * Comprehensive guide to peripheral nerve tumors including schwannomas, neurofibromas, and neuromas
 * Provides unparalleled educational value with specialised components
 */
const PeripheralNerveTumors: React.FC = () => {
  useScrollToTop();

  const { info, quickFacts, causes, symptoms, diagnostics, treatments, exercises: _exercises, prevention: _prevention, prognosis } = peripheralNerveTumorsData;

  // Transform treatments for shared component compatibility
  const transformedTreatments = {
    conservative: treatments.filter(t => t.title !== 'Microsurgical Resection' && t.title !== 'Radiation Therapy'),
    surgical: treatments.filter(t => t.title === 'Microsurgical Resection' || t.title === 'Radiation Therapy')
  };

  return (
    <StandardPageLayout 
      title="Peripheral Nerve Tumors - Comprehensive Guide" 
      showHeader={false}
    >
      <Helmet>
        <title>Peripheral Nerve Tumors - Schwannomas, Neurofibromas & Neuromas | miNEURO</title>
        <meta 
          name="description" 
          content="Comprehensive guide to peripheral nerve tumors including schwannomas, neurofibromas, and neuromas. Learn about causes, symptoms, diagnosis, treatment options, and management strategies from leading neurological experts." 
        />
        <meta 
          name="keywords" 
          content="peripheral nerve tumors, schwannoma, neurofibroma, neuroma, nerve tumor surgery, microsurgical resection, neurofibromatosis, MPNST, nerve tumor symptoms" 
        />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/conditions/peripheral-nerve-tumors" />
        
        {/* Open Graph tags */}
        <meta property="og:title" content="Peripheral Nerve Tumors - Complete Guide | miNEURO" />
        <meta property="og:description" content="Expert guide to understanding and treating peripheral nerve tumors. Comprehensive information on schwannomas, neurofibromas, neuromas, and treatment strategies." />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://mineuro.com.au/patient-resources/conditions/peripheral-nerve-tumors" />
        <meta property="og:image" content="https://mineuro.com.au/images/peripheral-nerve-conditions/peripheral-nerve-tumors-hero.jpg" />
        
        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Peripheral Nerve Tumors - Complete Guide" />
        <meta name="twitter:description" content="Comprehensive guide to peripheral nerve tumors and treatment options." />
        <meta name="twitter:image" content="https://mineuro.com.au/images/peripheral-nerve-conditions/peripheral-nerve-tumors-hero.jpg" />
        
        {/* Medical/Health specific meta tags */}
        <meta name="health-condition" content="Peripheral Nerve Tumors" />
        <meta name="medical-specialty" content="Neurology, Neurosurgery, Oncology" />
        <meta name="treatment-type" content="Conservative, Surgical, Radiation" />
      </Helmet>

      <main className="flex-1 pt-20">
        {/* Hero Section */}
        <ConditionHero
          title={info.title}
          subtitle={info.subtitle}
          description={info.description}
          backgroundImage={info.heroImage.src}
          badge="Peripheral Nerve Conditions Library"
        />

        {/* Quick Facts */}
        <ConditionQuickFacts facts={quickFacts} />

        {/* Overview Section */}
        <ConditionOverviewSection
          title="Understanding Peripheral Nerve Tumors"
          description={[
            "Peripheral nerve tumors are growths that develop from the cells that make up peripheral nerves throughout the body. The vast majority are benign (non-cancerous), with schwannomas being the most common type, followed by neurofibromas and traumatic neuromas.",
            "These tumors can occur anywhere along the peripheral nervous system, from the spinal nerve roots to the smallest nerve branches. While most are slow-growing and cause minimal symptoms, some can lead to significant neurological dysfunction or, rarely, malignant transformation.",
            "Understanding the different types of nerve tumors, their characteristics, and treatment options is essential for optimal management and outcomes. Early recognition and appropriate treatment can preserve nerve function and prevent complications."
          ]}
          keyPoints={[
            { text: "Most peripheral nerve tumors are benign (>95%)" },
            { text: "Schwannomas are the most common type" },
            { text: "Can occur at any age but peak incidence in 20-50 years" },
            { text: "May be associated with genetic syndromes (NF1, NF2)" },
            { text: "Microsurgical resection offers excellent outcomes" },
            { text: "Early detection improves treatment success" }
          ]}
          imageSrc={info.heroImage.src}
          imageAlt={info.heroImage.alt}
          imageCaption="Peripheral nerve tumor anatomy showing relationship to nerve structures"
        />

        {/* Causes and Risk Factors */}
        <ConditionCauses
          causes={causes}
        />

        {/* Symptoms */}
        <ConditionSymptoms
          symptomCategories={symptoms}
        />

        {/* Specialised Nerve Tumor Anatomy Section */}
        <NerveTumorAnatomySection />

        {/* Diagnostic Methods */}
        <section className="section-background border-y border-border/50 py-16 md:py-24">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-6">
                Diagnostic Evaluation
              </h2>
              <p className="text-lg text-foreground/80 max-w-3xl mx-auto">
                Comprehensive assessment to confirm diagnosis, characterise tumor type, and guide treatment planning
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {diagnostics.map((diagnostic, index) => {
                const IconComponent = diagnostic.icon;
                return (
                  <div key={index} className="medical-card p-6">
                    <div className="p-4 rounded-xl bg-primary/10 border border-primary/20 mb-4 w-fit">
                      <IconComponent className="w-8 h-8 text-primary" />
                    </div>
                    <h3 className="text-enhanced-subheading font-semibold mb-3">{diagnostic.title}</h3>
                    <p className="text-enhanced-body text-sm mb-4">{diagnostic.description}</p>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-enhanced-caption">Accuracy:</span>
                        <span className="text-enhanced-body text-sm font-medium">{diagnostic.accuracy}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-enhanced-caption">Duration:</span>
                        <span className="text-enhanced-body text-sm font-medium">{diagnostic.timeRequired}</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Treatment Options */}
        <ConditionTreatment
          title="Treatment Approaches"
          subtitle="Comprehensive treatment options from observation to advanced microsurgical techniques"
          conservativeOptions={transformedTreatments.conservative}
          surgicalOptions={transformedTreatments.surgical}
          treatmentPhases={[
            {
              phase: "Phase 1",
              title: "Initial Assessment",
              duration: "1-2 weeks",
              goals: ["Confirm diagnosis", "Characterise tumor", "Assess symptoms"],
              treatments: ["Clinical examination", "MRI imaging", "Nerve conduction studies", "Tissue sampling if needed"]
            },
            {
              phase: "Phase 2", 
              title: "Treatment Decision",
              duration: "Variable",
              goals: ["Determine optimal approach", "Consider patient factors", "Plan intervention"],
              treatments: ["Observation vs surgery", "Microsurgical planning", "Risk-benefit analysis", "Patient counselling"]
            },
            {
              phase: "Phase 3",
              title: "Treatment Implementation",
              duration: "Variable",
              goals: ["Execute treatment plan", "Preserve nerve function", "Achieve cure"],
              treatments: ["Microsurgical resection", "Radiation therapy", "Symptom management", "Rehabilitation"]
            }
          ]}
        />

        {/* Specialised Treatment Comparison */}
        <NerveTumorTreatmentComparison />

        {/* Exercise and Physical Therapy Guide */}
        <NerveTumorExerciseSection />

        {/* Ergonomic and Lifestyle Recommendations */}
        <NerveTumorErgonomicsSection />

        {/* Warning Signs and When to Seek Help */}
        <NerveTumorWarningSignsSection />

        {/* Prognosis and Recovery */}
        <section className="section-background-alt border-y border-border/50 py-16 md:py-24">
          <div className="container">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-foreground mb-6">
                Prognosis and Recovery Expectations
              </h2>
              <p className="text-lg text-foreground/80 max-w-3xl mx-auto">
                Understanding recovery potential and factors that influence long-term outcomes for peripheral nerve tumors
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8">
              {prognosis.map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <div key={index} className="medical-card p-6">
                    <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4 w-fit">
                      <IconComponent className="w-8 h-8 text-info" />
                    </div>
                    <h3 className="text-enhanced-subheading font-semibold mb-3">{item.title}</h3>
                    <p className="text-enhanced-body text-sm mb-4">{item.description}</p>
                    <ul className="space-y-2">
                      {item.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-info mt-2 flex-shrink-0" />
                          <span className="text-enhanced-body text-sm">{detail}</span>
                        </li>
                      ))}
                    </ul>
                    <div className="mt-4 pt-4 border-t border-border/50">
                      <span className="text-enhanced-caption font-medium">Timeline: </span>
                      <span className="text-enhanced-body text-sm">{item.timeframe}</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </section>
      </main>
    </StandardPageLayout>
  );
};

PeripheralNerveTumors.displayName = 'PeripheralNerveTumors';

export default PeripheralNerveTumors;
