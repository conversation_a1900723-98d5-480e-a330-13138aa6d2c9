import { Hospital, Phone, MapPin, Clock, AlertTriangle, ExternalLink } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface HospitalContact {
  address: string;
  phone: string;
  emergency?: string;
  website: string;
}

interface HospitalServices {
  emergencyDepartment?: string;
  admissions?: string;
  consultations?: string;
  visitingHours: string;
  parking: string;
}

interface HospitalProcedures {
  emergencyPresentation?: string;
  admissionProcess?: string;
  emergencyContact?: string;
}

interface HospitalInfo {
  id: string;
  name: string;
  type: string;
  emergency: boolean;
  contact: HospitalContact;
  services: HospitalServices;
  procedures?: HospitalProcedures;
}

interface EmergencyProcedure {
  situation: string;
  action: string;
  description: string;
}

interface EmergencyProcedures {
  title: string;
  icon: React.ComponentType;
  procedures: EmergencyProcedure[];
}

interface HospitalAffiliationsSectionProps {
  title: string;
  subtitle: string;
  hospitals: HospitalInfo[];
  emergencyProcedures: EmergencyProcedures;
}

const HospitalAffiliationsSection: React.FC<HospitalAffiliationsSectionProps> = ({
  title,
  subtitle,
  hospitals,
  emergencyProcedures
}) => {
  const deviceInfo = useDeviceDetection();

  const getHospitalTypeColor = (type: string) => {
    switch (type) {
      case 'Primary Affiliation':
        return 'badge-emergency';
      case 'Affiliated Hospital':
        return 'badge-info';
      case 'Consulting Hospital':
        return 'badge-routine';
      default:
        return 'badge-info';
    }
  };

  return (
    <section className="py-16">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            {subtitle}
          </p>
        </div>

        {/* Hospital Cards */}
        <div className={cn(
          "grid gap-6 mb-12",
          deviceInfo.isMobile 
            ? "grid-cols-1" 
            : "grid-cols-1 md:grid-cols-2"
        )}>
          {hospitals.map((hospital) => (
            <Card key={hospital.id} className="shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start mb-2">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Hospital className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg text-primary">
                        {hospital.name}
                      </CardTitle>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <Badge className={getHospitalTypeColor(hospital.type)}>
                      {hospital.type}
                    </Badge>
                    {hospital.emergency && (
                      <Badge className="bg-muted-light text-foreground border-border">
                        24/7 Emergency
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Contact Information */}
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <MapPin className="h-4 w-4 text-primary mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-sm">Address</h4>
                      <p className="text-muted-foreground text-sm">
                        {hospital.contact.address}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <div className="flex items-start gap-3">
                      <Phone className="h-4 w-4 text-primary mt-1 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold text-sm">Phone</h4>
                        <a 
                          href={`tel:${hospital.contact.phone.replace(/\s/g, '')}`}
                          className="text-muted-foreground text-sm hover:text-primary transition-colors"
                        >
                          {hospital.contact.phone}
                        </a>
                      </div>
                    </div>

                    {hospital.contact.emergency && (
                      <div className="flex items-start gap-3">
                        <AlertTriangle className="h-4 w-4 text-foreground mt-1 flex-shrink-0" />
                        <div>
                          <h4 className="font-semibold text-sm text-foreground">Emergency</h4>
                          <a 
                            href={`tel:${hospital.contact.emergency.replace(/\s/g, '')}`}
                            className="text-foreground text-sm hover:text-foreground transition-colors font-medium"
                          >
                            {hospital.contact.emergency}
                          </a>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Services */}
                <div>
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Clock className="h-4 w-4 text-primary" />
                    Services & Hours
                  </h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    {hospital.services.emergencyDepartment && (
                      <p>• Emergency Department: {hospital.services.emergencyDepartment}</p>
                    )}
                    {hospital.services.admissions && (
                      <p>• Admissions: {hospital.services.admissions}</p>
                    )}
                    {hospital.services.consultations && (
                      <p>• Consultations: {hospital.services.consultations}</p>
                    )}
                    <p>• Visiting Hours: {hospital.services.visitingHours}</p>
                    <p>• Parking: {hospital.services.parking}</p>
                  </div>
                </div>

                {/* Emergency Procedures */}
                {hospital.procedures && (
                  <div>
                    <h4 className="font-semibold mb-2">Emergency Procedures</h4>
                    <div className="text-sm text-muted-foreground space-y-1">
                      {hospital.procedures.emergencyPresentation && (
                        <p>• {hospital.procedures.emergencyPresentation}</p>
                      )}
                      {hospital.procedures.admissionProcess && (
                        <p>• {hospital.procedures.admissionProcess}</p>
                      )}
                      {hospital.procedures.emergencyContact && (
                        <p>• {hospital.procedures.emergencyContact}</p>
                      )}
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-2 pt-4">
                  <Button asChild size="sm">
                    <a href={`tel:${hospital.contact.phone.replace(/\s/g, '')}`}>
                      <Phone className="h-4 w-4 mr-2" />
                      Call
                    </a>
                  </Button>
                  
                  <Button asChild variant="outline" size="sm">
                    <a 
                      href={hospital.contact.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Website
                    </a>
                  </Button>

                  {hospital.emergency && (
                    <Button asChild variant="muted" size="sm">
                      <a href={`tel:${hospital.contact.emergency?.replace(/\s/g, '') || hospital.contact.phone.replace(/\s/g, '')}`}>
                        <AlertTriangle className="h-4 w-4 mr-2" />
                        Emergency
                      </a>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Emergency Procedures */}
        <Card className="shadow-lg bg-muted border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-foreground">
              <AlertTriangle className="h-6 w-6" />
              {emergencyProcedures.title}
            </CardTitle>
          </CardHeader>
          
          <CardContent>
            <div className={cn(
              "grid gap-4",
              deviceInfo.isMobile 
                ? "grid-cols-1" 
                : "grid-cols-1 md:grid-cols-2"
            )}>
              {emergencyProcedures.procedures.map((procedure, index) => (
                <div key={index} className="bg-background p-4 rounded-lg border border-border">
                  <h4 className="font-bold text-foreground mb-2">
                    {procedure.situation}
                  </h4>
                  <p className="font-semibold text-foreground mb-2">
                    {procedure.action}
                  </p>
                  <p className="text-foreground text-sm">
                    {procedure.description}
                  </p>
                </div>
              ))}
            </div>

            <div className="mt-6 text-center">
              <Card className="max-w-2xl mx-auto bg-muted/50 dark:bg-muted/30 border-border">
                <CardContent className="p-4">
                  <h3 className="font-bold text-foreground mb-2">
                    🚨 Emergency Contact Protocol
                  </h3>
                  <p className="text-foreground text-sm">
                    For immediate life-threatening emergencies, always call <strong>000</strong> first.
                    For urgent neurosurgical matters during business hours, call <strong>(03) 9008 4200</strong>.
                    After hours, present to Epworth Richmond Emergency Department.
                  </p>
                </CardContent>
              </Card>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default HospitalAffiliationsSection;
