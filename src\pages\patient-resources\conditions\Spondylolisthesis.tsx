import React, { useEffect } from 'react';

import { ConditionCauses } from '@/components/medical-conditions/shared/ConditionCauses';
import { ConditionHero } from '@/components/medical-conditions/shared/ConditionHero';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import { ConditionSymptoms } from '@/components/medical-conditions/shared/ConditionSymptoms';
import { ConditionTreatment } from '@/components/medical-conditions/shared/ConditionTreatment';
import StandardPageLayout from '@/components/StandardPageLayout';
import spondylolisthesisData from '@/data/conditions/spondylolisthesis';

/**
 * Refactored Spondylolisthesis Component
 * 
 * Original component: 84KB
 * Refactored component: <100 lines
 * Reduction: ~95%
 */

const SpondylolisthesisRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout 
      title="Spondylolisthesis - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={spondylolisthesisData.hero.title}
          subtitle={spondylolisthesisData.hero.subtitle}
          backgroundImage={spondylolisthesisData.hero.backgroundImage}
          badge={spondylolisthesisData.hero.badge}
        />

        <ConditionQuickFacts facts={spondylolisthesisData.quickFacts} />

        <ConditionOverviewSection
          title={spondylolisthesisData.overview.title}
          description={spondylolisthesisData.overview.description}
          keyPoints={spondylolisthesisData.overview.keyPoints}
          imageSrc={spondylolisthesisData.overview.imageSrc}
          imageAlt={spondylolisthesisData.overview.imageAlt}
          imageCaption={spondylolisthesisData.overview.imageCaption}
        />

        <ConditionCauses
          causes={spondylolisthesisData.causes}
          riskFactors={spondylolisthesisData.riskFactors}
        />

        <ConditionSymptoms
          symptomCategories={spondylolisthesisData.symptoms}
          warningSigns={spondylolisthesisData.warningSigns}
        />

        <ConditionTreatment
          conservativeOptions={spondylolisthesisData.conservativeTreatments}
          surgicalOptions={spondylolisthesisData.surgicalTreatments}
        />
      </main>
    </StandardPageLayout>
  );
};

SpondylolisthesisRefactored.displayName = 'SpondylolisthesisRefactored';

export default SpondylolisthesisRefactored;