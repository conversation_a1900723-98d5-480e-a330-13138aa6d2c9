import React from 'react';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface DoctorProfileSectionProps {
  doctorProfile: {
    title: string;
    description: string;
    qualifications: string[];
    experience: string;
    specializations: string[];
    affiliations: string[];
  };
}

const DoctorProfileSection: React.FC<DoctorProfileSectionProps> = ({ doctorProfile }) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "bg-muted/30",
      deviceInfo.isMobile ? "mobile-section" : "py-16"
    )}>
      <div className={deviceInfo.isMobile ? "mobile-container" : "container"}>
        <div className="max-w-4xl mx-auto">
          <h2 className={cn(
            "font-bold text-center mb-mobile-lg",
            deviceInfo.isMobile ? "mobile-heading" : "text-3xl mb-8"
          )}>
            {doctorProfile.title}
          </h2>
          <div className={cn(
            "text-center mb-mobile-lg",
            deviceInfo.isMobile ? "mobile-text" : "text-lg mb-8"
          )}>
            <p className="text-muted-foreground">
              {doctorProfile.description}
            </p>
          </div>
          
          <div className={cn(
            "grid gap-mobile-lg",
            deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2 gap-8"
          )}>
            {/* Qualifications */}
            <div className="card p-6 rounded-lg shadow-md medical-card">
              <h3 className={cn(
                "font-semibold mb-mobile-sm text-primary",
                deviceInfo.isMobile ? "mobile-subheading" : "text-xl mb-4"
              )}>
                Qualifications & Certifications
              </h3>
              <ul className="space-y-2">
                {doctorProfile.qualifications.map((qualification, index) => (
                  <li key={index} className={cn(
                    "text-muted-foreground",
                    deviceInfo.isMobile ? "mobile-text" : ""
                  )}>
                    • {qualification}
                  </li>
                ))}
              </ul>
            </div>

            {/* Specializations */}
            <div className="card p-6 rounded-lg shadow-md medical-card">
              <h3 className={cn(
                "font-semibold mb-mobile-sm text-primary",
                deviceInfo.isMobile ? "mobile-subheading" : "text-xl mb-4"
              )}>
                Areas of Specialization
              </h3>
              <ul className="space-y-2">
                {doctorProfile.specializations.map((specialization, index) => (
                  <li key={index} className={cn(
                    "text-muted-foreground",
                    deviceInfo.isMobile ? "mobile-text" : ""
                  )}>
                    • {specialization}
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Experience and Affiliations */}
          <div className={cn(
            "text-center mt-mobile-lg",
            deviceInfo.isMobile ? "" : "mt-8"
          )}>
            <div className="card p-6 rounded-lg shadow-md medical-card">
              <p className={cn(
                "font-semibold text-primary mb-mobile-sm",
                deviceInfo.isMobile ? "mobile-text" : "text-lg mb-4"
              )}>
                {doctorProfile.experience}
              </p>
              <div className={cn(
                "grid gap-mobile-sm",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2 md:grid-cols-4 gap-4"
              )}>
                {doctorProfile.affiliations.map((affiliation, index) => (
                  <div key={index} className={cn(
                    "text-muted-foreground",
                    deviceInfo.isMobile ? "mobile-text text-center" : "text-sm text-center"
                  )}>
                    {affiliation}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

DoctorProfileSection.displayName = 'DoctorProfileSection';

export default DoctorProfileSection;
