import React from 'react';

interface EmergencyItem {
  label: string;
  value: string;
  isLink?: boolean;
}

interface EmergencySection {
  type: 'emergency' | 'hospital' | 'warning';
  title: string;
  items?: EmergencyItem[];
  content?: string;
}

interface EmergencyContactProps {
  title: string;
  sections: EmergencySection[];
}

const EmergencyContact: React.FC<EmergencyContactProps> = ({
  title,
  sections
}) => {
  const getSectionStyles = (type: string) => {
    switch (type) {
      case 'emergency':
        return "p-4 border border-border bg-muted/50 dark:bg-muted/30 rounded-md";
      case 'hospital':
        return "p-4 border border-primary/50 bg-primary/10 dark:bg-primary/5 rounded-md";
      case 'warning':
        return "p-3 bg-info/10 dark:bg-info/5 rounded-md border border-info/50";
      default:
        return "p-4 border border-border bg-card rounded-md";
    }
  };

  const getTitleStyles = (type: string) => {
    switch (type) {
      case 'emergency':
        return "font-bold text-foreground mb-3";
      case 'hospital':
        return "font-bold text-primary mb-3";
      case 'warning':
        return "text-sm text-info dark:text-info font-medium";
      default:
        return "font-bold text-foreground mb-3";
    }
  };

  const getContentStyles = (type: string) => {
    switch (type) {
      case 'hospital':
        return "space-y-1 text-sm text-primary/80";
      default:
        return "space-y-2";
    }
  };

  return (
    <div>
      <h3 className="text-xl font-bold mb-4">{title}</h3>
      <div className="glass-card p-6 mb-8">
        <div className="space-y-4">
          {sections.map((section, index) => (
            <div key={index} className={getSectionStyles(section.type)}>
              <h4 className={getTitleStyles(section.type)}>
                {section.title}
              </h4>
              
              {section.items && (
                <div className={getContentStyles(section.type)}>
                  {section.items.map((item, itemIndex) => (
                    <p key={itemIndex} className="text-sm">
                      <span className="font-bold">{item.label}</span>
                      {item.isLink ? (
                        <a 
                          href={item.value} 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="underline hover:text-info ml-1"
                        >
                          {item.value}
                        </a>
                      ) : (
                        <span className={
                          section.type === 'emergency'
                            ? "text-foreground font-bold ml-2"
                            : "ml-1"
                        }>
                          {item.value}
                        </span>
                      )}
                    </p>
                  ))}
                </div>
              )}
              
              {section.content && (
                <p className={getTitleStyles(section.type)}>
                  <strong>Important:</strong> {section.content}
                </p>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EmergencyContact;
