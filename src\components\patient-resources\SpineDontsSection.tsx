import { XCircle } from 'lucide-react';
import React from 'react';

import { SpineDont } from '@/data/patient-resources/ageSpecificRecommendationsData';

interface SpineDontsSectionProps {
  title: string;
  spineDonts: SpineDont[];
}

const SpineDontsSection: React.FC<SpineDontsSectionProps> = ({ title, spineDonts }) => {
  return (
    <div>
      <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{title}</h2>

      <div className="bg-muted/30 p-6 rounded-lg my-8">
        <ul className="space-y-4 list-none pl-0">
          {spineDonts.map((dont) => (
            <li key={dont.id} className="flex items-start">
              <XCircle className="h-6 w-6 text-foreground mr-2 flex-shrink-0 mt-0.5" />
              <span>
                <strong>{dont.title}</strong> – {dont.description}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

SpineDontsSection.displayName = 'SpineDontsSection';

export default SpineDontsSection;
