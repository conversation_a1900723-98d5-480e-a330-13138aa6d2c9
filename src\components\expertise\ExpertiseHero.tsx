import React from 'react';

import PageHeader from '@/components/PageHeader';

interface ExpertiseHeroProps {
  title: string;
  subtitle: string;
  backgroundImage: string;
}

const ExpertiseHero: React.FC<ExpertiseHeroProps> = ({
  title, 
  subtitle, 
  backgroundImage 
}) => {
  return (
    <PageHeader
      title={title}
      subtitle={subtitle}
      backgroundImage={backgroundImage}
      enableParallax={true}
    />
  );
};

ExpertiseHero.displayName = 'ExpertiseHero';

export default ExpertiseHero;
