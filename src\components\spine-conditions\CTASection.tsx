import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowRight, Calendar, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface CTASectionProps {
  title?: string;
  subtitle?: string;
  primaryButtonText?: string;
  primaryButtonLink?: string;
  secondaryButtonText?: string;
  secondaryButtonLink?: string;
}

const CTASection: React.FC<CTASectionProps> = ({
  title = "Ready to Take the Next Step?",
  subtitle = "Schedule a consultation with Dr<PERSON> to discuss your spine condition and treatment options.",
  primaryButtonText = "Book Consultation",
  primaryButtonLink = "/appointments",
  secondaryButtonText = "Contact Us",
  secondaryButtonLink = "/contact"
}) => {
  return (
    <section className="py-16 bg-gradient-to-br from-primary/5 via-background to-muted/30">
      <div className="container">
        <Card className="max-w-4xl mx-auto shadow-xl border-primary/20">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-3xl font-bold text-foreground mb-4">
              {title}
            </CardTitle>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {subtitle}
            </p>
          </CardHeader>
          
          <CardContent className="text-center pb-8">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="group">
                <Link to={primaryButtonLink}>
                  <Calendar className="mr-2 h-5 w-5" />
                  {primaryButtonText}
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              
              <Button asChild variant="outline" size="lg">
                <Link to={secondaryButtonLink}>
                  <Phone className="mr-2 h-5 w-5" />
                  {secondaryButtonText}
                </Link>
              </Button>
            </div>
            
            <div className="mt-8 pt-6 border-t border-border/50">
              <p className="text-sm text-muted-foreground">
                <strong>Emergency?</strong> Call{' '}
                <a href="tel:+61394284128" className="text-primary hover:underline font-medium">
                  (03) 9428 4128
                </a>{' '}
                for urgent spine conditions
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default CTASection;
