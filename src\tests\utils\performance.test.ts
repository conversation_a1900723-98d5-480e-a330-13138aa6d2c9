import { act, renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { PerformanceMonitor, usePerformanceMetric } from '@/lib/performance';
import { mockUtils } from '@/lib/test-utils';

// Get reference to global performance mock
const mockPerformance = global.performance as Performance & { memory?: { usedJSHeapSize: number } };

// Mock PerformanceObserver (not covered by global setup)
const mockPerformanceObserver = vi.fn();
mockPerformanceObserver.prototype.observe = vi.fn();
mockPerformanceObserver.prototype.disconnect = vi.fn();

Object.defineProperty(global, 'PerformanceObserver', {
  value: mockPerformanceObserver,
  writable: true,
});

describe('Performance Monitoring', () => {
  beforeEach(() => {
    // Use standardized mock reset
    mockUtils.resetAllMocks();
    // Reset singleton instance
    (PerformanceMonitor as unknown as { instance: undefined }).instance = undefined;
  });

  describe('PerformanceMonitor', () => {
    it('creates singleton instance', () => {
      const instance1 = PerformanceMonitor.getInstance();
      const instance2 = PerformanceMonitor.getInstance();

      expect(instance1).toBe(instance2);
    });

    it('initializes performance observers', () => {
      const monitor = PerformanceMonitor.getInstance();
      monitor.initialize();

      expect(mockPerformanceObserver).toHaveBeenCalled();
    });

    it('starts and ends measurements correctly', () => {
      const monitor = PerformanceMonitor.getInstance();

      mockPerformance.now
        .mockReturnValueOnce(100) // Start time
        .mockReturnValueOnce(200); // End time

      monitor.startMeasure('test-metric');
      const result = monitor.endMeasure('test-metric');

      expect(result).toEqual({
        name: 'test-metric',
        startTime: 100,
        endTime: 200,
        duration: 100,
      });
    });

    it('handles missing measurements gracefully', () => {
      const monitor = PerformanceMonitor.getInstance();

      const result = monitor.endMeasure('non-existent-metric');

      expect(result).toBeNull();
    });

    it('logs navigation metrics', () => {
      const monitor = PerformanceMonitor.getInstance();
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });

      const mockNavigationEntry = {
        entryType: 'navigation',
        loadEventEnd: 1000,
        loadEventStart: 500,
        domContentLoadedEventEnd: 800,
        domContentLoadedEventStart: 600,
        responseEnd: 400,
        requestStart: 100,
      } as PerformanceNavigationTiming;

      monitor.logNavigationMetrics(mockNavigationEntry);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Navigation Metrics'),
        expect.objectContaining({
          pageLoadTime: 500,
          domContentLoaded: 200,
          networkTime: 300,
        })
      );

      consoleSpy.mockRestore();
    });

    it('logs resource metrics', () => {
      const monitor = PerformanceMonitor.getInstance();
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });

      const mockResourceEntry = {
        entryType: 'resource',
        name: 'https://example.com/image.jpg',
        responseEnd: 500,
        requestStart: 100,
        transferSize: 1024
      };

      monitor.logResourceMetrics(mockResourceEntry as PerformanceResourceTiming);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Resource Metrics'),
        expect.objectContaining({
          resource: 'https://example.com/image.jpg',
          loadTime: 400,
          size: 1024,
        })
      );

      consoleSpy.mockRestore();
    });

    it('tracks Core Web Vitals', () => {
      const monitor = PerformanceMonitor.getInstance();
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });

      // Mock LCP entry
      const mockLCPEntry = {
        entryType: 'largest-contentful-paint',
        startTime: 1500,
        element: document.createElement('img')
      };

      monitor.logLargestContentfulPaint(mockLCPEntry as PerformanceEntry);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Largest Contentful Paint'),
        expect.objectContaining({
          lcp: 1500,
          element: 'IMG',
        })
      );

      consoleSpy.mockRestore();
    });

    it('handles layout shift metrics', () => {
      const monitor = PerformanceMonitor.getInstance();
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });

      const mockLayoutShiftEntry = {
        entryType: 'layout-shift',
        value: 0.1,
        hadRecentInput: false
      };

      monitor.logLayoutShift(mockLayoutShiftEntry as PerformanceEntry);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Layout Shift'),
        expect.objectContaining({
          cls: 0.1,
          hadRecentInput: false,
        })
      );

      consoleSpy.mockRestore();
    });

    it('cleans up observers on destroy', () => {
      const monitor = PerformanceMonitor.getInstance();
      monitor.initialize();

      const disconnectSpy = vi.spyOn(mockPerformanceObserver.prototype, 'disconnect');

      monitor.destroy();

      expect(disconnectSpy).toHaveBeenCalled();
    });
  });

  describe('usePerformanceMetric Hook', () => {
    it('starts and ends measurements on mount/unmount', () => {
      const monitor = PerformanceMonitor.getInstance();
      const startSpy = vi.spyOn(monitor, 'startMeasure');
      const endSpy = vi.spyOn(monitor, 'endMeasure');

      const { unmount } = renderHook(() =>
        usePerformanceMetric('test-component')
      );

      expect(startSpy).toHaveBeenCalledWith('test-component');

      unmount();

      expect(endSpy).toHaveBeenCalledWith('test-component');
    });

    it('restarts measurements when dependencies change', () => {
      const monitor = PerformanceMonitor.getInstance();
      const startSpy = vi.spyOn(monitor, 'startMeasure');
      const endSpy = vi.spyOn(monitor, 'endMeasure');

      let dependency = 'initial';

      const { rerender } = renderHook(() =>
        usePerformanceMetric('test-component', [dependency])
      );

      expect(startSpy).toHaveBeenCalledTimes(1);

      dependency = 'changed';
      rerender();

      expect(endSpy).toHaveBeenCalledTimes(1);
      expect(startSpy).toHaveBeenCalledTimes(2);
    });

    it('returns measurement functions', () => {
      const { result } = renderHook(() =>
        usePerformanceMetric('test-component')
      );

      expect(result.current).toEqual({
        startMeasure: expect.any(Function),
        endMeasure: expect.any(Function),
      });
    });

    it('allows manual measurements', () => {
      const monitor = PerformanceMonitor.getInstance();
      const startSpy = vi.spyOn(monitor, 'startMeasure');
      const endSpy = vi.spyOn(monitor, 'endMeasure');

      const { result } = renderHook(() =>
        usePerformanceMetric('test-component')
      );

      act(() => {
        result.current.startMeasure('manual-metric');
        result.current.endMeasure('manual-metric');
      });

      expect(startSpy).toHaveBeenCalledWith('manual-metric');
      expect(endSpy).toHaveBeenCalledWith('manual-metric');
    });
  });

  describe('Performance Utilities', () => {
    it('measures component render time', async () => {
      const monitor = PerformanceMonitor.getInstance();

      mockPerformance.now
        .mockReturnValueOnce(100) // Start time
        .mockReturnValueOnce(150); // End time

      monitor.startMeasure('component-render');

      // Simulate component rendering
      await new Promise(resolve => setTimeout(resolve, 50));

      const result = monitor.endMeasure('component-render');

      expect(result?.duration).toBe(50);
    });

    it('tracks bundle loading performance', () => {
      const monitor = PerformanceMonitor.getInstance();
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => { });

      const mockResourceEntry = {
        entryType: 'resource',
        name: 'https://example.com/chunk-abc123.js',
        responseEnd: 800,
        requestStart: 200,
        transferSize: 50000
      };

      monitor.logResourceMetrics(mockResourceEntry as PerformanceResourceTiming);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Resource Metrics'),
        expect.objectContaining({
          loadTime: 600,
          size: 50000,
        })
      );

      consoleSpy.mockRestore();
    });

    it('monitors image loading performance', () => {
      const monitor = PerformanceMonitor.getInstance();

      mockPerformance.now
        .mockReturnValueOnce(100) // Start time
        .mockReturnValueOnce(300); // End time

      monitor.startMeasure('image-load-test.jpg');
      const result = monitor.endMeasure('image-load-test.jpg');

      expect(result?.duration).toBe(200);
    });
  });

  describe('Error Handling', () => {
    it('handles performance API unavailability', () => {
      // Mock unavailable performance API
      Object.defineProperty(global, 'performance', {
        value: undefined,
        writable: true,
      });

      expect(() => {
        const monitor = PerformanceMonitor.getInstance();
        monitor.initialize();
      }).not.toThrow();
    });

    it('handles PerformanceObserver unavailability', () => {
      Object.defineProperty(global, 'PerformanceObserver', {
        value: undefined,
        writable: true,
      });

      expect(() => {
        const monitor = PerformanceMonitor.getInstance();
        monitor.initialize();
      }).not.toThrow();
    });

    it('handles measurement errors gracefully', () => {
      const monitor = PerformanceMonitor.getInstance();

      // Mock performance.now to throw error
      mockPerformance.now.mockImplementation(() => {
        throw new Error('Performance API error');
      });

      expect(() => {
        monitor.startMeasure('error-test');
      }).not.toThrow();
    });
  });

  describe('Memory Management', () => {
    it('limits stored metrics to prevent memory leaks', () => {
      const monitor = PerformanceMonitor.getInstance();

      // Add many metrics
      for (let i = 0; i < 2000; i++) {
        monitor.startMeasure(`metric-${i}`);
        monitor.endMeasure(`metric-${i}`);
      }

      // Should not exceed maxMetrics limit (1000)
      const metrics = (monitor as unknown as { metrics: Map<string, unknown> }).metrics;
      expect(metrics.size).toBeLessThanOrEqual(1000);
    });

    it('cleans up old metrics when limit is reached', () => {
      const monitor = PerformanceMonitor.getInstance();

      // Fill up to limit
      for (let i = 0; i < 1000; i++) {
        monitor.startMeasure(`metric-${i}`);
        monitor.endMeasure(`metric-${i}`);
      }

      // Add one more to trigger cleanup
      monitor.startMeasure('new-metric');
      monitor.endMeasure('new-metric');

      const metrics = (monitor as unknown as { metrics: Map<string, unknown> }).metrics;
      expect(metrics.has('new-metric')).toBe(true);
      expect(metrics.has('metric-0')).toBe(false); // Old metric should be removed
    });
  });
});