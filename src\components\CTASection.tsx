import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import en from '@/locales/en';

interface CTASectionProps {
  className?: string;
  title?: string;
  description?: string;
  primaryButtonText?: string;
  primaryButtonLink?: string;
  secondaryButtonText?: string;
  secondaryButtonLink?: string;
  bgClass?: string;
  id?: string;
}

const CTASection: React.FC<CTASectionProps> = React.memo(({
  className = "",
  title,
  description,
  primaryButtonText,
  primaryButtonLink = "/appointments",
  secondaryButtonText,
  secondaryButtonLink = "/contact",
  bgClass: _bgClass = "bg-primary/5",
  id,
}: CTASectionProps) => {
  const { t } = useLanguage();
  const deviceInfo = useDeviceDetection();

  // Fallback to English translations if context fails
  const safeT = t || en;

  // Use provided text or default to translations with safety checks
  const ctaTitle = title || safeT?.cta?.title || 'Book Your Consultation';
  const ctaDescription = description || safeT?.cta?.description || 'Schedule your appointment today';
  const primaryBtnText = primaryButtonText || safeT?.cta?.primaryButton || 'Book Now';
  const secondaryBtnText = secondaryButtonText || safeT?.navigation?.contact || 'Contact';

  // Generate unique IDs for accessibility
  const sectionId = id || `cta-section-${Math.random().toString(36).substring(2, 9)}`;
  const headingId = `cta-heading-${sectionId}`;
  const descriptionId = `cta-description-${sectionId}`;

  return (
    <section
      className={cn(
        "section-spacing section-background-primary",
        className
      )}
      id={sectionId}
      aria-labelledby={headingId}
      aria-describedby={descriptionId}
    >
      <div className="section-container">
        <div className="text-center mx-auto max-w-4xl">
          <h2
            id={headingId}
            className={cn(
              "text-headline mb-6",
              deviceInfo.isMobile ? "text-3xl" : "text-4xl lg:text-5xl"
            )}
          >
            {ctaTitle}
          </h2>
          <p
            id={descriptionId}
            className={cn(
              "lead mb-12",
              deviceInfo.isMobile ? "text-base" : "text-xl"
            )}
          >
            {ctaDescription}
          </p>
          <div className={cn(
            "flex justify-center gap-6",
            deviceInfo.isMobile
              ? "flex-col items-center"
              : "flex-col sm:flex-row"
          )}>
            <Button
              asChild
              size={deviceInfo.isMobile ? "default" : "lg"}
              className={cn(
                "btn-primary touch-feedback",
                deviceInfo.isMobile ? "w-full max-w-sm" : "min-w-[200px]"
              )}
            >
              <Link
                to={primaryButtonLink}
                aria-describedby={descriptionId}
              >
                {primaryBtnText}
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              size={deviceInfo.isMobile ? "default" : "lg"}
              className={cn(
                "btn-outline touch-feedback",
                deviceInfo.isMobile ? "w-full max-w-sm" : "min-w-[200px]"
              )}
            >
              <Link
                to={secondaryButtonLink}
                aria-describedby={descriptionId}
              >
                {secondaryBtnText}
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
});

CTASection.displayName = 'CTASection';

export default CTASection;
