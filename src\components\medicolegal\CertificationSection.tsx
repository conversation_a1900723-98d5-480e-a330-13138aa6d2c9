import React from 'react';

import SafeImage from '@/components/SafeImage';

interface CertificationSectionProps {
  translations: {
    impairmentAssessmentDetails?: {
      title?: string;
      description?: string;
    };
  };
}

const CertificationSection: React.FC<CertificationSectionProps> = ({ translations }) => {
  return (
    <section className="py-16">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{translations.impairmentAssessmentDetails?.title || 'Medicolegal Impairment Assessment'}</h2>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            {translations.impairmentAssessmentDetails?.description || 'Comprehensive impairment assessment according to established medical and legal standards.'}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
          <div>
            <h3 className="text-enhanced-heading text-2xl font-bold mb-4 text-primary">AMA Guidelines Compliance</h3>
            <p className="text-muted-foreground mb-6">
              All assessments are conducted according to the AMA Guides to the Evaluation of Permanent Impairment (6th Edition), ensuring standardized and legally recognised evaluations.
            </p>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Standardized impairment rating methodology</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Functional capacity evaluation</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Work-related capacity assessment</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Activities of daily living evaluation</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Future treatment recommendations</span>
              </li>
            </ul>
          </div>
          <div className="relative rounded-xl overflow-hidden shadow-lg">
            <SafeImage
              src="/images/medical-consulting.jpg"
              alt="AMA Guidelines assessment process"
              className="w-full h-auto"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="order-2 lg:order-1 relative rounded-xl overflow-hidden shadow-lg">
            <SafeImage
              src="/images/examination-neurosurgery-report-spine-brain-insurance-claim-medicolegal.jpg"
              alt="Work capacity assessment"
              className="w-full h-auto"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>
          <div className="order-1 lg:order-2">
            <h3 className="text-enhanced-heading text-2xl font-bold mb-4 text-primary">Work Capacity Evaluation</h3>
            <p className="text-muted-foreground mb-6">
              Detailed evaluation of an individual's capacity to perform work-related activities, considering physical, cognitive, and psychological factors.
            </p>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Physical capacity for work tasks</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Cognitive function assessment</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Psychological impact evaluation</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Environmental factor considerations</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Workplace accommodation needs</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Return to work timeline</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Vocational rehabilitation requirements</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

CertificationSection.displayName = 'CertificationSection';

export default CertificationSection;
