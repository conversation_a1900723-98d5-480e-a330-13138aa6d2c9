import { useCallback, useState } from 'react';

import { validateArray, validateString, ValidationResult } from '@/lib/content-validation';
import { logWarning } from '@/lib/dev-console';

export type ContentState = 'idle' | 'loading' | 'success' | 'error' | 'empty';

export interface ContentStateOptions<T> {
  initialData?: T;
  validator?: (data: T) => ValidationResult<T>;
  retryAttempts?: number;
  retryDelay?: number;
  onError?: (error: Error) => void;
  onSuccess?: (data: T) => void;
  onEmpty?: () => void;
}

export interface UseContentStateReturn<T> {
  data: T | null;
  state: ContentState;
  error: Error | null;
  isLoading: boolean;
  isEmpty: boolean;
  hasError: boolean;
  retry: () => void;
  setData: (data: T) => void;
  reset: () => void;
  load: (loadFn: () => Promise<T>) => Promise<void>;
}

/**
 * Hook for managing content state with validation and error handling
 */
export function useContentState<T>(
  options: ContentStateOptions<T> = {}
): UseContentStateReturn<T> {
  const {
    initialData = null,
    validator,
    retryAttempts = 3,
    retryDelay = 1000,
    onError,
    onSuccess,
    onEmpty
  } = options;

  const [data, setDataState] = useState<T | null>(initialData);
  const [state, setState] = useState<ContentState>('idle');
  const [error, setError] = useState<Error | null>(null);
  const [currentLoadFn, setCurrentLoadFn] = useState<(() => Promise<T>) | null>(null);
  const [attemptCount, setAttemptCount] = useState(0);

  // Derived states
  const isLoading = state === 'loading';
  const isEmpty = state === 'empty';
  const hasError = state === 'error';

  // Validate and set data
  const setData = useCallback((newData: T) => {
    if (validator) {
      const validation = validator(newData);
      if (!validation.isValid) {
        setError(new Error(validation.error || 'Data validation failed'));
        setState('error');
        return;
      }
      setDataState(validation.data);
    } else {
      setDataState(newData);
    }

    // Check if data is empty
    if (newData === null || newData === undefined) {
      setState('empty');
      onEmpty?.();
    } else if (Array.isArray(newData) && newData.length === 0) {
      setState('empty');
      onEmpty?.();
    } else if (typeof newData === 'string' && newData.trim().length === 0) {
      setState('empty');
      onEmpty?.();
    } else if (typeof newData === 'object' && Object.keys(newData as Record<string, unknown>).length === 0) {
      setState('empty');
      onEmpty?.();
    } else {
      setState('success');
      onSuccess?.(newData);
    }

    setError(null);
    setAttemptCount(0);
  }, [validator, onSuccess, onEmpty]);

  // Load data with retry logic
  const load = useCallback(async (loadFn: () => Promise<T>) => {
    setCurrentLoadFn(() => loadFn);
    setState('loading');
    setError(null);

    try {
      const result = await loadFn();
      setData(result);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred');
      setError(error);
      setState('error');
      onError?.(error);
    }
  }, [setData, onError]);

  // Retry function
  const retry = useCallback(async () => {
    if (!currentLoadFn) {
      logWarning('No load function available for retry');
      return;
    }

    if (attemptCount >= retryAttempts) {
      logWarning(`Maximum retry attempts (${retryAttempts}) reached`);
      return;
    }

    setAttemptCount(prev => prev + 1);

    // Add delay for retries with cancellation support
    if (attemptCount > 0) {
      await new Promise<void>((resolve, reject) => {
        const timeoutId = setTimeout(resolve, retryDelay * attemptCount);

        // Store timeout for potential cleanup
        const cleanup = () => {
          clearTimeout(timeoutId);
          reject(new Error('Retry cancelled'));
        };

        // This would be cleaned up if the component unmounts
        return cleanup;
      });
    }

    await load(currentLoadFn);
  }, [currentLoadFn, attemptCount, retryAttempts, retryDelay, load]);

  // Reset function
  const reset = useCallback(() => {
    setDataState(initialData);
    setState('idle');
    setError(null);
    setCurrentLoadFn(null);
    setAttemptCount(0);
  }, [initialData]);

  return {
    data,
    state,
    error,
    isLoading,
    isEmpty,
    hasError,
    retry,
    setData,
    reset,
    load
  };
}

/**
 * Specialised hook for array content
 */
export function useArrayContentState<T>(
  options: ContentStateOptions<T[]> & {
    minLength?: number;
    maxLength?: number;
    itemValidator?: (item: T) => boolean;
  } = {}
): UseContentStateReturn<T[]> {
  const { minLength = 0, maxLength, itemValidator, ...baseOptions } = options;

  const validator = useCallback((data: T[]) => {
    return validateArray(data, {
      minLength,
      maxLength,
      itemValidator,
      fallback: []
    });
  }, [minLength, maxLength, itemValidator]);

  return useContentState<T[]>({
    ...baseOptions,
    validator
  });
}

/**
 * Specialised hook for string content
 */
export function useStringContentState(
  options: ContentStateOptions<string> & {
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    allowEmpty?: boolean;
    trim?: boolean;
  } = {}
): UseContentStateReturn<string> {
  const { minLength = 0, maxLength, pattern, allowEmpty = false, trim = true, ...baseOptions } = options;

  const validator = useCallback((data: string) => {
    return validateString(data, {
      minLength,
      maxLength,
      pattern,
      allowEmpty,
      trim,
      fallback: ''
    });
  }, [minLength, maxLength, pattern, allowEmpty, trim]);

  return useContentState<string>({
    ...baseOptions,
    validator
  });
}

/**
 * Hook for managing search results with empty states
 */
export function useSearchContentState<T>(
  searchFn: (query: string) => Promise<T[]>,
  options: ContentStateOptions<T[]> = {}
): UseContentStateReturn<T[]> & {
  search: (query: string) => Promise<void>;
  query: string;
  hasSearched: boolean;
} {
  const [query, setQuery] = useState('');
  const [hasSearched, setHasSearched] = useState(false);

  const contentState = useArrayContentState<T>(options);

  const search = useCallback(async (searchQuery: string) => {
    setQuery(searchQuery);
    setHasSearched(true);

    if (searchQuery.trim().length === 0) {
      contentState.setData([]);
      return;
    }

    await contentState.load(() => searchFn(searchQuery));
  }, [searchFn, contentState]);

  return {
    ...contentState,
    search,
    query,
    hasSearched
  };
}

/**
 * Hook for managing paginated content
 */
export function usePaginatedContentState<T>(
  loadFn: (page: number, limit: number) => Promise<{ items: T[]; total: number; hasMore: boolean }>,
  options: ContentStateOptions<T[]> & {
    initialPage?: number;
    pageSize?: number;
  } = {}
): UseContentStateReturn<T[]> & {
  page: number;
  total: number;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  loadPage: (page: number) => Promise<void>;
  refresh: () => Promise<void>;
} {
  const { initialPage = 1, pageSize = 10, ...baseOptions } = options;

  const [page, setPage] = useState(initialPage);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [allItems, setAllItems] = useState<T[]>([]);

  const contentState = useArrayContentState<T>(baseOptions);

  const loadPage = useCallback(async (targetPage: number) => {
    setPage(targetPage);

    await contentState.load(async () => {
      const result = await loadFn(targetPage, pageSize);
      setTotal(result.total);
      setHasMore(result.hasMore);

      if (targetPage === 1) {
        setAllItems(result.items);
      } else {
        setAllItems(prev => [...prev, ...result.items]);
      }

      return result.items;
    });
  }, [loadFn, pageSize, contentState]);

  const loadMore = useCallback(async () => {
    if (hasMore && !contentState.isLoading) {
      await loadPage(page + 1);
    }
  }, [hasMore, contentState.isLoading, page, loadPage]);

  const refresh = useCallback(async () => {
    setAllItems([]);
    await loadPage(1);
  }, [loadPage]);

  return {
    ...contentState,
    data: allItems,
    page,
    total,
    hasMore,
    loadMore,
    loadPage,
    refresh
  };
}