import { Slot } from '@radix-ui/react-slot';
import { type VariantProps } from 'class-variance-authority';
import React from 'react';

import { buttonVariants } from '@/lib/button-variants';
import { cn } from '@/lib/utils';

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  /** Accessible label for the button when the visual text is not descriptive enough */
  accessibleLabel?: string;
}
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(({ className, variant, size, asChild = false, accessibleLabel, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"

    // Enhance accessibility
    const ariaProps: Record<string, string> = {};

    // If an accessible label is provided, use it for screen readers
    if (accessibleLabel) {
      ariaProps['aria-label'] = accessibleLabel;
    }
    // Remove custom aria-disabled-reason, since it's not standard and avoids TS7053
    // If needed, introduce a custom prop pattern with correct typing in the future

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...ariaProps}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants }