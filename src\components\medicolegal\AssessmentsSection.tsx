import React from 'react';

import SafeImage from '@/components/SafeImage';

interface AssessmentsSectionProps {
  translations: {
    assessmentsReports?: {
      title?: string;
      description?: string;
    };
    independentExaminations?: {
      title?: string;
      description?: string;
    };
    impairmentAssessment?: {
      title?: string;
      description?: string;
      workCapacity?: {
        title?: string;
        description?: string;
      };
    };
  };
}

const AssessmentsSection: React.FC<AssessmentsSectionProps> = ({ translations }) => {
  return (
    <section className="py-16">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{translations.assessmentsReports?.title || 'Assessments and Reports'}</h2>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            {translations.assessmentsReports?.description || 'Expert medicolegal assessments for brain, spine, and nerve injuries.'}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Independent Medical Examinations */}
          <div className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">
              {translations.independentExaminations?.title || 'Independent and Joint Medical Examinations'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {translations.independentExaminations?.description || 'Comprehensive independent medical examinations for legal and insurance purposes.'}
            </p>
            <SafeImage
              src="/images/gp-resources/care-coordination-1.jpg"
              alt="Independent Medical Examination"
              className="w-full h-40 object-cover rounded-md mb-4"
            />
          </div>

          {/* Whole Person Impairment Assessment */}
          <div className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">
              {translations.impairmentAssessment?.title || 'Whole Person Impairment Assessment'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {translations.impairmentAssessment?.description || 'Assessment of impairment according to AMA Guides to Permanent Impairment.'}
            </p>
            <SafeImage
              src="/images/law-AMA-4th-edition-medicolegal-miNEURO-work-injury-accident-traffic-TAC-WorkCover-impairment-assessment.jpg"
              alt="Whole Person Impairment Assessment"
              className="w-full h-40 object-cover rounded-md mb-4"
            />
          </div>

          {/* Work Capacity Assessments */}
          <div className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">
              {translations.impairmentAssessment?.workCapacity?.title || 'Work Capacity and Disability Assessments'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {translations.impairmentAssessment?.workCapacity?.description || 'Comprehensive evaluation of work capacity and disability status.'}
            </p>
            <SafeImage
              src="/images/employment-worker-medicolegal-miNEURO-work-injury-accident-motor-vehicle-traffic-TAC-WorkCover.jpg"
              alt="Work Capacity Assessment"
              className="w-full h-40 object-cover rounded-md mb-4"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

AssessmentsSection.displayName = 'AssessmentsSection';

export default AssessmentsSection;
