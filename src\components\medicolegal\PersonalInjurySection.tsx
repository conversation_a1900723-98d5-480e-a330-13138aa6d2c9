import React from 'react';

import SafeImage from '@/components/SafeImage';

const PersonalInjurySection: React.FC = () => {
  return (
    <section className="py-16">
      <div className="container">
        {/* Help to Recover from Injury Section */}
        <div className="mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="relative rounded-xl overflow-hidden shadow-lg">
              <SafeImage
                src="/images/motor-vehicle-traffic-road-accident-injury-medicolegal-miNEURO-Ales-<PERSON>vich.jpg"
                alt="Personal injury assistance"
                className="w-full h-auto"
                fallbackSrc="/images/medical-consulting.jpg"
              />
            </div>
            <div>
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4">Personal Injury Assistance</h2>
              <h3 className="text-2xl font-semibold mb-6 text-primary">Help to Recover from Injury</h3>
              <p className="text-muted-foreground mb-6">
                Our reports are prepared in accordance with the Expert Witness Code of Conduct and the guidelines 
                specified by AMA (American Medical Association) TAC (Transport Accident Commission) and Worksafe. 
                They can be used for a variety of legal purposes:
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Assessment of injuries (e.g. related to work, transport accidents, public liability or medical negligence)</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Assisting with insurance and compensation claims (e.g. WorkSafe, TAC, third party claims, medical indemnity or income protection)</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Impairment assessments</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Estimation of work capacity</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Flexible Arrangements Section */}
        <div className="medical-card p-8 rounded-xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-primary">Flexible Arrangements</h3>
              <p className="text-muted-foreground mb-6">
                Dr Ales Aliashkevich can assist in meeting your requirements timely and in a cost-effective manner. 
                Our fees are based on the volume of the provided materials, the complexity of the injuries and urgency 
                of the request. We can provide the cost estimate in advance, in particular for complex and time-consuming assessments.
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Cost estimates provided in advance</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Fees based on complexity and urgency</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Network of locations for convenience</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Weekend appointments for urgent cases</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Short-notice deadline responses</span>
                </li>
              </ul>
              
              <div className="bg-primary/10 p-4 rounded-lg">
                <h4 className="font-semibold text-primary mb-2">Contact Information:</h4>
                <p className="text-muted-foreground text-sm mb-2">
                  If you have any questions about our medicolegal services:
                </p>
                <div className="space-y-1 text-sm">
                  <p className="text-muted-foreground">
                    <span className="font-medium">Phone:</span> 03 9008 4200
                  </p>
                  <p className="text-muted-foreground">
                    <span className="font-medium">Email:</span> <EMAIL>
                  </p>
                </div>
              </div>
            </div>
            <div className="relative rounded-xl overflow-hidden shadow-lg">
              <SafeImage
                src="/images/welcoming-environment-reception-door.jpg"
                alt="Flexible appointment arrangements"
                className="w-full h-auto"
                fallbackSrc="/images/medical-consulting.jpg"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

PersonalInjurySection.displayName = 'PersonalInjurySection';

export default PersonalInjurySection;
