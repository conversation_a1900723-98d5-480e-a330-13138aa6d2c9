import React from 'react';

import { Button } from '@/components/ui/button';

interface Hospital {
  title: string;
  description: string;
  address: string;
  phone: string;
  website: string;
  websiteText: string;
  image: {
    src: string;
    alt: string;
  };
}

interface NearbyHospitalsProps {
  title: string;
  subtitle: string;
  description: string;
  hospitals: Hospital[];
}

/**
 * Nearby Hospitals Component
 * Displays hospitals where Dr<PERSON> performs surgery
 * Preserves all original hospital content from Surrey Hills location page
 */
const NearbyHospitals: React.FC<NearbyHospitalsProps> = ({
  title,
  subtitle,
  description,
  hospitals
}) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-muted-foreground">
            {subtitle}
          </p>
        </div>

        <div className="mt-8 max-w-3xl mx-auto mb-12">
          <p className="text-muted-foreground text-center">
            {description}
          </p>
        </div>

        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-8">
          {hospitals.map((hospital, index) => (
            <div key={index} className="card p-6 rounded-lg shadow-md medical-card">
              <div className="relative h-48 rounded-lg overflow-hidden shadow-md mb-6">
                <img
                  src={hospital.image.src}
                  alt={hospital.image.alt}
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                />
              </div>

              <h3 className="text-xl font-semibold mb-3 text-primary">{hospital.title}</h3>
              <p className="text-muted-foreground mb-4">
                {hospital.description}
              </p>
              <p className="text-muted-foreground mb-4">
                {hospital.address}<br />
                {hospital.phone}
              </p>
              <Button asChild variant="outline" className="w-full">
                <a href={hospital.website} target="_blank" rel="noopener noreferrer">
                  {hospital.websiteText}
                </a>
              </Button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

NearbyHospitals.displayName = 'NearbyHospitals';

export default NearbyHospitals;
