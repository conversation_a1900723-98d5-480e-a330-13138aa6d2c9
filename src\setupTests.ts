import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import { toHaveNoViolations } from 'jest-axe';
import React from 'react';
import { afterAll, afterEach, beforeAll, expect, vi } from 'vitest';

import { setupTestEnvironment } from '@/lib/test-utils';

// Global context mocks - these need to be set up before any components are imported
vi.mock('@/contexts/DeviceContext', () => ({
  useDeviceDetection: vi.fn(() => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isTouchDevice: false,
    hasHover: true,
    orientation: 'landscape',
    screenSize: 'lg',
    width: 1024,
    height: 768
  })),
  useDeviceLoaded: vi.fn(() => true),
  useIsMobile: vi.fn(() => false),
  useBreakpoint: vi.fn(() => 'desktop'),
  withDeviceDetection: vi.fn((Component) => Component),
  DeviceProvider: ({ children }) => children
}));

vi.mock('@/contexts/LanguageContext', () => ({
  useLanguage: vi.fn(() => {
    const translations = {
      spine_conditions_library: 'Spine Conditions Library',
      navigation: { home: "Home", expertise: "Expertise", contact: "Contact", about: "About", locations: "Locations", patientResources: "Patient Resources", bookAppointment: "Book Appointment", language: "Language", menu: "Menu", close: "Close", skipToContent: "Skip to Content" },
      hero: {
        subtitle: "THE GOLD STANDARD FOR BRAIN AND SPINE SURGERY",
        title: "Neurosurgical Expertise and Innovative Technology for Superior Brain and Spine Surgery Results",
        description: "Our practice specialises in cutting-edge future-minded treatment of conditions affecting the brain, spine, and peripheral nerves. We utilise safe minimally-invasive technologies and innovations dedicated to preserving, restoring, and improving the patient's lifestyle.",
        credentials: "Dr Ales Aliashkevich is a progressive and conservative neurosurgeon committed to providing the highest level of care and expertise in a dedicated, personalised way.",
        technology: "Dr Aliashkevich is qualified to use a variety of state-of-the-art robotic and image-guided navigation systems, motion-preserving spine surgery, and minimally invasive approaches.",
        bookConsultation: "Book Consultation",
        exploreTreatments: "Explore Treatment Options",
        scrollDown: "Scroll Down"
      },
      common: { loading: "Loading", error: "Error", success: "Success", cancel: "Cancel", save: "Save", submit: "Submit", close: "Close", back: "Back", next: "Next", previous: "Previous", yes: "Yes", no: "No" },
      testimonials: {
        title: "What Our Patients Say",
        description: "Dr. Aliashkevich has received excellent feedback across multiple independent review platforms. See what patients are saying about their experiences.",
        readMore: "Read More"
      }
    };

    // Create a function that also has properties (hybrid approach)
    const tFunction = vi.fn((key) => {
      // Handle nested keys like 'testimonials.description'
      if (key.includes('.')) {
        const keys = key.split('.');
        let result = translations;
        for (const k of keys) {
          result = result?.[k];
          if (result === undefined) break;
        }
        return result || key;
      }
      return translations[key] || key;
    });

    // Add properties to the function so it can also be used as an object
    Object.assign(tFunction, translations);

    return {
      language: 'en',
      t: tFunction,
      isLanguageLoaded: true
    };
  }),
  LanguageProvider: ({ children }) => children
}));

// Mock SafeImage component to prevent image loading issues in tests
vi.mock('@/components/SafeImage', () => ({
  default: ({ alt, ...props }: React.ImgHTMLAttributes<HTMLImageElement> & { alt: string }) =>
    React.createElement('img', { alt, 'data-testid': 'safe-image', ...props })
}));

// Extend Vitest's expect with jest-dom and jest-axe matchers
expect.extend(toHaveNoViolations);

// Setup test environment with all mocks and utilities
setupTestEnvironment();

// Cleanup after each test case (e.g. clearing jsdom)
afterEach(() => {
  cleanup();
});

// Global test setup
beforeAll(() => {
  // Console methods are handled by test-utils for specific tests
  // No global console suppression to avoid conflicts

  // Suppress React act() warnings for third-party components
   
  const originalWarn = console.warn;
   
  console.warn = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: An update to') &&
      args[0].includes('was not wrapped in act')
    ) {
      return;
    }
    originalWarn.call(console, ...args);
  };
});

afterAll(() => {
  // Cleanup any global mocks or resources
  vi.restoreAllMocks();
});

// Mock environment variables for tests
Object.defineProperty(process, 'env', {
  value: {
    ...process.env,
    NODE_ENV: 'test',
    VITE_APP_TITLE: 'miNEURO Test',
    VITE_APP_DESCRIPTION: 'Test Environment',
  },
});

// Restore proper global mocks to prevent conflicts
// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: (query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => { },
    removeListener: () => { },
    addEventListener: () => { },
    removeEventListener: () => { },
    dispatchEvent: () => { },
  }),
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() { }
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() { }
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  value: vi.fn(),
  writable: true,
});

// Mock performance API with mockable functions
Object.defineProperty(global, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => []),
    clearMarks: vi.fn(),
    clearMeasures: vi.fn(),
  },
  writable: true,
});

// Mock URL constructor for tests
global.URL = class URL {
  constructor(url: string) {
    this.href = url;
    this.origin = 'http://localhost:3000';
    this.pathname = '/';
    this.search = '';
    this.hash = '';
  }
  href: string;
  origin: string;
  pathname: string;
  search: string;
  hash: string;
};
