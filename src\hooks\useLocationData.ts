import { useEffect } from 'react';

import { useLanguage } from '@/contexts/LanguageContext';
import en from '@/locales/en';

interface LocationData {
  hero: {
    title: string;
    subtitle: string;
    introduction1: string;
    introduction2?: string;
    introduction3?: string;
  };
  contact: {
    address: {
      street: string;
      suburb: string;
      state: string;
      postcode: string;
    };
    phone: string;
    email: string;
    hours: {
      weekdays: string;
      note?: string;
    };
  };
  map: {
    embedUrl: string;
    transportOptions: {
      publicTransport: string;
      car: string;
    };
  };
  facilities?: {
    title: string;
    subtitle: string;
    description: string;
    items: Array<{
      title: string;
      description: string;
    }>;
  };
  cta: {
    title: string;
    description: string;
    buttons: Array<{
      text: string;
      link: string;
      variant?: 'default' | 'outline';
    }>;
  };
}

export const useLocationData = (locationKey: string): LocationData => {
  const { t } = useLanguage();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Safe fallback for translations
  const safeT = t || en;
  const locationData = safeT?.[locationKey as keyof typeof safeT] as Record<string, unknown> | undefined;

  // Default fallback data
  const defaultData: LocationData = {
    hero: {
      title: 'Medical Practice Location',
      subtitle: 'Professional Healthcare Services',
      introduction1: 'Expert medical care in a convenient location.',
      introduction2: 'Our modern facilities provide comprehensive healthcare services.',
    },
    contact: {
      address: {
        street: 'Medical Centre',
        suburb: 'Melbourne',
        state: 'VIC',
        postcode: '3000'
      },
      phone: '03 9000 0000',
      email: '<EMAIL>',
      hours: {
        weekdays: 'Monday to Friday: 8:30 AM - 5:30 PM',
        note: 'Consultations by appointment only'
      }
    },
    map: {
      embedUrl: '',
      transportOptions: {
        publicTransport: 'Accessible by public transport.',
        car: 'Parking available on-site.'
      }
    },
    cta: {
      title: 'Ready to Schedule an Appointment?',
      description: 'Contact us today to schedule a consultation.',
      buttons: [
        { text: 'Book an Appointment', link: '/appointments' },
        { text: 'View All Locations', link: '/locations', variant: 'outline' },
        { text: 'Contact Us', link: '/contact', variant: 'outline' }
      ]
    }
  };

  // Merge location-specific data with defaults
  return {
    hero: {
      title: locationData?.expertNeurosurgery || locationData?.title || defaultData.hero.title,
      subtitle: locationData?.subtitle || defaultData.hero.subtitle,
      introduction1: locationData?.introduction1 || defaultData.hero.introduction1,
      introduction2: locationData?.introduction2 || defaultData.hero.introduction2,
      introduction3: locationData?.introduction3,
    },
    contact: {
      address: locationData?.address || defaultData.contact.address,
      phone: locationData?.phone || defaultData.contact.phone,
      email: locationData?.email || defaultData.contact.email,
      hours: locationData?.hours || defaultData.contact.hours,
    },
    map: {
      embedUrl: locationData?.mapEmbedUrl || defaultData.map.embedUrl,
      transportOptions: {
        publicTransport: locationData?.byPublicTransportDetails || defaultData.map.transportOptions.publicTransport,
        car: locationData?.byCarDetails || defaultData.map.transportOptions.car,
      }
    },
    facilities: locationData?.facilities ? {
      title: locationData.facilities.title || 'Our Facilities',
      subtitle: locationData.facilities.subtitle || 'Modern healthcare facilities',
      description: locationData.facilities.description || 'State-of-the-art medical facilities.',
      items: locationData.facilities.items || []
    } : undefined,
    cta: {
      title: locationData?.readyToSchedule?.title || defaultData.cta.title,
      description: locationData?.readyToSchedule?.description || defaultData.cta.description,
      buttons: [
        { 
          text: locationData?.readyToSchedule?.bookAppointment || 'Book an Appointment', 
          link: '/appointments' 
        },
        { 
          text: locationData?.readyToSchedule?.viewAllLocations || 'View All Locations', 
          link: '/locations', 
          variant: 'outline' as const
        },
        { 
          text: locationData?.readyToSchedule?.contactUs || 'Contact Us', 
          link: '/contact', 
          variant: 'outline' as const
        }
      ]
    }
  };
};

export default useLocationData;
