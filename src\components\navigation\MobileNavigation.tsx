import { Menu, X } from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import ThemeToggle from '@/components/ThemeToggle';
import { Button } from '@/components/ui/button';
import { NavLink } from '@/data/navigation/navigationData';
import { cn } from '@/lib/utils';

interface MobileNavigationProps {
  navLinks: NavLink[];
  ctaButton: { text: string; path: string };
  mobileMenuOpen: boolean;
  activeSubmenu: string | null;
  onToggleMobileMenu: () => void;
  onCloseMobileMenu: () => void;
  onMobileClick: (linkName: string, hasSubmenu: boolean) => (e: React.MouseEvent) => void;
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({
  navLinks,
  ctaButton,
  mobileMenuOpen,
  activeSubmenu,
  onToggleMobileMenu,
  onCloseMobileMenu,
  onMobileClick
}) => {
  return (
    <nav className="lg:hidden" aria-label="Mobile navigation controls">
      {/* Mobile Navigation Toggle */}
      <div className="flex items-center space-x-3">
        <ThemeToggle />
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggleMobileMenu}
          className="mobile-button touch-feedback rounded-lg p-3"
          aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
          aria-expanded={mobileMenuOpen}
        >
          {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </Button>
      </div>

      {/* Mobile Menu */}
      <div
        className={cn("fixed inset-0 z-40 bg-background/80 backdrop-blur-sm transition-opacity duration-300",
        mobileMenuOpen ? "opacity-100" : "opacity-0 pointer-events-none")}
        role="dialog"
        aria-modal="true"
        aria-label="Mobile navigation menu"
        id="mobile-menu"
      >
        <div
          className={cn("fixed inset-y-0 right-0 w-3/4 max-w-sm bg-card shadow-xl p-6 transition-transform duration-300 ease-in-out",
          mobileMenuOpen ? "translate-x-0" : "translate-x-full")}
        >
          <div className="flex flex-col h-full justify-between">
            <div>
              <div className="flex justify-between items-center mb-8">
                <div className="flex items-center">
                  <Link to="/" onClick={onCloseMobileMenu} className="flex items-center">
                    <SafeImage
                      src="/logo/logo.png"
                      alt="miNEURO Logo"
                      className="h-10 w-auto"
                      fallbackSrc="/logo/logo-white.png"
                    />
                  </Link>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onCloseMobileMenu}
                  className="rounded-full ml-2"
                  aria-label="Close menu"
                >
                  <X className="h-6 w-6" aria-hidden="true" />
                </Button>
              </div>
              <nav aria-label="Mobile navigation menu">
                <ul className="space-y-6" role="menu">
                  {navLinks?.map(link => (
                    <li key={link.name} role="none">
                      <Link
                        to={link.path}
                        className="text-lg font-medium transition-colors hover:text-primary block"
                        onClick={onMobileClick(link.name, !!link.submenu)}
                        aria-expanded={link.submenu ? activeSubmenu === link.name : undefined}
                        aria-haspopup={link.submenu ? 'true' : undefined}
                        role="menuitem"
                      >
                        {link.name}
                        {link.submenu && <span className="ml-1 inline-block float-right">▾</span>}
                      </Link>

                      {/* Mobile submenu */}
                      {link.submenu && (
                        <div
                          className={`pl-6 mt-2 space-y-2 ${activeSubmenu === link.name ? 'block' : 'hidden'}`}
                          role="menu"
                          aria-label={`${link.name} submenu`}
                        >
                          {link.submenu?.map((subItem) => (
                            <Link
                              key={subItem.name}
                              to={subItem.path}
                              className="block text-sm text-muted-foreground hover:text-primary"
                              onClick={onCloseMobileMenu}
                              role="menuitem"
                            >
                              {subItem.name}
                            </Link>
                          ))}
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              </nav>
            </div>

            <Button asChild className="w-full btn-primary mt-6">
              <Link to={ctaButton.path} onClick={onCloseMobileMenu}>
                {ctaButton.text}
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
};

MobileNavigation.displayName = 'MobileNavigation';

export default MobileNavigation;
