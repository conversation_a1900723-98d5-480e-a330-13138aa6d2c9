/**
 * Enhanced Error Utilities
 * Provides additional error handling patterns to complement existing error-handling.ts
 * Focuses on common patterns found throughout the codebase
 */

import { logger } from './logger';

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  timestamp?: string;
  retryCount?: number;
  [key: string]: unknown;
}

/**
 * Simple application error class for common use cases
 */
export class SimpleAppError extends Error {
  code?: string;
  context?: ErrorContext;
  recoverable: boolean;

  constructor(
    message: string,
    code?: string,
    recoverable: boolean = true,
    context?: ErrorContext
  ) {
    super(message);
    this.name = 'SimpleAppError';
    this.code = code;
    this.recoverable = recoverable;
    this.context = {
      ...context,
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * Network-specific error for API calls
 */
export class NetworkError extends SimpleAppError {
  status?: number;
  endpoint?: string;

  constructor(
    message: string,
    status?: number,
    endpoint?: string,
    context?: ErrorContext
  ) {
    super(message, 'NETWORK_ERROR', true, context);
    this.name = 'NetworkError';
    this.status = status;
    this.endpoint = endpoint;
  }
}

/**
 * Component rendering error
 */
export class ComponentError extends SimpleAppError {
  componentName: string;

  constructor(
    message: string,
    componentName: string,
    context?: ErrorContext
  ) {
    super(message, 'COMPONENT_ERROR', true, context);
    this.name = 'ComponentError';
    this.componentName = componentName;
  }
}

/**
 * Type guards
 */
export const isSimpleAppError = (error: unknown): error is SimpleAppError => {
  return error instanceof SimpleAppError;
};

export const isNetworkError = (error: unknown): error is NetworkError => {
  return error instanceof NetworkError;
};

export const isComponentError = (error: unknown): error is ComponentError => {
  return error instanceof ComponentError;
};

/**
 * Handle async operations with consistent error handling
 * Replaces try-catch patterns found throughout the codebase
 */
export const safeAsync = async <T>(
  asyncFn: () => Promise<T>,
  context?: ErrorContext
): Promise<{ data?: T; error?: SimpleAppError }> => {
  try {
    const data = await asyncFn();
    return { data };
  } catch (error) {
    const appError = isSimpleAppError(error)
      ? error
      : new SimpleAppError(
          error instanceof Error ? error.message : 'Unknown error',
          'ASYNC_ERROR',
          true,
          context
        );
    
    logger.error('Async operation failed', context, appError);
    return { error: appError };
  }
};

/**
 * Safe function execution with error handling
 */
export const safeExecute = <T extends (...args: any[]) => any>(
  fn: T,
  context?: ErrorContext
): (...args: Parameters<T>) => ReturnType<T> | SimpleAppError => {
  return (...args: Parameters<T>) => {
    try {
      return fn(...args);
    } catch (error) {
      const appError = isSimpleAppError(error)
        ? error
        : new SimpleAppError(
            error instanceof Error ? error.message : 'Unknown error',
            'FUNCTION_ERROR',
            true,
            context
          );
      
      logger.error('Function execution failed', context, appError);
      return appError;
    }
  };
};

/**
 * Retry mechanism for failed operations
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  context?: ErrorContext
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      
      logger.warn(`Operation failed, attempt ${attempt}/${maxRetries}`, {
        ...context,
        attempt,
        maxRetries,
        error: lastError.message,
      });
      
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
  }
  
  throw new SimpleAppError(
    `Operation failed after ${maxRetries} attempts: ${lastError!.message}`,
    'RETRY_EXHAUSTED',
    false,
    { ...context, maxRetries, finalError: lastError!.message }
  );
};

/**
 * Error boundary helper for React components
 */
export const createErrorBoundaryHandler = (componentName: string) => {
  return (error: Error, errorInfo: React.ErrorInfo) => {
    logger.componentError(componentName, error, {
      componentStack: errorInfo.componentStack,
      errorBoundary: true,
    });
  };
};

/**
 * Common error patterns found in the codebase
 */
export const ErrorPatterns = {
  /**
   * Handle timeout cleanup errors
   */
  timeoutCleanup: (error: Error, context?: ErrorContext) => {
    logger.warn('Timeout cleanup failed', context, error);
    // Don't throw - cleanup failures shouldn't break the app
  },

  /**
   * Handle animation errors
   */
  animationError: (error: Error, context?: ErrorContext) => {
    logger.warn('Animation error', context, error);
    // Animations are non-critical
  },

  /**
   * Handle translation fallback errors
   */
  translationError: (path: string, error: Error, context?: ErrorContext) => {
    logger.warn(`Translation fallback failed for path: ${path}`, context, error);
    return ''; // Return empty string as ultimate fallback
  },

  /**
   * Handle image loading errors
   */
  imageLoadError: (src: string, error: Error, context?: ErrorContext) => {
    logger.warn(`Image failed to load: ${src}`, context, error);
    // Return fallback image path or empty string
    return '/images/placeholder.jpg';
  },

  /**
   * Handle navigation errors
   */
  navigationError: (to: string, error: Error, context?: ErrorContext) => {
    logger.error(`Navigation failed to: ${to}`, context, error);
    // Could trigger fallback navigation or show error message
  },
};

/**
 * Error recovery strategies
 */
export const ErrorRecovery = {
  /**
   * Reload page as last resort
   */
  reloadPage: (reason: string) => {
    logger.error(`Page reload triggered: ${reason}`);
    window.location.reload();
  },

  /**
   * Navigate to safe page
   */
  navigateToSafe: (safePath: string = '/') => {
    logger.warn(`Navigating to safe path: ${safePath}`);
    window.location.href = safePath;
  },

  /**
   * Clear local storage and reload
   */
  clearAndReload: (reason: string) => {
    logger.error(`Clearing storage and reloading: ${reason}`);
    try {
      localStorage.clear();
      sessionStorage.clear();
    } catch {
      // Storage might not be available
    }
    window.location.reload();
  },
};

/**
 * Development-only error helpers
 */
export const DevErrorHelpers = {
  /**
   * Log error details in development
   */
  logErrorDetails: (error: Error, context?: ErrorContext) => {
    if (import.meta.env.DEV) {
      console.group('🚨 Error Details');
      console.error('Error:', error);
      console.log('Context:', context);
      console.log('Stack:', error.stack);
      console.groupEnd();
    }
  },

  /**
   * Break on error in development
   */
  breakOnError: () => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-debugger
      debugger;
    }
  },
};

export default {
  SimpleAppError,
  NetworkError,
  ComponentError,
  safeAsync,
  safeExecute,
  withRetry,
  createErrorBoundaryHandler,
  ErrorPatterns,
  ErrorRecovery,
  DevErrorHelpers,
};
