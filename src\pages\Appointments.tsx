import React, { useEffect } from 'react';

import AppointmentInformation from '@/components/appointments/AppointmentInformation';
import AppointmentProcess from '@/components/appointments/AppointmentProcess';
import AppointmentRequestForm from '@/components/appointments/AppointmentRequestForm';
import AppointmentTypesSection from '@/components/appointments/AppointmentTypesSection';
import ContactSection from '@/components/appointments/ContactSection';
import FeesAndInsuranceSection from '@/components/appointments/FeesAndInsuranceSection';
import InvestigationsSection from '@/components/appointments/InvestigationsSection';
import LocationsSection from '@/components/appointments/LocationsSection';
import PreparationSection from '@/components/appointments/PreparationSection';
import PrivacySection from '@/components/appointments/PrivacySection';
import TelehealthSection from '@/components/appointments/TelehealthSection';
import TreatmentOptionsSection from '@/components/appointments/TreatmentOptionsSection';
import PageHeader from '@/components/PageHeader';
import StandardPageLayout from '@/components/StandardPageLayout';
import appointmentsData from '@/data/pages/appointments';
import { generatePageSEO } from '@/lib/seo';

const Appointments: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const appointmentsSeoData = generatePageSEO('appointments');

  return (
    <StandardPageLayout
      pageType="appointments"
      seoData={appointmentsSeoData}
      showHeader={false}
    >
      {/* Hero Section */}
      <PageHeader
        title={appointmentsData.hero.title}
        subtitle={appointmentsData.hero.subtitle}
        backgroundImage={appointmentsData.hero.backgroundImage}
        enableParallax={true}
      />

      <div className="flex-1">
        {/* Appointment Types Section */}
        <AppointmentTypesSection
          title={appointmentsData.appointmentTypes.title}
          subtitle={appointmentsData.appointmentTypes.subtitle}
          types={appointmentsData.appointmentTypes.types}
        />

        {/* Booking Process Overview */}
        <AppointmentProcess
          title={appointmentsData.process.title}
          subtitle={appointmentsData.process.subtitle}
          steps={appointmentsData.process.steps}
        />

        {/* Online Booking System & Appointment Information */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <AppointmentInformation
                title={appointmentsData.appointmentInfo.title}
                sections={appointmentsData.appointmentInfo.sections}
              />

              <AppointmentRequestForm
                title={appointmentsData.requestForm.title}
                fields={appointmentsData.requestForm.fields}
                submitText={appointmentsData.requestForm.submitText}
                confirmationText={appointmentsData.requestForm.confirmationText}
              />
            </div>
          </div>
        </section>

        {/* Investigations Section */}
        <InvestigationsSection
          title={appointmentsData.investigations.title}
          description={appointmentsData.investigations.description}
          items={appointmentsData.investigations.items}
          image={appointmentsData.investigations.image}
        />

        {/* Treatment Options Section */}
        <TreatmentOptionsSection
          title={appointmentsData.treatmentOptions.title}
          description={appointmentsData.treatmentOptions.description}
          image={appointmentsData.treatmentOptions.image}
        />

        {/* Fees and Insurance Section */}
        <FeesAndInsuranceSection
          feesData={appointmentsData.fees}
          insuranceData={appointmentsData.insurance}
        />

        {/* Telehealth Section */}
        <TelehealthSection
          title={appointmentsData.telehealth.title}
          subtitle={appointmentsData.telehealth.subtitle}
          description={appointmentsData.telehealth.description}
          options={appointmentsData.telehealth.options}
          suitableFor={appointmentsData.telehealth.suitableFor}
          requirements={appointmentsData.telehealth.requirements}
        />

        {/* Preparation Section */}
        <PreparationSection
          title={appointmentsData.preparation.title}
          subtitle={appointmentsData.preparation.subtitle}
          sections={appointmentsData.preparation.sections}
        />

        {/* Locations Section */}
        <LocationsSection
          title={appointmentsData.locations.title}
          subtitle1={appointmentsData.locations.subtitle[0]}
          subtitle2={appointmentsData.locations.subtitle[1]}
          mainLocation={{
            title: appointmentsData.locations.mainOffice.title,
            address: appointmentsData.locations.mainOffice.address,
            phone: appointmentsData.locations.mainOffice.phone,
            fax: appointmentsData.locations.mainOffice.fax,
            email: appointmentsData.locations.mainOffice.email,
            image: {
              src: "/images/neurosurgical-consultation-suite-Surrey-Hills-miNEURO-doctor-desk.jpg",
              alt: "Surrey Hills miNEURO Consulting Suites"
            }
          }}
          labels={{
            phone: "Phone",
            fax: "Fax",
            email: "Email",
            viewDetails: "View Details",
            allLocations: "All Locations",
            viewAllLocations: "View All Locations"
          }}
        />

        {/* Privacy Section */}
        <PrivacySection
          title={appointmentsData.privacy.title}
          subtitle={appointmentsData.privacy.subtitle}
          description={appointmentsData.privacy.description}
          principles={appointmentsData.privacy.principles}
        />

        {/* Contact Section */}
        <ContactSection
          title={appointmentsData.contact.title}
          subtitle={appointmentsData.contact.subtitle}
          methods={appointmentsData.contact.methods}
          emergencyNote={appointmentsData.contact.emergencyNote}
          responseTime={appointmentsData.contact.responseTime}
        />
      </div>
    </StandardPageLayout>
  );
};
Appointments.displayName = 'Appointments';

export default Appointments;
