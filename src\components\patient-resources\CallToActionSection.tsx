import React from 'react';
import { Link } from 'react-router-dom';

import { But<PERSON> } from '@/components/ui/button';

interface CallToActionButton {
  text: string;
  link: string;
}

interface CallToActionSectionProps {
  title: string;
  description: string;
  primaryButton: CallToActionButton;
  secondaryButton: CallToActionButton;
}

const CallToActionSection: React.FC<CallToActionSectionProps> = ({
  title,
  description,
  primaryButton,
  secondaryButton
}) => {
  return (
    <div className="bg-primary/10 p-6 rounded-lg my-8">
      <h3 className="text-xl font-bold mb-4">{title}</h3>
      <p>{description}</p>
      <div className="flex flex-col sm:flex-row gap-4 mt-6">
        <Button asChild>
          <Link to={primaryButton.link}>{primaryButton.text}</Link>
        </Button>
        <Button asChild variant="outline">
          <Link to={secondaryButton.link}>{secondaryButton.text}</Link>
        </Button>
      </div>
    </div>
  );
};

CallToActionSection.displayName = 'CallToActionSection';

export default CallToActionSection;
