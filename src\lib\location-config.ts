/**
 * Location Configuration System
 * Centralizes location-specific configurations and data injection
 * Provides a systematic way to manage location variations
 */

import { LocationSEOData, LocationSpecialSections } from '@/components/locations/GenericLocationTemplate';

export interface LocationConfig {
  id: string;
  name: string;
  slug: string;
  layoutVariant: 'standard' | 'split' | 'custom';
  seoData: LocationSEOData;
  specialSections?: LocationSpecialSections;
  hasTherapeuticInterventions: boolean;
  hasAmenities: boolean;
  hasConditionsTreated: boolean;
  hasInsuranceAndFunding: boolean;
  hasPatientsPrivacy: boolean;
  customSections?: string[]; // Array of custom section identifiers
}

/**
 * Location configurations for all consulting locations
 * Each location can have different sections and layout variants
 */
export const locationConfigs: Record<string, LocationConfig> = {
  sunbury: {
    id: 'sunbury',
    name: 'Sunbury',
    slug: 'sunbury',
    layoutVariant: 'standard',
    seoData: {
      title: 'Sunbury Consulting Location - Lake Imaging | miNEURO',
      description: 'Dr. <PERSON><PERSON> provides expert neurosurgical consultations at Lake Imaging in Sunbury. Convenient location with modern facilities and comprehensive care.',
      keywords: 'Sunbury neurosurgeon, Lake Imaging, spine surgeon <PERSON><PERSON>, neurosurgical consultation, Dr Ales <PERSON>ashkevich, <PERSON> Street medical',
      canonicalUrl: 'https://mineuro.com.au/locations/sunbury'
    },
    hasTherapeuticInterventions: true,
    hasAmenities: false,
    hasConditionsTreated: false,
    hasInsuranceAndFunding: false,
    hasPatientsPrivacy: true,
  },

  heidelberg: {
    id: 'heidelberg',
    name: 'Heidelberg',
    slug: 'heidelberg',
    layoutVariant: 'split',
    seoData: {
      title: 'Heidelberg Neurosurgeon | Warringal Private Hospital | Dr. Ales Aliashkevich',
      description: 'Expert neurosurgery and spine care at Warringal Private Hospital Heidelberg. Dr. Ales Aliashkevich provides comprehensive neurosurgical consultations and treatments. Book your appointment today.',
      keywords: 'neurosurgeon heidelberg, spine surgeon heidelberg, warringal private hospital, neurosurgery heidelberg, spine surgery heidelberg',
      canonicalUrl: 'https://mineuro.com.au/locations/heidelberg'
    },
    hasTherapeuticInterventions: true,
    hasAmenities: true,
    hasConditionsTreated: false,
    hasInsuranceAndFunding: false,
    hasPatientsPrivacy: true,
  },

  frankston: {
    id: 'frankston',
    name: 'Frankston',
    slug: 'frankston',
    layoutVariant: 'split',
    seoData: {
      title: 'Frankston Neurosurgeon | Peninsula Private Hospital | Dr Ales Aliashkevich',
      description: 'Expert neurosurgery and spine care at Peninsula Private Hospital, Frankston. Dr Ales Aliashkevich provides comprehensive neurosurgical consultations and treatments. Book your appointment today.',
      keywords: 'neurosurgeon frankston, spine surgeon frankston, peninsula private hospital, neurosurgery frankston, spine surgery frankston',
      canonicalUrl: 'https://mineuro.com.au/locations/frankston'
    },
    hasTherapeuticInterventions: true,
    hasAmenities: true,
    hasConditionsTreated: true,
    hasInsuranceAndFunding: true,
    hasPatientsPrivacy: false,
  },

  bundoora: {
    id: 'bundoora',
    name: 'Bundoora',
    slug: 'bundoora',
    layoutVariant: 'standard',
    seoData: {
      title: 'Bundoora Neurosurgeon | Northern Hospital | Dr Ales Aliashkevich',
      description: 'Expert neurosurgery and spine care at Northern Hospital Bundoora. Dr Ales Aliashkevich provides comprehensive neurosurgical consultations and treatments.',
      keywords: 'neurosurgeon bundoora, spine surgeon bundoora, northern hospital, neurosurgery bundoora, spine surgery bundoora',
      canonicalUrl: 'https://mineuro.com.au/locations/bundoora'
    },
    hasTherapeuticInterventions: true,
    hasAmenities: false,
    hasConditionsTreated: false,
    hasInsuranceAndFunding: false,
    hasPatientsPrivacy: true,
  },

  dandenong: {
    id: 'dandenong',
    name: 'Dandenong',
    slug: 'dandenong',
    layoutVariant: 'standard',
    seoData: {
      title: 'Dandenong Neurosurgeon | Monash Medical Centre | Dr Ales Aliashkevich',
      description: 'Expert neurosurgery and spine care at Monash Medical Centre Dandenong. Dr Ales Aliashkevich provides comprehensive neurosurgical consultations.',
      keywords: 'neurosurgeon dandenong, spine surgeon dandenong, monash medical centre, neurosurgery dandenong, spine surgery dandenong',
      canonicalUrl: 'https://mineuro.com.au/locations/dandenong'
    },
    hasTherapeuticInterventions: true,
    hasAmenities: false,
    hasConditionsTreated: false,
    hasInsuranceAndFunding: false,
    hasPatientsPrivacy: true,
  },

  langwarrin: {
    id: 'langwarrin',
    name: 'Langwarrin',
    slug: 'langwarrin',
    layoutVariant: 'standard',
    seoData: {
      title: 'Langwarrin Neurosurgeon | Peninsula Private Hospital | Dr Ales Aliashkevich',
      description: 'Expert neurosurgery and spine care at Peninsula Private Hospital Langwarrin. Dr Ales Aliashkevich provides comprehensive neurosurgical consultations.',
      keywords: 'neurosurgeon langwarrin, spine surgeon langwarrin, peninsula private hospital, neurosurgery langwarrin, spine surgery langwarrin',
      canonicalUrl: 'https://mineuro.com.au/locations/langwarrin'
    },
    hasTherapeuticInterventions: true,
    hasAmenities: false,
    hasConditionsTreated: false,
    hasInsuranceAndFunding: false,
    hasPatientsPrivacy: true,
  },

  'moonee-ponds': {
    id: 'moonee-ponds',
    name: 'Moonee Ponds',
    slug: 'moonee-ponds',
    layoutVariant: 'standard',
    seoData: {
      title: 'Moonee Ponds Neurosurgeon | Dr Ales Aliashkevich',
      description: 'Expert neurosurgery and spine care in Moonee Ponds. Dr Ales Aliashkevich provides comprehensive neurosurgical consultations and treatments.',
      keywords: 'neurosurgeon moonee ponds, spine surgeon moonee ponds, neurosurgery moonee ponds, spine surgery moonee ponds',
      canonicalUrl: 'https://mineuro.com.au/locations/moonee-ponds'
    },
    hasTherapeuticInterventions: true,
    hasAmenities: false,
    hasConditionsTreated: false,
    hasInsuranceAndFunding: false,
    hasPatientsPrivacy: true,
  },

  mornington: {
    id: 'mornington',
    name: 'Mornington',
    slug: 'mornington',
    layoutVariant: 'standard',
    seoData: {
      title: 'Mornington Neurosurgeon | Dr Ales Aliashkevich',
      description: 'Expert neurosurgery and spine care in Mornington. Dr Ales Aliashkevich provides comprehensive neurosurgical consultations and treatments.',
      keywords: 'neurosurgeon mornington, spine surgeon mornington, neurosurgery mornington, spine surgery mornington',
      canonicalUrl: 'https://mineuro.com.au/locations/mornington'
    },
    hasTherapeuticInterventions: true,
    hasAmenities: false,
    hasConditionsTreated: false,
    hasInsuranceAndFunding: false,
    hasPatientsPrivacy: true,
  },

  'surrey-hills': {
    id: 'surrey-hills',
    name: 'Surrey Hills',
    slug: 'surrey-hills',
    layoutVariant: 'standard',
    seoData: {
      title: 'Surrey Hills Neurosurgeon | Dr Ales Aliashkevich',
      description: 'Expert neurosurgery and spine care in Surrey Hills. Dr Ales Aliashkevich provides comprehensive neurosurgical consultations and treatments.',
      keywords: 'neurosurgeon surrey hills, spine surgeon surrey hills, neurosurgery surrey hills, spine surgery surrey hills',
      canonicalUrl: 'https://mineuro.com.au/locations/surrey-hills'
    },
    hasTherapeuticInterventions: true,
    hasAmenities: false,
    hasConditionsTreated: false,
    hasInsuranceAndFunding: false,
    hasPatientsPrivacy: true,
  },

  wantirna: {
    id: 'wantirna',
    name: 'Wantirna',
    slug: 'wantirna',
    layoutVariant: 'standard',
    seoData: {
      title: 'Wantirna Neurosurgeon | Knox Private Hospital | Dr Ales Aliashkevich',
      description: 'Expert neurosurgery and spine care at Knox Private Hospital Wantirna. Dr Ales Aliashkevich provides comprehensive neurosurgical consultations.',
      keywords: 'neurosurgeon wantirna, spine surgeon wantirna, knox private hospital, neurosurgery wantirna, spine surgery wantirna',
      canonicalUrl: 'https://mineuro.com.au/locations/wantirna'
    },
    hasTherapeuticInterventions: true,
    hasAmenities: false,
    hasConditionsTreated: false,
    hasInsuranceAndFunding: false,
    hasPatientsPrivacy: true,
  },

  werribee: {
    id: 'werribee',
    name: 'Werribee',
    slug: 'werribee',
    layoutVariant: 'standard',
    seoData: {
      title: 'Werribee Neurosurgeon | Werribee Mercy Hospital | Dr Ales Aliashkevich',
      description: 'Expert neurosurgery and spine care at Werribee Mercy Hospital. Dr Ales Aliashkevich provides comprehensive neurosurgical consultations.',
      keywords: 'neurosurgeon werribee, spine surgeon werribee, werribee mercy hospital, neurosurgery werribee, spine surgery werribee',
      canonicalUrl: 'https://mineuro.com.au/locations/werribee'
    },
    hasTherapeuticInterventions: true,
    hasAmenities: false,
    hasConditionsTreated: false,
    hasInsuranceAndFunding: false,
    hasPatientsPrivacy: true,
  },
};

/**
 * Get location configuration by slug
 */
export const getLocationConfig = (slug: string): LocationConfig | null => {
  return locationConfigs[slug] || null;
};

/**
 * Get all location configurations
 */
export const getAllLocationConfigs = (): LocationConfig[] => {
  return Object.values(locationConfigs);
};

/**
 * Check if a location has a specific section
 */
export const locationHasSection = (slug: string, sectionName: keyof Omit<LocationConfig, 'id' | 'name' | 'slug' | 'layoutVariant' | 'seoData' | 'specialSections' | 'customSections'>): boolean => {
  const config = getLocationConfig(slug);
  return config ? config[sectionName] : false;
};

export default {
  locationConfigs,
  getLocationConfig,
  getAllLocationConfigs,
  locationHasSection,
};
