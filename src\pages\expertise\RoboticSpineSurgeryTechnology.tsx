import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import ExpertiseCallToAction from '@/components/expertise/ExpertiseCallToAction';
import ExpertiseHero from '@/components/expertise/ExpertiseHero';
import ExpertiseSidebar from '@/components/expertise/ExpertiseSidebar';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { roboticSpineSurgeryTechnologyData } from '@/data/expertise/roboticSpineSurgeryTechnologyData';

/**
 * Robotic Spine Surgery Technology Component
 * Comprehensive technology page with specialised content from documentation
 * Focuses on robotic systems, AI integration, and technical capabilities
 */

const RoboticSpineSurgeryTechnology: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const data = roboticSpineSurgeryTechnologyData;

  return (
    <StandardPageLayout title="Robotic Spine Surgery Technology" showHeader={false}>
      <ExpertiseHero
        title={data.hero.title}
        subtitle={data.hero.subtitle}
        backgroundImage={data.hero.backgroundImage}
      />

      <div className="flex-1">
        {/* Modern Navigation Header */}
        <section className="py-6 bg-muted/30 border-b">
          <div className="container px-4 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Link
                  to="/expertise/robotic-spine-surgery"
                  className="flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors group"
                >
                  <svg className="w-5 h-5 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  <span className="text-sm font-medium">Robotic Spine Surgery</span>
                </Link>
              </div>
              <div className="text-right">
                <h1 className="text-xl font-bold text-foreground">Technology</h1>
                <p className="text-sm text-muted-foreground">Advanced Robotic Systems</p>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                {/* Overview */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.overview.title}</h2>
                  {data.overview.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-6">
                      {paragraph}
                    </p>
                  ))}
                  
                  <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                    <SafeImage
                      src={data.images.overview}
                      alt="Advanced robotic spine surgery technology platform"
                      className="w-full h-full object-cover"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  </div>
                </div>

                {/* Robotic Platforms */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.roboticPlatforms.title}</h2>
                  <p className="text-muted-foreground mb-6">{data.roboticPlatforms.introduction}</p>
                  
                  <div className="space-y-6">
                    {data.roboticPlatforms.platforms.map((platform, index) => (
                      <div key={index} className="border rounded-lg p-6 medical-card">
                        <h3 className="text-lg font-semibold mb-3 text-primary">{platform.title}</h3>
                        <p className="text-muted-foreground mb-4">{platform.description}</p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {platform.features.map((feature, featureIndex) => (
                            <div key={featureIndex} className="flex items-center text-sm text-enhanced-caption">
                              <span className="w-2 h-2 bg-primary rounded-full mr-2"></span>
                              {feature}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* System Components */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.systemComponents.title}</h2>
                  <p className="text-muted-foreground mb-6">{data.systemComponents.introduction}</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {data.systemComponents.components.map((component, index) => (
                      <div key={index} className="bg-muted/30 p-6 rounded-lg">
                        <h3 className="text-lg font-semibold mb-3">{component.title}</h3>
                        <p className="text-muted-foreground mb-4 text-sm">{component.description}</p>
                        <ul className="list-disc pl-5 space-y-1 text-sm text-muted-foreground">
                          {component.capabilities.map((capability, capIndex) => (
                            <li key={capIndex}>{capability}</li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Technology Advantages */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.technologyAdvantages.title}</h2>
                  <p className="text-muted-foreground mb-6">{data.technologyAdvantages.introduction}</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {data.technologyAdvantages.advantages.map((advantage, index) => (
                      <div key={index} className="bg-primary/10 p-6 rounded-lg">
                        <h3 className="text-lg font-semibold mb-3 text-primary">{advantage.category}</h3>
                        <ul className="list-disc pl-5 space-y-2 text-sm text-muted-foreground">
                          {advantage.benefits.map((benefit, benefitIndex) => (
                            <li key={benefitIndex}>{benefit}</li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>

                {/* AI Integration */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.aiIntegration.title}</h2>
                  {data.aiIntegration.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-4">
                      {paragraph}
                    </p>
                  ))}
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    {data.aiIntegration.features.map((feature, index) => (
                      <div key={index} className="border rounded-lg p-6 bg-muted">
                        <h3 className="text-lg font-semibold mb-3 text-info">{feature.title}</h3>
                        <p className="text-info text-sm">{feature.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Precision Capabilities */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.precisionCapabilities.title}</h2>
                  {data.precisionCapabilities.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-4">
                      {paragraph}
                    </p>
                  ))}
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                    {data.precisionCapabilities.metrics.map((metric, index) => (
                      <div key={index} className="text-center p-4 bg-primary/5 rounded-lg">
                        <div className="text-enhanced-heading text-2xl font-bold text-primary mb-2">{metric.value}</div>
                        <div className="text-sm font-semibold mb-1">{metric.metric}</div>
                        <div className="text-xs text-muted-foreground">{metric.description}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Safety Features */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.safetyFeatures.title}</h2>
                  {data.safetyFeatures.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-4">
                      {paragraph}
                    </p>
                  ))}
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-6">
                    {data.safetyFeatures.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-sm text-muted-foreground bg-success border border-success p-3 rounded">
                        <span className="w-2 h-2 bg-success rounded-full mr-3"></span>
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Related Pages Navigation */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Related Robotic Surgery Information</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="card p-6 rounded-lg shadow-md medical-card border-l-4 border-l-primary">
                      <h3 className="text-xl font-semibold mb-3 text-primary">Robotic Spine Surgery Overview</h3>
                      <p className="text-muted-foreground mb-4">
                        Learn about the general principles and applications of robotic spine surgery.
                      </p>
                      <Button asChild variant="outline">
                        <Link to="/expertise/robotic-spine-surgery">Learn More</Link>
                      </Button>
                    </div>
                    
                    <div className="card p-6 rounded-lg shadow-md medical-card border-l-4 border-l-secondary">
                      <h3 className="text-xl font-semibold mb-3 text-secondary">Robotic Surgery Procedures</h3>
                      <p className="text-muted-foreground mb-4">
                        Discover specific procedures and clinical applications of robotic spine surgery.
                      </p>
                      <Button asChild variant="outline">
                        <Link to="/expertise/robotic-spine-surgery/procedures">Learn More</Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <ExpertiseSidebar sections={data.sidebar} />
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <ExpertiseCallToAction
          title={data.callToAction.title}
          description={data.callToAction.description}
          primaryButton={data.callToAction.primaryButton}
          secondaryButton={data.callToAction.secondaryButton}
        />
      </div>
    </StandardPageLayout>
  );
};

RoboticSpineSurgeryTechnology.displayName = 'RoboticSpineSurgeryTechnology';

export default RoboticSpineSurgeryTechnology;
