import React from 'react';

import type { WhenLessIsMoreProps } from '@/types/homepage';

/**
 * When Less is More Section Component
 * Explains keyhole approaches and MIS requirements
 * Preserves all original content and styling from Index.tsx lines 701-761
 */
const WhenLessIsMoreSection: React.FC<WhenLessIsMoreProps> = ({ benefits, principles }) => {
  return (
    <section className="section">
      <div className="container">
        <div className="text-center mb-12">
          <span className="text-enhanced-caption text-primary font-semibold uppercase tracking-wider text-sm">
            KEYHOLE APPROACHES
          </span>
          <h2 className="text-enhanced-heading text-3xl md:text-4xl font-bold mt-2 mb-6">When Less is More</h2>
        </div>
        <div className="max-w-4xl mx-auto">
          <p className="text-muted-foreground mb-6 text-lg">
            The treatment options for brain and spine problems usually include conservative and interventional strategies. Neurosurgeons usually consider non-surgical strategy first. When any surgery comes into question, it should be performed in the least disruptive and less invasive way.
          </p>
          <p className="text-muted-foreground mb-6">
            The term 'minimally invasive surgery' is often used as a synonym for 'keyhole surgery' emphasizing the small size of skin incisions. There is a lot of work hidden underneath the small scar:
          </p>
          <div className="grid md:grid-cols-3 gap-8 mb-8">
            {benefits.map((benefit) => (
              <BenefitCard key={benefit.id} benefit={benefit} />
            ))}
          </div>
          <div className="bg-primary/5 p-6 rounded-lg">
            <p className="text-muted-foreground mb-4">
              Minimally invasive procedures represent a logical evolution of surgical philosophy focusing on patient safety and satisfaction. This philosophy is based on three main principles:
            </p>
            <ul className="text-muted-foreground list-none space-y-2">
              {principles.map((principle, index) => (
                <li key={index}>– {principle}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

/**
 * Individual Benefit Card Component
 */
interface BenefitCardProps {
  benefit: {
    id: string;
    title: string;
    description: string;
    icon: string;
  };
}

const BenefitCard: React.FC<BenefitCardProps> = ({ benefit }) => {
  const renderIcon = (iconType: string) => {
    switch (iconType) {
      case 'training':
        return (
          <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        );
      case 'technology':
        return (
          <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
        );
      case 'precision':
        return (
          <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div className="text-center">
      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
        {renderIcon(benefit.icon)}
      </div>
      <h3 className="text-xl font-semibold mb-3 text-enhanced-heading">{benefit.title}</h3>
      <p className="text-muted-foreground">
        {benefit.description}
      </p>
    </div>
  );
};

WhenLessIsMoreSection.displayName = 'WhenLessIsMoreSection';

export default WhenLessIsMoreSection;
