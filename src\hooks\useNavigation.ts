import { useState, useEffect, useCallback } from 'react';

export interface NavigationState {
  mobileMenuOpen: boolean;
  scrolled: boolean;
  activeSubmenu: string | null;
  hoverTimeout: NodeJS.Timeout | null;
}

export const useNavigation = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 20;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };
    window.addEventListener("scroll", handleScroll);

    // Close submenu when clicking outside
    const handleClickOutside = (e: MouseEvent) => {
      if (activeSubmenu && !(e.target as Element).closest('.nav-item-with-submenu')) {
        setActiveSubmenu(null);
      }
    };
    document.addEventListener('click', handleClickOutside);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      document.removeEventListener('click', handleClickOutside);
      if (hoverTimeout) {
        clearTimeout(hoverTimeout);
      }
    };
  }, [scrolled, activeSubmenu, hoverTimeout]);

  // Submenu hover handlers
  const handleSubmenuEnter = useCallback((name: string) => {
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
      setHoverTimeout(null);
    }
    setActiveSubmenu(name);
  }, [hoverTimeout]);

  const handleSubmenuLeave = useCallback(() => {
    const timeout = setTimeout(() => {
      setActiveSubmenu(null);
    }, 150); // 150ms delay before closing
    setHoverTimeout(timeout);
  }, []);

  // Keyboard navigation handler
  const handleKeyDown = useCallback((e: React.KeyboardEvent, name: string, hasSubmenu: boolean) => {
    if (hasSubmenu && e.key === 'ArrowDown') {
      e.preventDefault();
      setActiveSubmenu(name);
    } else if (hasSubmenu && (e.key === 'Enter' || e.key === ' ')) {
      if (activeSubmenu === name) {
        return; // Let navigation happen
      } else {
        e.preventDefault();
        setActiveSubmenu(name);
      }
    }
  }, [activeSubmenu]);

  // Mobile menu handlers
  const toggleMobileMenu = useCallback(() => {
    setMobileMenuOpen(prev => !prev);
  }, []);

  const closeMobileMenu = useCallback(() => {
    setMobileMenuOpen(false);
  }, []);

  const closeSubmenu = useCallback(() => {
    setActiveSubmenu(null);
  }, []);

  // Mobile click handler factory
  const createMobileClickHandler = useCallback((linkName: string, hasSubmenu: boolean) => {
    return (e: React.MouseEvent) => {
      if (hasSubmenu) {
        const target = e.currentTarget as HTMLElement;
        const rect = target.getBoundingClientRect();
        const isClickNearRightEdge = (e.clientX > rect.right - 40);

        if (isClickNearRightEdge) {
          e.preventDefault();
          setActiveSubmenu(activeSubmenu === linkName ? null : linkName);
        } else {
          closeMobileMenu();
        }
      } else {
        closeMobileMenu();
      }
    };
  }, [activeSubmenu, closeMobileMenu]);

  // Keyboard handler factory
  const createKeyDownHandler = useCallback((linkName: string) => {
    return (e: React.KeyboardEvent) => handleKeyDown(e, linkName, true);
  }, [handleKeyDown]);

  return {
    // State
    mobileMenuOpen,
    scrolled,
    activeSubmenu,
    
    // Actions
    toggleMobileMenu,
    closeMobileMenu,
    closeSubmenu,
    handleSubmenuEnter,
    handleSubmenuLeave,
    
    // Factories
    createMobileClickHandler,
    createKeyDownHandler
  };
};
