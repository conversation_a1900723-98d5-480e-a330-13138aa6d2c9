import React, { useEffect } from 'react';

import { ConditionCauses } from '@/components/medical-conditions/shared/ConditionCauses';
import { ConditionHero } from '@/components/medical-conditions/shared/ConditionHero';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import { ConditionSymptoms } from '@/components/medical-conditions/shared/ConditionSymptoms';
import { ConditionTreatment } from '@/components/medical-conditions/shared/ConditionTreatment';
import StandardPageLayout from '@/components/StandardPageLayout';
import parsdefectsData from '@/data/conditions/parsdefects';

/**
 * Refactored ParsDefects Component
 * 
 * Original component: 81KB
 * Refactored component: <100 lines
 * Reduction: ~95%
 */

const ParsDefectsRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout 
      title="Pars Defects - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={parsdefectsData.hero.title}
          subtitle={parsdefectsData.hero.subtitle}
          backgroundImage={parsdefectsData.hero.backgroundImage}
          badge={parsdefectsData.hero.badge}
        />

        <ConditionQuickFacts facts={parsdefectsData.quickFacts} />

        <ConditionOverviewSection
          title={parsdefectsData.overview.title}
          description={parsdefectsData.overview.description}
          keyPoints={parsdefectsData.overview.keyPoints}
          imageSrc={parsdefectsData.overview.imageSrc}
          imageAlt={parsdefectsData.overview.imageAlt}
          imageCaption={parsdefectsData.overview.imageCaption}
        />

        <ConditionCauses
          causes={parsdefectsData.causes}
          riskFactors={parsdefectsData.riskFactors}
        />

        <ConditionSymptoms
          symptomCategories={parsdefectsData.symptoms}
          warningSigns={parsdefectsData.warningSigns}
        />

        <ConditionTreatment
          conservativeOptions={parsdefectsData.conservativeTreatments}
          surgicalOptions={parsdefectsData.surgicalTreatments}
        />
      </main>
    </StandardPageLayout>
  );
};

ParsDefectsRefactored.displayName = 'ParsDefectsRefactored';

export default ParsDefectsRefactored;