import React from 'react';
import { Link } from 'react-router-dom';

import { But<PERSON> } from '@/components/ui/button';

interface FAQCallToActionProps {
  title: string;
  description: string;
  primaryButtonText: string;
  primaryButtonLink: string;
  secondaryButtonText: string;
  secondaryButtonLink: string;
}

const FAQCallToAction: React.FC<FAQCallToActionProps> = ({
  title,
  description,
  primaryButtonText,
  primaryButtonLink,
  secondaryButtonText,
  secondaryButtonLink
}) => {
  return (
    <section className="py-20 bg-gradient-to-br from-primary/10 via-primary/5 to-background border-y border-border/50">
      <div className="container">
        <div className="text-center max-w-4xl mx-auto">
          <h2 className="text-enhanced-heading text-3xl lg:text-4xl font-bold mb-8 text-foreground">{title}</h2>
          <p className="text-foreground/80 mb-12 text-lg leading-relaxed font-medium">
            {description}
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-6">
            <Button asChild size="lg" className="font-bold py-3 px-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <Link to={primaryButtonLink}>{primaryButtonText}</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="font-bold py-3 px-8 border-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <Link to={secondaryButtonLink}>{secondaryButtonText}</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

FAQCallToAction.displayName = 'FAQCallToAction';

export default FAQCallToAction;
