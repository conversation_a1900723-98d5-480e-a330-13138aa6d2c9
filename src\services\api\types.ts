/**
 * Core types for the centralised service layer
 */

// API Configuration
export interface ApiConfig {
  baseURL: string;
  timeout?: number;
  retryAttempts?: number;
  headers?: Record<string, string>;
}

// Request Configuration
export interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: unknown;
  headers?: Record<string, string>;
  timeout?: number;
  retryAttempts?: number;
  signal?: AbortSignal;
}

// API Response
export interface ApiResponse<T = unknown> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config: RequestConfig;
}

// Service Error
export interface ServiceError extends Error {
  code: string;
  status?: number;
  details?: Record<string, unknown>;
  recoverable: boolean;
  userMessage?: string;
  originalError?: Error;
}

// Error Types
export type ErrorType = 
  | 'network-error'
  | 'server-error' 
  | 'client-error'
  | 'validation-error'
  | 'not-found'
  | 'unauthorized'
  | 'forbidden'
  | 'timeout-error'
  | 'abort-error';

// Retry Options
export interface RetryOptions {
  maxAttempts?: number;
  baseDelay?: number;
  backoffFactor?: number;
  retryCondition?: (error: ServiceError) => boolean;
}

// Cache Options
export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  persistent?: boolean; // Store in persistent storage
  tags?: string[]; // Cache tags for invalidation
}

// Cache Entry
export interface CacheEntry<T = unknown> {
  data: T;
  timestamp: number;
  ttl: number;
  tags: string[];
}

// Request Interceptor
export interface RequestInterceptor {
  onRequest?: (config: RequestConfig) => Promise<RequestConfig>;
  onRequestError?: (error: Error) => Promise<never>;
}

// Response Interceptor
export interface ResponseInterceptor {
  onResponse?: <T>(response: ApiResponse<T>) => Promise<ApiResponse<T>>;
  onResponseError?: (error: ServiceError) => Promise<never>;
}

// Medical Data Types
export interface MedicalCondition {
  id: string;
  name: string;
  description: string;
  symptoms: string[];
  treatments: string[];
  category: string;
}

export interface Procedure {
  id: string;
  name: string;
  description: string;
  duration: string;
  recovery: string;
  risks: string[];
  benefits: string[];
}

export interface Exercise {
  id: string;
  name: string;
  description: string;
  instructions: string[];
  duration: string;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
}

// Appointment Types
export interface AppointmentRequest {
  patientName: string;
  email: string;
  phone: string;
  preferredDate: string;
  preferredTime: string;
  locationId: string;
  reason: string;
  hasReferral: boolean;
}

export interface Appointment {
  id: string;
  patientName: string;
  date: string;
  time: string;
  location: string;
  status: 'pending' | 'confirmed' | 'cancelled';
  reason: string;
}

export interface TimeSlot {
  id: string;
  date: string;
  time: string;
  available: boolean;
  locationId: string;
}

// Location Types
export interface Location {
  id: string;
  name: string;
  address: string;
  phone: string;
  fax?: string;
  email?: string;
  hours: OperatingHours[];
  services: string[];
}

export interface OperatingHours {
  day: string;
  open: string;
  close: string;
  closed?: boolean;
}

// Error Reporter
export interface ErrorReporter {
  report(error: ServiceError): Promise<void>;
}

// Persistent Cache Interface
export interface PersistentCache {
  get<T>(key: string): Promise<CacheEntry<T> | null>;
  set<T>(key: string, entry: CacheEntry<T>): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  keys(): Promise<string[]>;
}

// Service Base Interface
export interface ServiceBase {
  readonly name: string;
  readonly version: string;
  isHealthy(): Promise<boolean>;
}

// Query Key Factory
export interface QueryKeyFactory {
  all: readonly string[];
  lists: () => readonly string[];
  list: (filters?: Record<string, unknown>) => readonly string[];
  details: () => readonly string[];
  detail: (id: string) => readonly string[];
}

// Service Events
export type ServiceEvent = 
  | 'request-start'
  | 'request-success'
  | 'request-error'
  | 'cache-hit'
  | 'cache-miss'
  | 'error-reported';

export interface ServiceEventData {
  type: ServiceEvent;
  timestamp: number;
  details: Record<string, unknown>;
}

// Service Metrics
export interface ServiceMetrics {
  requestCount: number;
  errorCount: number;
  averageResponseTime: number;
  cacheHitRate: number;
  lastError?: ServiceError;
}

// Environment Configuration
export interface EnvironmentConfig {
  apiBaseUrl: string;
  timeout: number;
  retryAttempts: number;
  enableCaching: boolean;
  enableErrorReporting: boolean;
  enableMetrics: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}
