import React, { useEffect } from 'react';

import { ConditionCauses } from '@/components/medical-conditions/shared/ConditionCauses';
import { ConditionHero } from '@/components/medical-conditions/shared/ConditionHero';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import { ConditionSymptoms } from '@/components/medical-conditions/shared/ConditionSymptoms';
import { ConditionTreatment } from '@/components/medical-conditions/shared/ConditionTreatment';
import StandardPageLayout from '@/components/StandardPageLayout';
import piriformissyndromeData from '@/data/conditions/piriformissyndrome';

/**
 * Refactored PiriformisSyndrome Component
 * 
 * Original component: 81KB
 * Refactored component: <100 lines
 * Reduction: ~95%
 */

const PiriformisSyndromeRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout 
      title="Piriformis Syndrome - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={piriformissyndromeData.hero.title}
          subtitle={piriformissyndromeData.hero.subtitle}
          backgroundImage={piriformissyndromeData.hero.backgroundImage}
          badge={piriformissyndromeData.hero.badge}
        />

        <ConditionQuickFacts facts={piriformissyndromeData.quickFacts} />

        <ConditionOverviewSection
          title={piriformissyndromeData.overview.title}
          description={piriformissyndromeData.overview.description}
          keyPoints={piriformissyndromeData.overview.keyPoints}
          imageSrc={piriformissyndromeData.overview.imageSrc}
          imageAlt={piriformissyndromeData.overview.imageAlt}
          imageCaption={piriformissyndromeData.overview.imageCaption}
        />

        <ConditionCauses
          causes={piriformissyndromeData.causes}
          riskFactors={piriformissyndromeData.riskFactors}
        />

        <ConditionSymptoms
          symptomCategories={piriformissyndromeData.symptoms}
          warningSigns={piriformissyndromeData.warningSigns}
        />

        <ConditionTreatment
          conservativeOptions={piriformissyndromeData.conservativeTreatments}
          surgicalOptions={piriformissyndromeData.surgicalTreatments}
        />
      </main>
    </StandardPageLayout>
  );
};

PiriformisSyndromeRefactored.displayName = 'PiriformisSyndromeRefactored';

export default PiriformisSyndromeRefactored;