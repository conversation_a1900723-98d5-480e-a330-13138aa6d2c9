
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Premium FAQ Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.2);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Custom utility classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Enhanced scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(241 245 249);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgb(59 130 246), rgb(99 102 241));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgb(37 99 235), rgb(79 70 229));
}

@layer base {
  :root {
    /* Enhanced Professional Medical Theme - WCAG AA Compliant */
    --background: 0 0% 100%;
    --foreground: 215 25% 15%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 15%;

    --primary: 210 85% 42%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 30% 96%;
    --secondary-foreground: 215 25% 20%;

    --muted: 210 25% 95%;
    --muted-foreground: 215 20% 35%;

    --accent: 210 25% 96%;
    --accent-foreground: 215 25% 20%;

    --destructive: 0 65% 48%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 15% 88%;
    --input: 210 15% 88%;
    --ring: 210 85% 42%;

    --radius: 0.5rem;

    /* Enhanced Professional Medical Color Palette */
    --medical-blue: 210 85% 42%;
    --medical-blue-foreground: 0 0% 100%;
    --medical-blue-light: 210 85% 97%;
    --medical-blue-dark: 210 85% 32%;

    --success: 142 65% 35%;
    --success-foreground: 0 0% 100%;
    --success-light: 142 65% 96%;

    --warning: 38 85% 52%;
    --warning-foreground: 0 0% 100%;
    --warning-light: 38 85% 96%;

    --error: 0 70% 50%;
    --error-foreground: 0 0% 100%;
    --error-light: 0 70% 96%;

    --info: 199 80% 42%;
    --info-foreground: 0 0% 100%;
    --info-light: 199 80% 96%;

    /* Professional Trust Colors */
    --trust-navy: 215 85% 25%;
    --trust-navy-light: 215 85% 95%;
    --trust-teal: 180 65% 35%;
    --trust-teal-light: 180 65% 95%;
  }

  .dark {
    /* Enhanced Professional Dark Theme - Improved Medical Readability */
    --background: 220 20% 12%;
    --foreground: 220 10% 96%;

    --card: 220 15% 16%;
    --card-foreground: 220 10% 96%;

    --popover: 220 15% 16%;
    --popover-foreground: 220 10% 96%;

    --primary: 210 80% 65%;
    --primary-foreground: 220 20% 12%;

    --secondary: 220 12% 22%;
    --secondary-foreground: 220 10% 90%;

    --muted: 220 12% 22%;
    --muted-foreground: 220 10% 75%;

    --accent: 220 12% 22%;
    --accent-foreground: 220 10% 90%;

    --destructive: 0 60% 62%;
    --destructive-foreground: 220 10% 96%;

    --border: 220 12% 28%;
    --input: 220 12% 28%;
    --ring: 210 80% 65%;

    /* Enhanced Professional Dark Medical Colors - Better Contrast */
    --medical-blue: 210 80% 68%;
    --medical-blue-foreground: 220 20% 12%;
    --medical-blue-light: 210 80% 20%;
    --medical-blue-dark: 210 80% 75%;

    --success: 142 60% 65%;
    --success-foreground: 220 20% 12%;
    --success-light: 142 60% 20%;

    --warning: 38 80% 68%;
    --warning-foreground: 220 20% 12%;
    --warning-light: 38 80% 20%;

    --error: 0 65% 72%;
    --error-foreground: 220 20% 12%;
    --error-light: 0 65% 20%;

    --info: 199 75% 68%;
    --info-foreground: 220 20% 12%;
    --info-light: 199 75% 20%;

    /* Professional Dark Trust Colors - Enhanced Visibility */
    --trust-navy: 215 80% 50%;
    --trust-navy-light: 215 80% 20%;
    --trust-teal: 180 60% 60%;
    --trust-teal-light: 180 60% 20%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html, body {
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    font-weight: 400;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.2;
    color: hsl(var(--foreground));
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight;
    margin-bottom: 1.5rem;
    color: hsl(var(--foreground));
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl font-semibold leading-tight tracking-tight;
    margin-bottom: 1.25rem;
    color: hsl(var(--foreground));
  }

  h3 {
    @apply text-2xl md:text-3xl lg:text-4xl font-semibold leading-snug tracking-tight;
    margin-bottom: 1rem;
    color: hsl(var(--foreground));
  }

  h4 {
    @apply text-xl md:text-2xl lg:text-3xl font-medium leading-snug;
    margin-bottom: 0.875rem;
    color: hsl(var(--foreground));
  }

  h5 {
    @apply text-lg md:text-xl lg:text-2xl font-medium leading-normal;
    margin-bottom: 0.75rem;
    color: hsl(var(--foreground));
  }

  h6 {
    @apply text-base md:text-lg lg:text-xl font-medium leading-normal;
    margin-bottom: 0.625rem;
    color: hsl(var(--foreground));
  }

  p {
    @apply text-foreground/90 leading-relaxed text-base md:text-lg;
    margin-bottom: 1rem;
    line-height: 1.7;
  }

  .lead {
    @apply text-lg md:text-xl text-foreground/95 leading-relaxed font-normal;
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }

  strong, b {
    @apply font-semibold text-foreground;
  }

  em, i {
    @apply italic text-foreground/95;
  }

  /* Enhanced Link Styling for Dark Theme */
  a {
    @apply transition-colors duration-200;
  }

  a:not(.btn-primary):not(.btn-secondary):not(.btn-outline) {
    color: hsl(var(--primary));
    text-decoration: none;
  }

  a:not(.btn-primary):not(.btn-secondary):not(.btn-outline):hover {
    color: hsl(var(--primary) / 0.8);
    text-decoration: underline;
  }

  .dark a:not(.btn-primary):not(.btn-secondary):not(.btn-outline) {
    color: hsl(var(--primary) / 0.9);
  }

  .dark a:not(.btn-primary):not(.btn-secondary):not(.btn-outline):hover {
    color: hsl(var(--primary));
    text-shadow: 0 0 8px hsl(var(--primary) / 0.3);
  }

  /* Enhanced text hierarchy classes */
  .text-hero {
    @apply text-5xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight;
    color: hsl(var(--foreground));
    margin-bottom: 2rem;
  }

  .text-display {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight;
    color: hsl(var(--foreground));
    margin-bottom: 1.5rem;
  }

  .text-headline {
    @apply text-3xl md:text-4xl lg:text-5xl font-semibold leading-tight tracking-tight;
    color: hsl(var(--foreground));
    margin-bottom: 1.25rem;
  }

  .text-title {
    @apply text-2xl md:text-3xl lg:text-4xl font-semibold leading-snug tracking-tight;
    color: hsl(var(--foreground));
    margin-bottom: 1rem;
  }

  .text-subtitle {
    @apply text-xl md:text-2xl lg:text-3xl font-medium leading-snug;
    color: hsl(var(--foreground));
    margin-bottom: 0.875rem;
  }

  /* Medical Content Typography */
  .medical-heading {
    @apply text-2xl md:text-3xl font-bold leading-tight;
    color: hsl(var(--foreground));
    margin-bottom: 1.5rem;
    border-bottom: 2px solid hsl(var(--primary) / 0.3);
    padding-bottom: 0.5rem;
  }

  .medical-subheading {
    @apply text-lg md:text-xl font-semibold leading-snug;
    color: hsl(var(--foreground));
    margin-bottom: 1rem;
    margin-top: 2rem;
  }

  .medical-body {
    @apply text-base leading-relaxed;
    color: hsl(var(--foreground) / 0.9);
    margin-bottom: 1.25rem;
    line-height: 1.7;
  }

  .medical-caption {
    @apply text-sm leading-normal;
    color: hsl(var(--muted-foreground));
    margin-top: 0.5rem;
    font-style: italic;
  }

  /* Remove any browser default progress bars or loading indicators */
  ::-webkit-progress-bar,
  ::-webkit-progress-value,
  progress {
    display: none !important;
  }
}

@layer components {
  .container {
    @apply px-4 md:px-6 lg:px-8 mx-auto;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground rounded-lg px-8 py-3 font-semibold transition-all duration-300 hover:shadow-lg hover:shadow-primary/25 active:scale-[0.98];
    border: 1px solid hsl(var(--primary));
    position: relative;
    overflow: hidden;
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.9));
    transform: translateY(-1px);
  }

  .dark .btn-primary {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.95));
    border-color: hsl(var(--primary) / 0.8);
    box-shadow: 0 4px 12px hsl(var(--primary) / 0.3);
  }

  .dark .btn-primary:hover {
    background: linear-gradient(135deg, hsl(var(--primary) / 1.1), hsl(var(--primary)));
    box-shadow: 0 6px 20px hsl(var(--primary) / 0.4);
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground rounded-lg px-8 py-3 font-semibold transition-all duration-300 hover:shadow-md border border-border/50;
  }

  .btn-secondary:hover {
    @apply bg-secondary/80 border-border;
    transform: translateY(-1px);
  }

  .dark .btn-secondary {
    background: hsl(var(--secondary) / 0.8);
    border-color: hsl(var(--border) / 0.6);
  }

  .dark .btn-secondary:hover {
    background: hsl(var(--secondary));
    border-color: hsl(var(--border));
  }

  .btn-outline {
    @apply bg-transparent text-primary border-2 border-primary rounded-lg px-8 py-3 font-semibold transition-all duration-300 hover:bg-primary hover:text-primary-foreground;
  }

  .btn-outline:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px hsl(var(--primary) / 0.3);
  }

  .dark .btn-outline {
    border-color: hsl(var(--primary) / 0.8);
    color: hsl(var(--primary));
  }

  .dark .btn-outline:hover {
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    box-shadow: 0 6px 20px hsl(var(--primary) / 0.4);
  }

  /* Enhanced Focus States for Accessibility */
  .btn-primary:focus-visible,
  .btn-secondary:focus-visible,
  .btn-outline:focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }

  .dark .btn-primary:focus-visible,
  .dark .btn-secondary:focus-visible,
  .dark .btn-outline:focus-visible {
    outline-color: hsl(var(--primary) / 0.8);
    box-shadow: 0 0 0 4px hsl(var(--primary) / 0.2);
  }

  /* Interactive Element Enhancements */
  .medical-card-interactive:focus-within {
    outline: 2px solid hsl(var(--primary) / 0.5);
    outline-offset: 2px;
  }

  .dark .medical-card-interactive:focus-within {
    outline-color: hsl(var(--primary) / 0.7);
    box-shadow: 0 0 0 4px hsl(var(--primary) / 0.2);
  }

  .section {
    @apply py-12 md:py-16 lg:py-24;
  }

  /* Enhanced Glass Card with Better Contrast */
  .glass-card {
    @apply bg-card/95 backdrop-blur-lg border border-border/50 rounded-xl shadow-lg;
  }

  /* Enhanced Professional Card Styling */
  .medical-card {
    @apply bg-card border border-border/40 rounded-xl shadow-sm hover:shadow-md transition-all duration-300;
    padding: 2rem;
    backdrop-filter: blur(8px);
  }

  .medical-card-content {
    @apply text-card-foreground space-y-4;
  }

  .medical-card-inner {
    @apply bg-card/80 border border-border/20 rounded-lg p-6;
    backdrop-filter: blur(4px);
  }

  /* Enhanced Card Variants */
  .medical-card-elevated {
    @apply medical-card shadow-md hover:shadow-lg border-border/50;
    transform: translateY(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .medical-card-elevated:hover {
    transform: translateY(-2px);
  }

  .medical-card-interactive {
    @apply medical-card hover:border-primary/40 cursor-pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .medical-card-interactive:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .medical-card-feature {
    @apply medical-card border-primary/15 hover:bg-primary/10;
    background-color: hsl(var(--primary) / 0.03);
    position: relative;
    overflow: hidden;
  }

  .medical-card-feature:hover {
    background-color: hsl(var(--primary) / 0.08);
  }

  .medical-card-feature::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--primary) / 0.7));
  }

  /* Professional Section Backgrounds - Enhanced */
  .section-background {
    @apply bg-background;
    border-bottom: 1px solid hsl(var(--border) / 0.1);
  }

  .section-background-muted {
    @apply bg-muted/20;
    border-bottom: 1px solid hsl(var(--border) / 0.1);
  }

  .section-background-alt {
    @apply bg-accent/20;
    border-bottom: 1px solid hsl(var(--border) / 0.1);
  }

  .section-background-card {
    @apply bg-card;
    border-bottom: 1px solid hsl(var(--border) / 0.1);
  }

  .section-background-primary {
    @apply bg-primary/5;
    border-bottom: 1px solid hsl(var(--primary) / 0.1);
  }

  /* Enhanced Professional Section Spacing */
  .section-spacing {
    @apply py-16 md:py-20 lg:py-24;
  }

  .section-spacing-sm {
    @apply py-12 md:py-16 lg:py-20;
  }

  .section-spacing-lg {
    @apply py-20 md:py-24 lg:py-32;
  }

  .section-spacing-xl {
    @apply py-24 md:py-32 lg:py-40;
  }

  /* Content spacing within sections */
  .section-content-spacing {
    @apply space-y-8 md:space-y-12 lg:space-y-16;
  }

  .section-header-spacing {
    @apply mb-12 md:mb-16 lg:mb-20;
  }

  /* Enhanced Medical Content Layout */
  .medical-content-layout {
    @apply space-y-6 md:space-y-8;
  }

  .medical-section-divider {
    @apply border-t border-border/30 my-8 md:my-12;
  }

  .medical-content-grid {
    @apply grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12;
  }

  .medical-info-panel {
    @apply bg-primary/5 border border-primary/20 rounded-lg p-6;
    backdrop-filter: blur(8px);
  }

  .dark .medical-info-panel {
    background: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, hsl(var(--primary) / 0.05) 100%);
    border-color: hsl(var(--primary) / 0.3);
  }

  /* Content Width Management */
  .content-width-narrow {
    @apply max-w-3xl mx-auto;
  }

  .content-width-standard {
    @apply max-w-4xl mx-auto;
  }

  .content-width-wide {
    @apply max-w-6xl mx-auto;
  }

  /* Container Standardization */
  .section-container {
    @apply container mx-auto px-4 md:px-6 lg:px-8;
  }

  .section-container-narrow {
    @apply max-w-4xl mx-auto px-4 md:px-6 lg:px-8;
  }

  .section-container-wide {
    @apply max-w-7xl mx-auto px-4 md:px-6 lg:px-8;
  }

  /* Enhanced Mobile-optimized components */
  .mobile-container {
    @apply px-4 sm:px-6 md:px-8 lg:px-12 mx-auto max-w-7xl;
  }

  .mobile-section {
    @apply py-12 sm:py-16 md:py-20 lg:py-24;
  }

  .mobile-section-sm {
    @apply py-8 sm:py-12 md:py-16 lg:py-20;
  }

  .mobile-section-lg {
    @apply py-16 sm:py-20 md:py-24 lg:py-32;
  }

  /* Professional Badge Styles with Enhanced Contrast */
  .badge-emergency {
    @apply bg-muted-light text-foreground border border-border/70 font-semibold shadow-sm;
  }

  .badge-info {
    @apply bg-info-light text-info border border-info/30 font-semibold shadow-sm;
  }

  .badge-routine {
    @apply bg-success-light text-success border border-success/30 font-semibold shadow-sm;
  }

  .badge-medical {
    @apply bg-medical-blue-light text-medical-blue border border-medical-blue/30 font-semibold shadow-sm;
  }

  /* Color Utility Classes */
  .bg-info-light {
    background-color: hsl(var(--info-light));
  }

  .bg-success-light {
    background-color: hsl(var(--success-light));
  }

  .bg-muted-light {
    background-color: hsl(var(--muted));
  }

  .bg-medical-blue-light {
    background-color: hsl(var(--medical-blue-light));
  }

  .text-info {
    color: hsl(var(--info));
  }

  .text-success {
    color: hsl(var(--success));
  }

  .text-foreground {
    color: hsl(var(--foreground));
  }

  .text-medical-blue {
    color: hsl(var(--medical-blue));
  }

  .text-medical-blue-foreground {
    color: hsl(var(--medical-blue-foreground));
  }

  .border-info\/30 {
    border-color: hsl(var(--info) / 0.3);
  }

  .border-success\/30 {
    border-color: hsl(var(--success) / 0.3);
  }

  .border-border\/30 {
    border-color: hsl(var(--border) / 0.3);
  }

  .border-medical-blue\/30 {
    border-color: hsl(var(--medical-blue) / 0.3);
  }

  /* Additional Theme Color Classes */
  .bg-info {
    background-color: hsl(var(--info));
  }

  .bg-success {
    background-color: hsl(var(--success));
  }

  .bg-muted {
    background-color: hsl(var(--muted));
  }

  .bg-medical-blue {
    background-color: hsl(var(--medical-blue));
  }

  .border-info {
    border-color: hsl(var(--info));
  }

  .border-success {
    border-color: hsl(var(--success));
  }

  .border-border {
    border-color: hsl(var(--border));
  }

  .border-medical-blue {
    border-color: hsl(var(--medical-blue));
  }

  .border-background {
    border-color: hsl(var(--background));
  }

  .border-foreground {
    border-color: hsl(var(--foreground));
  }

  /* Emergency styling */
  .border-emergency {
    @apply border-border;
  }

  .border-emergency\/30 {
    @apply border-border/70;
  }

  .bg-emergency-light {
    @apply bg-muted-light;
  }

  .text-emergency {
    @apply text-foreground;
  }

  /* Enhanced Text Readability with Clear Visual Hierarchy */
  .text-enhanced {
    @apply text-foreground font-medium leading-relaxed;
  }

  .text-enhanced-muted {
    @apply text-muted-foreground leading-relaxed font-normal;
  }

  .text-enhanced-strong {
    @apply text-foreground font-semibold leading-tight;
  }

  .text-enhanced-light {
    @apply text-foreground/75 font-normal leading-relaxed;
  }

  .text-enhanced-heading {
    @apply text-foreground font-bold leading-tight tracking-tight;
  }

  .text-enhanced-subheading {
    @apply text-foreground/95 font-semibold leading-snug tracking-wide;
  }

  .text-enhanced-body {
    @apply text-foreground/85 font-normal leading-relaxed;
  }

  .text-enhanced-caption {
    @apply text-primary font-semibold leading-normal text-sm uppercase tracking-wider;
  }

  .text-enhanced-link {
    @apply text-primary font-semibold hover:text-primary/80 transition-colors duration-200;
  }

  .text-enhanced-muted {
    @apply text-muted-foreground font-normal leading-relaxed;
  }

  .text-enhanced-strong {
    @apply text-foreground font-bold leading-tight;
  }

  /* Professional Text Alignment Classes */
  .text-center-aligned {
    @apply text-center items-center justify-center;
  }

  .text-left-aligned {
    @apply text-left items-start justify-start;
  }

  .text-right-aligned {
    @apply text-right items-end justify-end;
  }

  /* Consistent Icon Alignment */
  .icon-text-aligned {
    @apply flex items-center gap-2;
  }

  .icon-text-aligned-vertical {
    @apply flex flex-col items-center gap-2;
  }

  /* Professional Spacing Patterns */
  .content-spacing-sm {
    @apply space-y-4 md:space-y-6;
  }

  .content-spacing-md {
    @apply space-y-6 md:space-y-8;
  }

  .content-spacing-lg {
    @apply space-y-8 md:space-y-12;
  }

  /* Additional Enhanced Text Classes */
  .text-enhanced-small {
    @apply text-foreground/80 text-sm font-medium leading-relaxed;
  }

  .text-enhanced-large {
    @apply text-foreground text-lg font-medium leading-relaxed;
  }

  .link-enhanced-subtle {
    @apply text-muted-foreground hover:text-foreground transition-colors duration-200;
  }

  /* Professional Card Enhancements */
  .card-enhanced {
    @apply bg-card border border-border/50 rounded-lg shadow-sm hover:shadow-md transition-all duration-300;
  }

  .card-enhanced-interactive {
    @apply bg-card border border-border/50 rounded-lg shadow-sm hover:shadow-lg hover:border-border transition-all duration-300 cursor-pointer;
  }

  /* Technology Section Specific Styling */
  .technology-card {
    @apply medical-card p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105;
    @apply border border-border/30 hover:border-primary/20;
  }

  .technology-icon-container {
    @apply rounded-2xl flex items-center justify-center shadow-lg backdrop-blur-sm;
    @apply transition-all duration-300 hover:shadow-xl hover:scale-110;
  }

  /* Professional Animation Classes */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Professional Button Enhancements */
  .button-enhanced {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-200 font-medium;
  }

  .button-enhanced-outline {
    @apply border border-border text-foreground hover:bg-muted/50 transition-all duration-200;
  }

  /* Professional Section Styling */
  .section-enhanced {
    @apply py-16 md:py-20 lg:py-24 bg-background border-y border-border/20;
  }

  .section-enhanced-alt {
    @apply py-16 md:py-20 lg:py-24 bg-muted/30 border-y border-border/20;
  }

  .section-enhanced-primary {
    @apply py-16 md:py-20 lg:py-24 bg-primary/5 border-y border-primary/20;
  }

  /* Enhanced Visual Hierarchy */
  .visual-hierarchy-section {
    @apply space-y-8 md:space-y-12 lg:space-y-16;
  }

  .visual-hierarchy-content {
    @apply space-y-6 md:space-y-8;
  }

  /* Enhanced Grid Layouts */
  .grid-technology {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8;
  }

  .grid-features {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8;
  }

  .grid-services {
    @apply grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8;
  }

  /* Professional Spacing Utilities */
  .spacing-technology {
    @apply space-y-8 md:space-y-12 lg:space-y-16;
  }

  .spacing-content {
    @apply space-y-6 md:space-y-8 lg:space-y-10;
  }

  .spacing-section {
    @apply space-y-12 md:space-y-16 lg:space-y-20;
  }

  /* Enhanced Color Utilities */
  .enhanced-border {
    @apply border-border/50;
  }

  .enhanced-hover {
    @apply hover:bg-accent/50;
  }

  .enhanced-accent {
    @apply text-primary;
  }

  .bg-enhanced-accent {
    @apply bg-primary/10;
  }



  .text-enhanced-label {
    @apply text-foreground font-medium leading-none text-sm tracking-wide;
  }

  /* Visual Hierarchy Spacing */
  .section-spacing {
    @apply py-16 md:py-20 lg:py-24;
  }

  .section-spacing-sm {
    @apply py-12 md:py-16 lg:py-20;
  }

  .content-spacing {
    @apply space-y-6 md:space-y-8;
  }

  /* Enhanced Card Hierarchy */
  .card-primary {
    @apply medical-card border-primary/20 shadow-lg hover:shadow-xl;
  }

  .card-secondary {
    @apply medical-card border-border/30 shadow-md hover:shadow-lg;
  }

  .card-accent {
    @apply medical-card bg-accent/50 border-accent/30 shadow-sm hover:shadow-md;
  }

  /* Medical Content Cards - Enhanced for Dark Theme */
  .medical-content-card {
    @apply bg-card border border-border/40 rounded-xl shadow-lg;
    backdrop-filter: blur(12px);
    position: relative;
  }

  .dark .medical-content-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.95) 100%);
    border-color: hsl(var(--border) / 0.6);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .medical-image-container {
    @apply rounded-lg overflow-hidden border border-border/30;
    background: hsl(var(--background));
  }

  .dark .medical-image-container {
    background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--card)) 100%);
    border-color: hsl(var(--border) / 0.5);
  }

  .link-enhanced-subtle {
    @apply text-foreground/80 hover:text-primary transition-colors duration-200;
  }

  /* Professional Button Styling */
  .btn-enhanced {
    @apply px-6 py-3 rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn-enhanced bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-105 focus:ring-primary shadow-md hover:shadow-lg;
  }

  .btn-secondary {
    @apply btn-enhanced bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-105 focus:ring-secondary shadow-md hover:shadow-lg;
  }

  .btn-outline {
    @apply btn-enhanced border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground hover:scale-105 focus:ring-primary;
  }

  .btn-ghost {
    @apply btn-enhanced text-primary hover:bg-primary/10 hover:scale-105 focus:ring-primary;
  }

  /* Enhanced Badge Styling */
  .badge-enhanced {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }

  .badge-primary {
    @apply badge-enhanced bg-primary/10 text-primary border border-primary/20;
  }

  .badge-secondary {
    @apply badge-enhanced bg-secondary/10 text-secondary-foreground border border-secondary/20;
  }

  .badge-success {
    @apply badge-enhanced bg-green-100 text-green-800 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800;
  }

  .badge-warning {
    @apply badge-enhanced bg-yellow-100 text-yellow-800 border border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800;
  }
}

  /* Enhanced Mobile Components */
  .mobile-card {
    @apply bg-card rounded-xl shadow-sm border border-border/40 p-6 transition-all duration-300;
    backdrop-filter: blur(8px);
  }

  .mobile-button {
    @apply min-h-[48px] min-w-[48px] touch-manipulation select-none transition-all duration-200 active:scale-95;
    padding: 12px 24px;
    font-weight: 600;
  }

  .mobile-input {
    @apply min-h-[48px] text-base touch-manipulation rounded-lg border-2 border-border/50 focus:border-primary;
    padding: 12px 16px;
  }

  .mobile-text {
    @apply text-base leading-relaxed;
    line-height: 1.6;
  }

  .mobile-heading {
    @apply text-2xl font-bold leading-tight;
    margin-bottom: 1rem;
  }

  .mobile-subheading {
    @apply text-xl font-semibold leading-snug;
    margin-bottom: 0.75rem;
  }

  /* Enhanced Touch-friendly utilities */
  .touch-target {
    @apply min-h-[48px] min-w-[48px] flex items-center justify-center;
    position: relative;
  }

  .touch-feedback {
    @apply transition-all duration-150 active:scale-95 touch-manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .touch-feedback:active {
    transform: scale(0.95);
    opacity: 0.8;
  }

  .swipe-container {
    @apply overflow-x-auto scrollbar-hide touch-pan-x;
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: x mandatory;
  }

  .swipe-item {
    scroll-snap-align: start;
    flex-shrink: 0;
  }

  .wave-animation {
    animation: wave 12s linear infinite;
    animation-delay: -2s;
    transform-origin: center bottom;
  }

  .page-transition-enter {
    opacity: 0;
    transform: translateY(10px);
  }

  .page-transition-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 400ms, transform 400ms;
  }

  .page-transition-exit {
    opacity: 1;
  }

  .page-transition-exit-active {
    opacity: 0;
    transition: opacity 300ms;
  }

  /* Mobile-specific utilities */
  .mobile-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  .mobile-scroll-smooth {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  .mobile-no-scroll {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  .mobile-backdrop {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Hide scrollbars on mobile */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Touch-specific styles */
  @media (hover: none) and (pointer: coarse) {
    .hover-only {
      display: none;
    }

    .touch-only {
      display: block;
    }

    /* Larger touch targets on touch devices */
    button, a, input, select, textarea {
      min-height: 44px;
    }

    /* Remove hover effects on touch devices */
    .hover\:scale-105:hover {
      transform: none;
    }

    .hover\:shadow-lg:hover {
      box-shadow: none;
    }
  }

  @media (hover: hover) and (pointer: fine) {
    .touch-only {
      display: none;
    }

    .hover-only {
      display: block;
    }
  }

  /* Enhanced Mobile typography improvements */
  @media (max-width: 640px) {
    h1 {
      font-size: 2.25rem;
      line-height: 2.5rem;
      word-wrap: break-word;
      hyphens: auto;
      margin-bottom: 1.5rem;
    }

    h2 {
      font-size: 1.875rem;
      line-height: 2.25rem;
      margin-bottom: 1.25rem;
    }

    h3 {
      font-size: 1.5rem;
      line-height: 1.875rem;
      margin-bottom: 1rem;
    }

    h4 {
      font-size: 1.25rem;
      line-height: 1.625rem;
      margin-bottom: 0.875rem;
    }

    p {
      font-size: 1rem;
      line-height: 1.6rem;
      word-wrap: break-word;
      margin-bottom: 1rem;
    }

    .lead {
      font-size: 1.125rem;
      line-height: 1.75rem;
      margin-bottom: 1.5rem;
    }

    .btn-primary, .btn-secondary, .btn-outline {
      padding: 14px 28px;
      font-size: 1rem;
      min-height: 48px;
    }
  }

  /* Responsive text overflow prevention */
  .text-responsive {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Breadcrumb responsive improvements */
  .breadcrumb-responsive {
    word-break: break-word;
    overflow-wrap: break-word;
  }

  /* Accessibility Enhancements */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .skip-link {
    position: absolute;
    top: -100px;
    left: 6px;
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
    opacity: 0;
    pointer-events: none;
  }

  .skip-link:focus {
    top: 6px;
    opacity: 1;
    pointer-events: auto;
  }

  /* Focus management */
  .keyboard-navigation *:focus {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  .keyboard-navigation button:focus,
  .keyboard-navigation a:focus,
  .keyboard-navigation input:focus,
  .keyboard-navigation select:focus,
  .keyboard-navigation textarea:focus {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :root {
      --border: 0 0% 0%;
      --input: 0 0% 0%;
    }

    .dark {
      --border: 0 0% 100%;
      --input: 0 0% 100%;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }

    .wave-animation {
      animation: none;
    }
  }

  /* Defensive CSS against external tool interference */
  /* Prevent external tools from affecting our layout */
  body > div:not(#root),
  body > iframe:not([src*="mineuro.com.au"]),
  body > [class*="extension-"],
  body > [id*="extension-"],
  body > [class*="widget-"],
  body > [id*="widget-"] {
    z-index: 999999 !important;
    pointer-events: auto !important;
  }

  /* Ensure our content stays properly aligned */
  #root {
    position: relative;
    z-index: 1;
  }

  /* Prevent external styles from affecting our buttons */
  .btn-primary,
  .medical-card,
  .glass-card {
    position: relative !important;
    z-index: auto !important;
  }

  /* Performance optimizations */
  .will-change-transform {
    will-change: transform;
  }

  .will-change-opacity {
    will-change: opacity;
  }

  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Loading states */
  .loading-skeleton {
    background: linear-gradient(90deg,
      hsl(var(--muted)) 25%,
      hsl(var(--muted-foreground) / 0.1) 50%,
      hsl(var(--muted)) 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 2s infinite;
  }

  @keyframes loading-shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }

    body {
      background: white !important;
      color: black !important;
    }

    a[href]:after {
      content: " (" attr(href) ")";
    }

    .page-break {
      page-break-before: always;
    }
  }
