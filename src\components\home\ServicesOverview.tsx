import React from 'react';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  ServiceCard as SharedServiceCard,
  GridLayout,
  ResponsiveSection,
  ResponsiveContainer
} from '@/components/shared';
import {
  MinimallyInvasiveIcon,
  ImagingNavigationIcon,
  SurgicalAdvantagesIcon,
  RoboticSurgeryIcon
} from '@/components/shared/MedicalIcons';
import { DeviceInfo } from '@/contexts/DeviceContext';
import type { ServicesOverviewProps } from '@/types/homepage';

/**
 * Services Overview Component
 * Displays the comprehensive services overview section with technology cards
 * Preserves all original content and styling from Index.tsx lines 137-437
 * Now uses Foundation Phase shared components for consistency
 */
const ServicesOverview: React.FC<ServicesOverviewProps> = ({ deviceInfo, services }) => {
  return (
    <ResponsiveSection background="card" spacing="md">
      <ResponsiveContainer size="lg">
        <SectionHeader
          subtitle="Advanced Technology"
          title="Cutting-Edge Neurosurgical Technologies"
          description="Medical technology advances facilitated less invasive procedures and treatment of brain and spine lesions previously considered inoperable due to their size or critical location. This approach provides many benefits for our patients, including minimally-invasive access, increased accuracy, reduced surgery time, faster recovery times, and earlier return to normal activities."
          centered={true}
        />

        <GridLayout columns={4} gap="lg">
          {services.map((service) => (
            <ServiceCard
              key={service.id}
              service={service}
              deviceInfo={deviceInfo}
            />
          ))}
        </GridLayout>
      </ResponsiveContainer>
    </ResponsiveSection>
  );
};

/**
 * Individual Service Card Component
 * Renders each service with its icon, title, description, and link
 * Now uses Foundation Phase shared components for consistency
 */
interface ServiceCardProps {
  service: {
    id: string;
    title: string;
    description: string;
    icon: string;
    link: string;
    animationDelay: string;
    gradientColors: string;
  };
  deviceInfo?: DeviceInfo; // Optional since it's not used in the component
}

const ServiceCard: React.FC<ServiceCardProps> = ({ service, deviceInfo: _deviceInfo }) => {
  const renderIcon = (iconType: string) => {
    const iconClassName = "w-12 h-12 text-primary drop-shadow-sm transition-all duration-300";

    switch (iconType) {
      case 'minimal-invasive':
        return <MinimallyInvasiveIcon className={iconClassName} />;

      case 'imaging-navigation':
        return <ImagingNavigationIcon className={iconClassName} />;

      case 'advantages':
        return <SurgicalAdvantagesIcon className={iconClassName} />;

      case 'robotic-surgery':
        return <RoboticSurgeryIcon className={iconClassName} />;

      default:
        return null;
    }
  };

  return (
    <SharedServiceCard
      title={service.title}
      description={service.description}
      icon={renderIcon(service.icon)}
      link={service.link}
      linkText="Learn More"
      animationDelay={service.animationDelay}
      gradientColors={service.gradientColors}
    />
  );
};

ServicesOverview.displayName = 'ServicesOverview';

export default ServicesOverview;
