import React from 'react';

import SafeImage from '@/components/SafeImage';

const DetailedProcessSection: React.FC = () => {
  return (
    <section className="py-16">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">Unbiased Independent Expert Review</h2>
          <p className="text-muted-foreground max-w-4xl mx-auto">
            Medical examinations form a crucial part of the personal injury and compensation claim assessment. 
            Lawyers, insurers or other parties can request them. They should be performed by the unbiased independent 
            experts who have appropriate qualifications, deep knowledge of the problem and are specifically trained 
            to provide impairment ratings.
          </p>
        </div>

        {/* Before Appointment Section */}
        <div className="mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-enhanced-heading text-2xl font-bold mb-4 text-primary">Preparation in Comfort</h3>
              <h4 className="text-xl font-semibold mb-4">Before the Medicolegal Appointment</h4>
              <p className="text-muted-foreground mb-6">
                As many of our clients suffer from chronic pain and psychological problems from their injuries, 
                we want to reduce the discomfort of the examination and collect as much information about the 
                injury and its consequences well in advance. We understand that undergoing a medicolegal assessment 
                and going through the details of the injury over and over again can cause additional distress.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Registration form, health questionnaire and consent form provided well in advance</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Assistance with reading/writing problems available</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Support person, carer or legal guardian welcome</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Professional interpreter can be arranged if required</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Chaperone available during examination</span>
                </li>
              </ul>
            </div>
            <div className="relative rounded-xl overflow-hidden shadow-lg">
              <SafeImage
                src="/images/welcoming-environment-for-elderly-and-disabled-patients.jpg"
                alt="Comfortable preparation environment"
                className="w-full h-auto"
                fallbackSrc="/images/medical-consulting.jpg"
              />
            </div>
          </div>
        </div>

        {/* During Appointment Section */}
        <div className="mb-16 bg-primary/5 p-8 rounded-xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="order-2 lg:order-1 relative rounded-xl overflow-hidden shadow-lg">
              <SafeImage
                src="/images/gp-resources/neuro-exam.jpg"
                alt="During the appointment examination"
                className="w-full h-auto"
                fallbackSrc="/images/medical-consulting.jpg"
              />
            </div>
            <div className="order-1 lg:order-2">
              <h3 className="text-enhanced-heading text-2xl font-bold mb-4 text-primary">Attention to Detail</h3>
              <h4 className="text-xl font-semibold mb-4">During the Appointment</h4>
              <p className="text-muted-foreground mb-6">
                The appointment usually consists of 4 parts: explanation of the purpose of the assessment, 
                interview, examination, and time for any comments and conclusion.
              </p>
              <div className="space-y-4">
                <div>
                  <h5 className="font-semibold text-primary mb-2">Interview Topics:</h5>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• History of relevant pre-existing medical conditions</li>
                    <li>• Educational background and work history</li>
                    <li>• Accident circumstances that caused the injury</li>
                    <li>• Current complaints, symptoms, and work capacity</li>
                    <li>• Impact on daily living and quality of life</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-semibold text-primary mb-2">Examination:</h5>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• Neurological examination focused on injured area</li>
                    <li>• Testing reflexes, sensation, and joint movement</li>
                    <li>• Discrete photography for documentation if needed</li>
                    <li>• No additional pain expected during examination</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* After Appointment Section */}
        <div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-enhanced-heading text-2xl font-bold mb-4 text-primary">Highest Quality Reports</h3>
              <h4 className="text-xl font-semibold mb-4">After the Appointment</h4>
              <p className="text-muted-foreground mb-6">
                Medicolegal reports provide significantly more detail than the usual specialist's letters. 
                They are legal documents which can be used as evidence in courts. In some complex cases, 
                reports can be 30-50 pages long.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Comprehensive analysis of all evidence and literature</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Reports suitable for presentation to judge and jury</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Several weeks preparation time for complex cases</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Reports sent directly to requesting party</span>
                </li>
                <li className="flex items-start">
                  <span className="text-primary mr-2">•</span>
                  <span className="text-muted-foreground">Can be forwarded to claimants or GPs for treatment planning</span>
                </li>
              </ul>
            </div>
            <div className="relative rounded-xl overflow-hidden shadow-lg">
              <SafeImage
                src="/images/law-neurosurgery-JME-IME-assessment-report-spine-brain-insurance-claim-medicolegal.png"
                alt="High quality medicolegal reports"
                className="w-full h-auto"
                fallbackSrc="/images/medical-consulting.jpg"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

DetailedProcessSection.displayName = 'DetailedProcessSection';

export default DetailedProcessSection;
