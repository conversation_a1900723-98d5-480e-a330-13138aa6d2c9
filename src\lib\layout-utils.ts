import React from 'react';

// Note: Import StandardPageLayout when using these utilities
// import StandardPageLayout, { StandardPageLayoutProps } from '@/components/StandardPageLayout';

interface StandardPageLayoutProps {
  children: React.ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
  containerClassName?: string;
  className?: string;
  contentClassName?: string;
}

/**
 * Higher-order component for wrapping pages with standard layout
 * Note: You need to import StandardPageLayout component to use this function
 */
export function withStandardLayout(
  Component: React.ComponentType<Record<string, unknown>>,
  StandardPageLayoutComponent: React.ComponentType<StandardPageLayoutProps>,
  layoutProps?: Partial<StandardPageLayoutProps>
) {
  const WrappedComponent = (props: Record<string, unknown>) =>
    React.createElement(StandardPageLayoutComponent, layoutProps,
      React.createElement(Component, props)
    );

  WrappedComponent.displayName = `withStandardLayout(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

/**
 * Layout factory function for creating custom layouts
 * Note: You need to import StandardPageLayout component to use this function
 */
export function createLayoutVariant(
  name: string,
  StandardPageLayoutComponent: React.ComponentType<StandardPageLayoutProps>,
  defaultProps: Partial<StandardPageLayoutProps>
) {
  const LayoutVariant = (props: Omit<StandardPageLayoutProps, keyof typeof defaultProps>) =>
    React.createElement(StandardPageLayoutComponent, { ...defaultProps, ...props });

  LayoutVariant.displayName = `${name}Layout`;

  return LayoutVariant;
}

/**
 * Common layout configurations
 */
export const layoutConfigs = {
  /**
   * Standard page layout with header
   */
  standard: {
    showHeader: true,
    showFooter: true,
    containerClassName: 'container mx-auto px-4'
  },

  /**
   * Full-width layout without container
   */
  fullWidth: {
    showHeader: true,
    showFooter: true,
    containerClassName: ''
  },

  /**
   * Minimal layout for landing pages
   */
  minimal: {
    showHeader: false,
    showFooter: false,
    containerClassName: ''
  },

  /**
   * Centered layout for forms and auth pages
   */
  centered: {
    showHeader: true,
    showFooter: true,
    containerClassName: 'container mx-auto px-4 max-w-2xl'
  }
};

/**
 * Layout utility functions
 */
export const layoutUtils = {
  /**
   * Get responsive container classes
   */
  getContainerClasses: (variant: 'standard' | 'wide' | 'narrow' | 'full' = 'standard') => {
    const baseClasses = 'mx-auto px-4';
    
    switch (variant) {
      case 'wide':
        return `${baseClasses} max-w-7xl`;
      case 'narrow':
        return `${baseClasses} max-w-4xl`;
      case 'full':
        return 'w-full';
      default:
        return `${baseClasses} container`;
    }
  },

  /**
   * Get responsive spacing classes
   */
  getSpacingClasses: (size: 'sm' | 'md' | 'lg' | 'xl' = 'md') => {
    switch (size) {
      case 'sm':
        return 'py-8';
      case 'lg':
        return 'py-20';
      case 'xl':
        return 'py-24';
      default:
        return 'py-16';
    }
  },

  /**
   * Get grid classes for responsive layouts
   */
  getGridClasses: (columns: { sm?: number; md?: number; lg?: number; xl?: number }) => {
    const classes = ['grid', 'gap-6'];
    
    if (columns.sm) classes.push(`grid-cols-${columns.sm}`);
    if (columns.md) classes.push(`md:grid-cols-${columns.md}`);
    if (columns.lg) classes.push(`lg:grid-cols-${columns.lg}`);
    if (columns.xl) classes.push(`xl:grid-cols-${columns.xl}`);
    
    return classes.join(' ');
  }
};
