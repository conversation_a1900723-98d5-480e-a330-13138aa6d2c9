import React from 'react';

interface FAQSectionProps {
  faq: {
    title: string;
    questions: {
      question: string;
      answer: string;
    }[];
  };
}

const FAQSection: React.FC<FAQSectionProps> = ({ faq }) => {
  return (
    <section className="py-16">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{faq.title}</h2>
          <p className="text-enhanced-body max-w-3xl mx-auto">
            Common questions about our medicolegal services and assessment process
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {faq.questions.map((faqItem, index) => (
            <div key={index} className="medical-card p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-3 text-enhanced-subheading">{faqItem.question}</h3>
              <p className="text-enhanced-body">
                {faqItem.answer}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

FAQSection.displayName = 'FAQSection';

export default FAQSection;
