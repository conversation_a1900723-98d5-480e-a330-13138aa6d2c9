import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import AppointmentCallToActionSection from '@/components/patient-resources/AppointmentCallToActionSection';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';
import {
  Brain,
  Bone,
  Network,
  Dumbbell,
  Heart,
  HelpCircle,
  ArrowRight,
  CheckCircle,
  Calculator,
  TrendingUp,
  Target
} from 'lucide-react';

/**
 * Neurosurgical Intelligence Hub - Patient Resources
 * World-leading patient empowerment platform combining expert knowledge,
 * interactive tools, and personalised care pathways
 */

const PatientResources: React.FC = () => {
  const deviceInfo = useDeviceDetection();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Assessment and support tools
  const assessmentTools = [
    {
      id: 'symptom-assessment',
      title: 'Symptom Assessment Tool',
      description: 'Structured assessment to help evaluate your symptoms and determine appropriate care.',
      icon: Brain,
      type: 'assessment',
      timeToComplete: '5-10 min',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Symptom Analysis', 'Severity Assessment', 'Care Recommendations', 'Specialist Guidance']
    },
    {
      id: 'treatment-information',
      title: 'Treatment Information Guide',
      description: 'Comprehensive information about treatment options and expected outcomes.',
      icon: Calculator,
      type: 'information',
      timeToComplete: '10-15 min',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Treatment Options', 'Outcome Information', 'Recovery Expectations', 'Risk Factors']
    },
    {
      id: 'decision-guide',
      title: 'Treatment Decision Guide',
      description: 'Structured approach to understanding your treatment choices and considerations.',
      icon: Target,
      type: 'decision',
      timeToComplete: '15-20 min',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Decision Framework', 'Key Considerations', 'Expert Guidance', 'Personalised Information']
    },
    {
      id: 'recovery-support',
      title: 'Recovery Support Tools',
      description: 'Resources and tools to support your recovery and ongoing health management.',
      icon: TrendingUp,
      type: 'support',
      timeToComplete: 'Ongoing',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Recovery Resources', 'Progress Monitoring', 'Support Networks', 'Health Management']
    }
  ];

  // Specialised condition areas
  const conditionAreas = [
    {
      id: 'brain-conditions',
      title: 'Brain Conditions',
      description: 'Comprehensive information about brain tumours, neurological disorders, and treatment approaches.',
      icon: Brain,
      link: '/patient-resources/brain-conditions',
      stats: '15 Conditions',
      highlight: 'Neurosurgical Expertise',
      features: ['Tumour Management', 'Surgical Options', 'Recovery Support', 'Risk Information'],
      outcomes: 'Excellent outcomes',
      experience: 'Extensive experience'
    },
    {
      id: 'spine-conditions',
      title: 'Spine Conditions',
      description: 'Complete information about spine health, conditions, and modern treatment options.',
      icon: Bone,
      link: '/patient-resources/spine-conditions',
      stats: '15 Conditions',
      highlight: 'Minimally Invasive Techniques',
      features: ['Advanced Surgery', 'Motion Preservation', 'Pain Management', 'Recovery Support'],
      outcomes: 'Excellent outcomes',
      experience: 'Extensive experience'
    },
    {
      id: 'nerve-conditions',
      title: 'Peripheral Nerve Conditions',
      description: 'Specialised information about peripheral nerve disorders and microsurgical treatments.',
      icon: Network,
      link: '/patient-resources/peripheral-nerve-conditions',
      stats: '8 Conditions',
      highlight: 'Microsurgical Expertise',
      features: ['Nerve Repair', 'Function Restoration', 'Pain Relief', 'Mobility Improvement'],
      outcomes: 'Excellent outcomes',
      experience: 'Extensive experience'
    }
  ];

  // Additional support resources
  const supportResources = [
    {
      category: 'Exercise & Rehabilitation',
      icon: Dumbbell,
      title: 'Exercise Library',
      description: 'Evidence-based exercise programmes designed for spine health and safe rehabilitation.',
      link: '/patient-resources/exercise-library',
      features: ['6 Specialised Programmes', 'Video Demonstrations', 'Progress Tracking', 'Safety Guidelines'],
      highlight: 'Spine-Safe Exercises'
    },
    {
      category: 'Health & Wellness',
      icon: Heart,
      title: 'Brain and Spine Health',
      description: 'Comprehensive health information covering anatomy, lifestyle, and recovery support.',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Lifestyle Information', 'Nutrition Guidance', 'Mental Health Support', 'Recovery Resources'],
      highlight: 'Holistic Health Approach'
    },
    {
      category: 'Support & Information',
      icon: HelpCircle,
      title: 'FAQ and Support',
      description: 'Frequently asked questions, emergency information, and comprehensive patient support.',
      link: '/faq',
      features: ['Emergency Information', 'Common Questions', 'Support Resources', 'Preparation Guides'],
      highlight: 'Comprehensive Support'
    }
  ];

  return (
    <StandardPageLayout showHeader={false}>
      <PageHeader
        title="Patient Resources"
        subtitle="Comprehensive medical information, assessment tools, and support for your healthcare journey"
        backgroundImage="/images/patient-resources/spine-health-hero.jpg"
        enableParallax={true}
      />

      <main className="flex-1">




        {/* Condition Areas */}
        <section className="py-16 bg-gradient-to-br from-background to-primary/5">
          <div className="container max-w-7xl">
            <div className="text-center mb-12">
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4">
                Condition Information
              </h2>
              <p className="text-enhanced-body max-w-3xl mx-auto">
                Comprehensive information about neurological and spine conditions, treatment options, and recovery.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
              {conditionAreas.map((area) => {
                const IconComponent = area.icon;
                return (
                  <Card
                    key={area.id}
                    className="group hover:shadow-xl transition-all duration-300 border-2 hover:border-primary/20"
                  >
                    <CardHeader className="pb-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="p-3 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                          <IconComponent className="h-10 w-10 text-primary" />
                        </div>
                        <div className="text-right">
                          <Badge variant="secondary" className="mb-1">{area.outcomes}</Badge>
                          <div className="text-xs text-muted-foreground">{area.experience}</div>
                        </div>
                      </div>
                      <CardTitle className="text-xl font-bold group-hover:text-primary transition-colors mb-2">
                        {area.title}
                      </CardTitle>
                      <CardDescription className="text-base leading-relaxed mb-4">
                        {area.description}
                      </CardDescription>
                      <Badge className="bg-primary/10 text-primary border-primary/20 mb-4">
                        {area.highlight}
                      </Badge>
                    </CardHeader>

                    <CardContent className="pb-6">
                      <div className="grid grid-cols-2 gap-3 mb-4">
                        {area.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                            <span className="text-sm text-muted-foreground">{feature}</span>
                          </div>
                        ))}
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Conditions covered:</span>
                        <span className="font-medium">{area.stats}</span>
                      </div>
                    </CardContent>

                    <CardFooter>
                      <Button asChild className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                        <Link to={area.link} className="flex items-center justify-center space-x-2">
                          <span>View {area.title}</span>
                          <ArrowRight className="h-4 w-4" />
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Additional Resources */}
        <section className="py-16 bg-muted/30">
          <div className="container max-w-7xl">
            <div className="text-center mb-12">
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4">
                Additional Support Resources
              </h2>
              <p className="text-enhanced-body max-w-3xl mx-auto">
                Comprehensive support resources to assist you throughout your healthcare journey.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
              {supportResources.map((resource, index) => {
                const IconComponent = resource.icon;
                return (
                  <Card
                    key={index}
                    className="group hover:shadow-xl transition-all duration-500 border-2 hover:border-primary/30"
                  >
                    <CardHeader className="pb-6">
                      <Badge variant="outline" className="mb-3 w-fit">{resource.category}</Badge>
                      <div className="p-4 rounded-xl bg-primary/10 group-hover:bg-primary/20 transition-colors w-fit mb-4">
                        <IconComponent className="h-10 w-10 text-primary" />
                      </div>
                      <CardTitle className="text-2xl font-bold group-hover:text-primary transition-colors mb-2">
                        {resource.title}
                      </CardTitle>
                      <CardDescription className="text-base leading-relaxed mb-4">
                        {resource.description}
                      </CardDescription>
                      <Badge className="bg-primary/10 text-primary border-primary/20">
                        {resource.highlight}
                      </Badge>
                    </CardHeader>

                    <CardContent className="pb-6">
                      <div className="space-y-3">
                        {resource.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                            <span className="text-sm text-muted-foreground">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>

                    <CardFooter>
                      <Button asChild size="lg" className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                        <Link to={resource.link} className="flex items-center justify-center space-x-2">
                          <span>Access {resource.category}</span>
                          <ArrowRight className="h-5 w-5" />
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>

            {/* Individual Spine Health Programme Highlight */}
            <Card className="bg-gradient-to-br from-primary/5 to-background border-2 border-primary/20">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center p-8">
                <div className="relative rounded-lg overflow-hidden shadow-lg">
                  <img
                    src="/images/individual-spine-health-1.jpg"
                    alt="Individual Spine Health Programme"
                    className="w-full h-auto"
                  />
                </div>
                <div className="space-y-6">
                  <div>
                    <Badge className="mb-4 bg-primary/10 text-primary border-primary/20">
                      Featured Programme
                    </Badge>
                    <h3 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">
                      Individual Spine Health Programme
                    </h3>
                    <p className="text-muted-foreground leading-relaxed mb-4">
                      Our comprehensive programme combines assessment tools, personalised recommendations,
                      and progress tracking to support your spine health journey.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      'Comprehensive symptom assessment',
                      'Personalised treatment information',
                      'Progress tracking tools',
                      'Expert guidance integration',
                      'Evidence-based recommendations',
                      'Outcome monitoring'
                    ].map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <div className={cn(
                    "flex gap-4",
                    deviceInfo.isMobile ? "flex-col" : "flex-row"
                  )}>
                    <Button asChild size="lg">
                      <Link to="/patient-resources/individual-spine-health-programme">
                        Access Programme
                      </Link>
                    </Button>
                    <Button asChild variant="outline" size="lg">
                      <Link to="/patient-resources/brain-and-spine-health">
                        View Assessment Tools
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </section>

        {/* Assessment Tools */}
        <section className="py-16 bg-muted/30">
          <div className="container max-w-7xl">
            <div className="text-center mb-12">
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4">
                Assessment and Decision Support Tools
              </h2>
              <p className="text-enhanced-body max-w-3xl mx-auto">
                Evidence-based tools to help you understand your condition and explore treatment options.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
              {assessmentTools.map((tool) => {
                const IconComponent = tool.icon;
                return (
                  <Card
                    key={tool.id}
                    className="group hover:shadow-xl transition-all duration-300 border-2 hover:border-primary/20"
                  >
                    <CardHeader className="pb-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="p-3 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                          <IconComponent className="h-8 w-8 text-primary" />
                        </div>
                        <div className="text-right">
                          <div className="text-xs text-muted-foreground">{tool.timeToComplete}</div>
                        </div>
                      </div>
                      <CardTitle className="text-xl font-bold group-hover:text-primary transition-colors mb-2">
                        {tool.title}
                      </CardTitle>
                      <CardDescription className="text-base leading-relaxed">
                        {tool.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="pb-6">
                      <div className="grid grid-cols-2 gap-3">
                        {tool.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                            <span className="text-sm text-muted-foreground">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>

                    <CardFooter>
                      <Button asChild className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                        <Link to={tool.link} className="flex items-center justify-center space-x-2">
                          <span>Access {tool.title}</span>
                          <ArrowRight className="h-4 w-4" />
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Appointment Call to Action */}
        <AppointmentCallToActionSection
          title="Ready to Take the Next Step?"
          description="Book a consultation with our expert neurosurgical team to discuss your condition and explore treatment options tailored to your needs."
          primaryButtonText="Book Appointment"
          primaryButtonLink="/appointments"
          secondaryButtonText="Contact Us"
          secondaryButtonLink="/contact"
        />
      </main>
    </StandardPageLayout>
  );
};

PatientResources.displayName = 'PatientResources';

export default PatientResources;
