import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';

import IndependentReviewsSection from '@/components/IndependentReviewsSection';
import { TestWrapper } from '@/lib/test-utils';
import { setupAllStandardMocks } from '@/tests/utils/standard-mocks';

// Setup standardized mocks
setupAllStandardMocks();

describe('IndependentReviewsSection', () => {
  it('renders review platforms and patient testimonials', () => {
    render(
      <TestWrapper>
        <IndependentReviewsSection />
      </TestWrapper>
    );

    // Check for main heading (actual content is "What Our Patients Say")
    expect(screen.getByRole('heading', { name: /what our patients say/i })).toBeInTheDocument();

    // Check for review platforms (check for actual link text)
    expect(screen.getByRole('link', { name: /view reviews on google/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /view reviews on birdeye/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /view reviews on ratemds/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /view reviews on trustindex/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /view reviews on threebestrated/i })).toBeInTheDocument();

    // Check for description text (fallback content)
    expect(screen.getByText(/dr\. aliashkevich has received excellent feedback/i)).toBeInTheDocument();

    // Check for "Read More" button
    expect(screen.getByRole('link', { name: /read more patient reviews on google/i })).toBeInTheDocument();
  });

  it('displays ratings for each platform', () => {
    render(
      <TestWrapper>
        <IndependentReviewsSection />
      </TestWrapper>
    );

    // Check that ratings are displayed (should be immediately available)
    expect(screen.getAllByText('4.8')).toHaveLength(2); // Google and BirdEye both have 4.8
    expect(screen.getByText('4.9')).toBeInTheDocument(); // TrustIndex

    // Check for the "/ 5.0" text that appears in the rating display
    expect(screen.getAllByText('/ 5.0')).toHaveLength(5); // All platforms show "/ 5.0"
  });

  it('has proper accessibility attributes', async () => {
    render(
      <TestWrapper>
        <IndependentReviewsSection />
      </TestWrapper>
    );

    // Check for section with proper ARIA labeling (actual content is "What Our Patients Say")
    const section = screen.getByRole('region', { name: /what our patients say/i });
    expect(section).toBeInTheDocument();

    // Check for proper heading structure
    const mainHeading = screen.getByRole('heading', { level: 2, name: /what our patients say/i });
    expect(mainHeading).toBeInTheDocument();
  });
});