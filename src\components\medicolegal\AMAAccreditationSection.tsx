import React from 'react';

import SafeImage from '@/components/SafeImage';

const AMAAccreditationSection: React.FC = () => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">AMA Accreditation</h2>
          <h3 className="text-2xl font-semibold mb-6 text-primary">Medicolegal Impairment Assessment</h3>
          <p className="text-muted-foreground max-w-4xl mx-auto">
            Dr <PERSON> is certified by the Australian Medical Association (AMA) Victoria and the Personal Injury Education Foundation (PIEF) 
            in Impairment Assessment using the American Medical Association Guides to the Evaluation of Permanent Impairment 4th Edition and other 
            prescribed methods, as applied to relevant Victorian legislation. He completed courses to assess impairments for TAC, WorkCover and 
            the purposes of Part VBA of the Wrongs Act 1958 (personal injury).
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
          <div>
            <p className="text-muted-foreground mb-6">
              He is accredited for assessing the spine using both the methodology from Chapter 3 of the Guides and modification to those 
              criteria by the Spinal Impairment Guides Modification Document (SIGMD). He can perform an expert apportionment to disentangle 
              what impairment may or may not be related to a compensable event.
            </p>
            
            <div className="space-y-4">
              <div className="medical-card p-4 rounded-lg shadow-md">
                <h4 className="font-semibold text-primary mb-2">Certification Areas:</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• AMA Guides 4th Edition methodology</li>
                  <li>• Spinal Impairment Guides Modification Document (SIGMD)</li>
                  <li>• TAC impairment assessments</li>
                  <li>• WorkCover assessments</li>
                  <li>• Wrongs Act 1958 Part VBA assessments</li>
                  <li>• Expert apportionment analysis</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="relative rounded-xl overflow-hidden shadow-lg">
            <SafeImage
              src="/images/law-AMA-4th-edition-medicolegal-miNEURO-work-injury-accident-traffic-TAC-WorkCover-impairment-assessment.jpg"
              alt="AMA Guides and impairment assessment"
              className="w-full h-auto"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>
        </div>

        {/* Professional Logos */}
        <div className="text-center">
          <h4 className="text-lg font-semibold mb-8 text-primary">Professional Accreditations</h4>
          <div className="flex flex-wrap justify-center items-center gap-8 md:gap-12">
            <div className="bg-background p-4 rounded-lg shadow-md">
              <SafeImage
                src="/images/ama-victoria-logo.jpg"
                alt="AMA Victoria Logo"
                className="h-16 w-auto object-contain"
                fallbackSrc="/images/placeholder-logo.png"
              />
            </div>
            <div className="bg-background p-4 rounded-lg shadow-md">
              <SafeImage
                src="/images/PIEF-logo.jpg"
                alt="PIEF Logo"
                className="h-16 w-auto object-contain"
                fallbackSrc="/images/placeholder-logo.png"
              />
            </div>
            <div className="bg-background p-4 rounded-lg shadow-md">
              <SafeImage
                src="/images/iatvic-logo.jpg"
                alt="IAT Victoria Logo"
                className="h-16 w-auto object-contain"
                fallbackSrc="/images/placeholder-logo.png"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

AMAAccreditationSection.displayName = 'AMAAccreditationSection';

export default AMAAccreditationSection;
