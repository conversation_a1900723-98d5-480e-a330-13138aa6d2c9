import React from 'react';
import { Link } from 'react-router-dom';

import { But<PERSON> } from '@/components/ui/button';
import type { ImageGuidedSurgeryProps } from '@/types/homepage';

/**
 * Image-Guided and Robotic Surgery Section Component
 * Displays benefits and outcomes of advanced surgical technology
 * Preserves all original content and styling from Index.tsx lines 805-854
 */
const ImageGuidedSurgerySection: React.FC<ImageGuidedSurgeryProps> = ({ keyBenefits, patientOutcomes }) => {
  return (
    <section className="section">
      <div className="container">
        <div className="text-center mb-12">
          <span className="text-primary font-medium uppercase tracking-wider text-sm">
            KEYHOLE NON-INVASIVE APPROACHES
          </span>
          <h2 className="text-enhanced-heading text-3xl md:text-enhanced-heading text-4xl font-bold mt-2 mb-6">Image-Guided and Robotic Surgery</h2>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            Image-guided and robotic surgery allows accurate planning of the procedure before the patient enters the operating theatre. It can often be performed in difficult clinical scenarios when open surgery is deemed too risky, e.g. in elderly, obese or medically unfit patients.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <div>
            <h3 className="text-xl font-semibold mb-4">Key Benefits</h3>
            <ul className="space-y-2 text-muted-foreground">
              {keyBenefits.map((benefit, index) => (
                <li key={index}>– {benefit}</li>
              ))}
            </ul>
          </div>
          <div>
            <h3 className="text-xl font-semibold mb-4">Patient Outcomes</h3>
            <ul className="space-y-2 text-muted-foreground">
              {patientOutcomes.map((outcome, index) => (
                <li key={index}>– {outcome}</li>
              ))}
            </ul>
          </div>
        </div>

        <div className="bg-primary/5 p-8 rounded-lg text-center">
          <p className="text-muted-foreground mb-6">
            It causes less operative trauma and blood loss, resulting in reduced postoperative pain and scarring. It helps to achieve faster recovery from surgery by combining advanced technology with surgical expertise.
          </p>
          <Button asChild>
            <Link to="/expertise/robotic-spine-surgery">
              Learn More About Robotic Surgery
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

ImageGuidedSurgerySection.displayName = 'ImageGuidedSurgerySection';

export default ImageGuidedSurgerySection;
