/**
 * 🎯 STANDARDIZED MOCK SETUP
 * 
 * Centralised mock configuration for all integration tests
 * Ensures consistent mocking across the entire test suite
 */

import { vi } from 'vitest';

/**
 * Standard DeviceContext mock setup
 */
export const setupDeviceContextMocks = () => {
  vi.mock('@/contexts/DeviceContext', () => ({
    useDevice: () => ({
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      screenSize: 'desktop'
    }),
    useDeviceDetection: () => ({
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      isTouchDevice: false,
      hasHover: true,
      screenSize: 'desktop',
      orientation: 'landscape',
      pixelRatio: 1,
      isLoaded: true
    }),
    useDeviceLoaded: () => true,
    useIsMobile: () => false,
    useBreakpoint: () => 'desktop',
    withDeviceDetection: (Component: React.ComponentType<unknown>) => Component,
    DeviceProvider: ({ children }: { children: React.ReactNode }) => children
  }));
};

/**
 * Standard LanguageContext mock setup
 */
export const setupLanguageContextMocks = () => {
  vi.mock('@/contexts/LanguageContext', () => ({
    useLanguage: () => ({
      language: 'en',
      t: (key: string) => key, // Return the key as the translation
      isLanguageLoaded: true,
      setLanguage: vi.fn()
    }),
    LanguageProvider: ({ children }: { children: React.ReactNode }) => children
  }));
};

/**
 * Standard SEO hook mock setup
 */
export const setupSEOMocks = () => {
  vi.mock('@/hooks/useSEO', () => ({
    useSEO: () => ({
      title: 'Test Page',
      description: 'Test page description',
      keywords: 'test, page'
    }),
    generatePageSEO: () => ({
      title: 'Test Page',
      description: 'Test page description',
      keywords: 'test, page'
    })
  }));
};

/**
 * Standard Router mock setup
 */
export const setupRouterMocks = () => {
  vi.mock('react-router-dom', async () => {
    const actual = await vi.importActual('react-router-dom');
    return {
      ...actual,
      useNavigate: () => vi.fn(),
      useLocation: () => ({
        pathname: '/',
        search: '',
        hash: '',
        state: null,
        key: 'default'
      }),
      useParams: () => ({})
    };
  });
};

/**
 * Standard API mock setup
 */
export const setupAPIMocks = () => {
  // Mock fetch for API calls
  global.fetch = vi.fn(() =>
    Promise.resolve({
      ok: true,
      status: 200,
      json: () => Promise.resolve({ success: true }),
      text: () => Promise.resolve('Success'),
    } as Response)
  );
};

/**
 * Standard localStorage mock setup
 */
export const setupLocalStorageMocks = () => {
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  };

  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
  });

  return localStorageMock;
};

/**
 * Standard window mock setup
 */
export const setupWindowMocks = () => {
  // Mock matchMedia with comprehensive implementation
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => {
      const mediaQuery = {
        matches: query.includes('(hover: hover)') ? true : false, // Default to hover support
        media: query,
        onchange: null,
        addListener: vi.fn(), // deprecated
        removeListener: vi.fn(), // deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      };

      // Handle specific media queries
      if (query.includes('(prefers-colour-scheme: dark)')) {
        mediaQuery.matches = false; // Default to light mode
      }
      if (query.includes('(max-width:')) {
        mediaQuery.matches = false; // Default to desktop
      }

      return mediaQuery;
    }),
  });

  // Mock setTimeout and clearTimeout to prevent async issues in tests
  const _originalSetTimeout = global.setTimeout;
  const _originalClearTimeout = global.clearTimeout;

  global.setTimeout = vi.fn().mockImplementation((callback, _delay) => {
    // Execute immediately in test environment to prevent async issues
    if (typeof callback === 'function') {
      try {
        callback();
      } catch (error) {
        // Silently handle errors to prevent uncaught exceptions
        if (import.meta.env.DEV) {
          console.warn('Timeout callback error in test:', error);
        }
      }
    }
    return 1; // Return a mock timer ID
  });

  global.clearTimeout = vi.fn();

  // Mock ResizeObserver
  Object.defineProperty(window, 'ResizeObserver', {
    writable: true,
    value: vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    })),
  });

  // Mock IntersectionObserver
  Object.defineProperty(window, 'IntersectionObserver', {
    writable: true,
    value: vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    })),
  });

  // Mock location for navigation components
  Object.defineProperty(window, 'location', {
    writable: true,
    value: {
      href: 'http://localhost:3000/',
      origin: 'http://localhost:3000',
      protocol: 'http:',
      host: 'localhost:3000',
      hostname: 'localhost',
      port: '3000',
      pathname: '/',
      search: '',
      hash: '',
      assign: vi.fn(),
      replace: vi.fn(),
      reload: vi.fn(),
    },
  });

  // Mock navigator
  Object.defineProperty(window, 'navigator', {
    writable: true,
    value: {
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      language: 'en-US',
      languages: ['en-US', 'en'],
      platform: 'Win32',
      cookieEnabled: true,
      onLine: true,
    },
  });
};

/**
 * Comprehensive mock setup for all integration tests
 */
export const setupAllStandardMocks = () => {
  setupDeviceContextMocks();
  setupLanguageContextMocks();
  setupSEOMocks();
  setupRouterMocks();
  setupAPIMocks();
  setupLocalStorageMocks();
  setupWindowMocks();
};

/**
 * Mock device type for responsive testing
 * Note: This function should only be used after the mocks are already set up
 */
export const mockDeviceType = (deviceType: 'mobile' | 'tablet' | 'desktop') => {
  // This is a placeholder function for documentation
  // The actual device mocking is handled by the vi.mock() calls in setupDeviceContextMocks()
  if (import.meta.env.DEV) {
    console.log(`Device type set to: ${deviceType}`);
  }
};

/**
 * Mock language for internationalization testing
 * Note: This function should only be used after the mocks are already set up
 */
export const mockLanguage = (language: string) => {
  // This is a placeholder function for documentation
  // The actual language mocking is handled by the vi.mock() calls in setupLanguageContextMocks()
  if (import.meta.env.DEV) {
    console.log(`Language set to: ${language}`);
  }
};

/**
 * Reset all mocks to default state
 */
export const resetAllMocks = () => {
  vi.clearAllMocks();
  setupAllStandardMocks();
};

/**
 * Mock error states for error handling testing
 */
export const mockErrorStates = () => {
  // Mock console to suppress error logs during testing
  const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => { });

  // Mock fetch to return errors
  global.fetch = vi.fn(() =>
    Promise.reject(new Error('Network error'))
  );

  return { consoleSpy };
};

/**
 * Restore normal states after error testing
 */
export const restoreNormalStates = (consoleSpy?: ReturnType<typeof vi.spyOn>) => {
  if (consoleSpy) {
    consoleSpy.mockRestore();
  }

  // Restore normal fetch
  global.fetch = vi.fn(() =>
    Promise.resolve({
      ok: true,
      status: 200,
      json: () => Promise.resolve({ success: true }),
      text: () => Promise.resolve('Success'),
    } as Response)
  );
};
