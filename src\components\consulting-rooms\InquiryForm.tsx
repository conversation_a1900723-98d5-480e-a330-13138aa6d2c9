import React from 'react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { FormField } from '@/data/consultingRooms/consultingRoomsData';

interface InquiryFormProps {
  title: string;
  fields: FormField[];
  submitText: string;
  confirmationText: string;
  onSubmit: (e: React.FormEvent) => void;
}

const InquiryForm: React.FC<InquiryFormProps> = ({
  title,
  fields,
  submitText,
  confirmationText,

  onSubmit
}) => {
  const renderField = (field: FormField) => {
    const baseProps = {
      id: field.id,
      name: field.name,
      required: field.required
    };

    switch (field.type) {
      case 'select':
        return (
          <select
            {...baseProps}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            aria-describedby={field.required ? `${field.id}-required` : undefined}
          >
            <option value="" disabled>
              {field.placeholder || `Select ${field.label}`}
            </option>
            {field.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      
      case 'textarea':
        return (
          <Textarea
            {...baseProps}
            placeholder={field.placeholder}
          />
        );
      
      default:
        return (
          <Input
            {...baseProps}
            type={field.type}
            placeholder={field.placeholder}
          />
        );
    }
  };

  return (
    <div id="inquiry-form">
      <h2 id="inquiry-form-title" className="text-enhanced-heading text-2xl font-bold mb-6">{title}</h2>
      <div className="card p-6 rounded-lg shadow-md medical-card">
        <form
          className="space-y-4"
          onSubmit={onSubmit}
          aria-labelledby="inquiry-form-title"
          noValidate
        >
          {/* Name fields in grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {fields.slice(0, 2).map((field) => (
              <div key={field.id} className="space-y-2">
                <Label htmlFor={field.id}>{field.label}</Label>
                {renderField(field)}
              </div>
            ))}
          </div>

          {/* Other fields */}
          {fields.slice(2, -2).map((field) => (
            <div key={field.id} className="space-y-2">
              <Label htmlFor={field.id}>{field.label}</Label>
              {renderField(field)}
            </div>
          ))}

          {/* Preferred Days Checkboxes */}
          <fieldset className="space-y-2">
            <legend className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              Preferred Days
            </legend>
            <div className="grid grid-cols-2 gap-2" role="group" aria-labelledby="preferred-days-legend">
              {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'].map((day) => (
                <label key={day} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={`day-${day.toLowerCase()}`}
                    name="preferredDays"
                    value={day}
                    className="h-4 w-4 rounded border-border text-primary focus:ring-primary focus:ring-2 focus:ring-offset-2"
                    aria-describedby="preferred-days-help"
                  />
                  <span>{day}</span>
                </label>
              ))}
            </div>
            <div id="preferred-days-help" className="text-xs text-muted-foreground">
              Select all days that work for your schedule
            </div>
          </fieldset>

          {/* Additional Requirements */}
          {fields.slice(-1).map((field) => (
            <div key={field.id} className="space-y-2">
              <Label htmlFor={field.id}>{field.label}</Label>
              {renderField(field)}
            </div>
          ))}

          <Button type="submit" className="w-full">{submitText}</Button>

          <p className="text-sm text-muted-foreground text-center mt-4">
            {confirmationText}
          </p>
        </form>
      </div>
    </div>
  );
};

InquiryForm.displayName = 'InquiryForm';

export default InquiryForm;
