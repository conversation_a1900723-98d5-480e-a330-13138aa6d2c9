import React from 'react';

/**
 * Hook for responsive image sizes
 */
export const useResponsiveImageSizes = (breakpoints: Record<string, number>) => {
  const generateSizes = React.useCallback(() => {
    const sizeEntries = Object.entries(breakpoints)
      .sort(([, a], [, b]) => b - a) // Sort by width descending
      .map(([size, width]) => `(max-width: ${width}px) ${size}`)
      .join(', ');

    return sizeEntries;
  }, [breakpoints]);

  return generateSizes();
};

/**
 * Utility function to generate responsive image sizes string
 */
export const generateImageSizes = (breakpoints: Record<string, number>): string => {
  return Object.entries(breakpoints)
    .sort(([, a], [, b]) => b - a) // Sort by width descending
    .map(([size, width]) => `(max-width: ${width}px) ${size}`)
    .join(', ');
};

/**
 * Utility function to generate srcSet for responsive images
 */
export const generateSrcSet = (baseSrc: string, sizes: number[]): string => {
  return sizes
    .map(size => `${baseSrc}?w=${size} ${size}w`)
    .join(', ');
};

/**
 * Image optimization utilities
 */
export const imageOptimization = {
  /**
   * Get optimized image URL with parameters
   */
  getOptimizedUrl: (src: string, options: { width?: number; height?: number; quality?: number; format?: string } = {}) => {
    const url = new URL(src, window.location.origin);
    
    if (options.width) url.searchParams.set('w', options.width.toString());
    if (options.height) url.searchParams.set('h', options.height.toString());
    if (options.quality) url.searchParams.set('q', options.quality.toString());
    if (options.format) url.searchParams.set('f', options.format);
    
    return url.toString();
  },

  /**
   * Check if image format is supported
   */
  isFormatSupported: (format: string): boolean => {
    const canvas = document.createElement('canvas');
    return canvas.toDataURL(`image/${format}`).indexOf(`data:image/${format}`) === 0;
  },

  /**
   * Get best supported image format
   */
  getBestFormat: (): string => {
    if (imageOptimization.isFormatSupported('webp')) return 'webp';
    if (imageOptimization.isFormatSupported('avif')) return 'avif';
    return 'jpg';
  }
};
