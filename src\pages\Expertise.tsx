import React, { useEffect, useMemo } from 'react';

import CTASection from '@/components/CTASection';
import {
  IntroductionSection,
  DoctorProfileSection,
  MainTreatmentAreasSection,
  SpecialisedProceduresSection,
  SurgicalApproachSection,
  TreatmentApproachesSection,
  TechnologySection,
  PatientOutcomesSection
} from '@/components/expertise';
import PageHeader from '@/components/PageHeader';
import StandardPageLayout from '@/components/StandardPageLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { expertisePageData } from '@/data/expertise/expertisePageData';
import { generatePageSEO } from '@/lib/seo';
import en from '@/locales/en';

const Expertise: React.FC = () => {
  const { t } = useLanguage();

  // Use translations with safe fallback
  const finalT = t || en || {
    navigation: {
      home: "Home",
      expertise: "Expertise",
      about: "About",
      locations: "Locations",
      patientResources: "Patient Resources",
      contact: "Contact",
      bookAppointment: "Book Appointment",
      language: "Language",
      menu: "Menu",
      close: "Close",
      skipToContent: "Skip to Content"
    },
    hero: {
      title: "Welcome",
      subtitle: "Professional Care",
      primaryCTA: "Book Now",
      secondaryCTA: "Learn More",
      scrollDown: "Scroll Down"
    },
    expertisePage: {
      surgicalApproach: {
        title: "Our Surgical Approach",
        subtitle: " follows a systematic approach to neurosurgical care:",
        principles: {
          identification: {
            title: "Accurate Identification",
            description: "Precise diagnosis using advanced imaging and diagnostic techniques to identify the exact problem."
          },
          access: {
            title: "Minimally Invasive Access",
            description: "Safe access to the pathological area with minimal injury to surrounding healthy tissues."
          },
          repair: {
            title: "Effective Repair",
            description: "Delicate and effective repair of complex structures using the latest surgical techniques."
          }
        },
        cta: "Schedule Your Consultation"
      },
      specialisedProcedures: {
        lumbarDisc: {
          description: "Advanced lumbar disc replacement procedures to address lower back pain and preserve spinal motion."
        },
        imageGuided: {
          description: "Precision image-guided surgery using advanced navigation systems for optimal surgical accuracy."
        },
        roboticSpine: {
          description: "State-of-the-art robotic-assisted spine surgery for enhanced precision and minimal invasiveness."
        }
      }
    },
    technologiesPage: {
      title: "Advanced Technologies",
      description: "State-of-the-art technology for neurosurgical procedures.",
      categories: {
        wellness: {
          title: "Minimally-Invasive Techniques",
          description: "Advanced minimally invasive procedures for better outcomes.",
          items: [
            { title: "Technique 1", description: "Description 1" },
            { title: "Technique 2", description: "Description 2" },
            { title: "Technique 3", description: "Description 3" },
            { title: "Technique 4", description: "Description 4" }
          ]
        },
        imagingTech: {
          title: "Image-Guided Technologies",
          description: "Precision imaging for accurate surgical navigation.",
          items: [
            { title: "Technology 1", description: "Description 1" },
            { title: "Technology 2", description: "Description 2" },
            { title: "Technology 3", description: "Description 3" },
            { title: "Technology 4", description: "Description 4" }
          ]
        },
        services: {
          title: "Spine Surgery Techniques",
          description: "Advanced spine surgery procedures.",
          items: [
            { title: "Service 1", description: "Description 1" },
            { title: "Service 2", description: "Description 2" },
            { title: "Service 3", description: "Description 3" },
            { title: "Service 4", description: "Description 4" }
          ]
        },
        brainSurgery: {
          title: "Brain Surgery Techniques",
          description: "Advanced brain surgery procedures.",
          items: [
            { title: "Brain Surgery 1", description: "Description 1" },
            { title: "Brain Surgery 2", description: "Description 2" },
            { title: "Brain Surgery 3", description: "Description 3" },
            { title: "Brain Surgery 4", description: "Description 4" }
          ]
        }
      }
    }
  };

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  // Generate SEO data for expertise page with structured data (enhanced per expertise.md)
  const expertiseSeoData = useMemo(() => {
    return generatePageSEO('expertise', {
      title: "Neurosurgical Expertise - Advanced Spine & Brain Surgery",
      description: "Expert neurosurgical procedures including robotic surgery, disc replacement, and minimally invasive techniques. Dr Ales Aliashkevich provides comprehensive brain and spine surgery services.",
      keywords: ["neurosurgery", "spine surgery", "brain surgery", "robotic surgery", "minimally invasive", "disc replacement", "image-guided surgery"]
    });
  }, []);

  // Ensure we have the required translation structure with fallbacks
  const safeExpertisePage = finalT?.expertisePage || en.expertisePage || {
    hero: {
      title: "Neurosurgical Expertise",
      subtitle: "Explore our neurosurgical specialties from spine surgery to brain tumor removal."
    },
    introduction: {
      paragraph1: "is a specialist neurosurgeon and spine surgeon with extensive experience in treating complex brain, spine, and nerve conditions.",
      paragraph2: "utilises the latest minimally invasive techniques and state-of-the-art technology to provide optimal patient outcomes.",
      paragraph3: "His comprehensive approach combines advanced surgical techniques with personalised patient care to achieve the best possible results."
    },
    specialisedProcedures: {
      cervicalDisc: {
        description: "Motion-preserving cervical disc replacement surgery to treat neck pain and restore natural mobility."
      },
      lumbarDisc: {
        description: "Advanced lumbar disc replacement procedures to address lower back pain and preserve spinal motion."
      },
      imageGuided: {
        description: "Precision image-guided surgery using advanced navigation systems for optimal surgical accuracy."
      },
      roboticSpine: {
        description: "State-of-the-art robotic-assisted spine surgery for enhanced precision and minimal invasiveness."
      }
    },
    surgicalApproach: {
      title: "Our Surgical Approach",
      subtitle: " follows a systematic approach to neurosurgical care:",
      principles: {
        identification: {
          title: "Accurate Identification",
          description: "Precise diagnosis using advanced imaging and diagnostic techniques to identify the exact problem."
        },
        access: {
          title: "Minimally Invasive Access",
          description: "Safe access to the pathological area with minimal injury to surrounding healthy tissues."
        },
        repair: {
          title: "Effective Repair",
          description: "Delicate and effective repair of complex structures using the latest surgical techniques."
        }
      },
      cta: "Schedule Your Consultation"
    }
  };

  const safeProcedureDescriptions = finalT?.procedureDescriptions || en.procedureDescriptions || {
    "brain-tumour-removal": { name: "Brain Conditions" },
    "lumbar-disc-replacement": { name: "Spinal Problems" },
    "peripheral-nerve-surgery": { name: "Nerve Problems" }
  };

  const safeExpertiseCards = finalT?.expertiseCards || en.expertiseCards || {
    brainConditions: { description: "Comprehensive treatment for brain tumours and neurological conditions." },
    spinalProblems: { description: "Advanced spine surgery for disc problems and spinal disorders." },
    nerveProblems: { description: "Specialised treatment for peripheral nerve conditions." },
    medicolegalReports: {
      title: "Medico-Legal Reports",
      description: "Assessment of impairment according to AMA Guides to Permanent impairment, incapacity, work cover, transport accident injuries, liability enquiries."
    }
  };

  const safeNav = finalT?.nav || en.nav || {
    expertiseSubmenu: {
      cervicalDisc: "Cervical Disc Replacement",
      lumbarDisc: "Lumbar Disc Replacement",
      imageGuided: "Image-Guided Surgery",
      roboticSpine: "Robotic Spine Surgery"
    }
  };

  const safeHome = finalT?.home || en.home || {
    featuredProcedures: {
      title: "Specialised Treatment Areas",
      subtitle: "Our Expertise",
      description: "We use the latest minimally-invasive techniques to treat a variety of brain, spine, and nerve conditions.",
      viewAll: "Explore All Neurosurgical Specialties"
    },
    welcome: { learnMore: "Learn More" }
  };

  const safeTechnologiesPage = finalT?.technologiesPage || en.technologiesPage || {
    title: "Advanced Technologies",
    description: "State-of-the-art technology for neurosurgical procedures.",
    categories: {
      wellness: {
        title: "Minimally-Invasive Techniques",
        description: "Advanced minimally invasive procedures for better outcomes.",
        items: [
          { title: "Technique 1", description: "Description 1" },
          { title: "Technique 2", description: "Description 2" },
          { title: "Technique 3", description: "Description 3" },
          { title: "Technique 4", description: "Description 4" }
        ]
      },
      imagingTech: {
        title: "Image-Guided Technologies",
        description: "Precision imaging for accurate surgical navigation.",
        items: [
          { title: "Technology 1", description: "Description 1" },
          { title: "Technology 2", description: "Description 2" },
          { title: "Technology 3", description: "Description 3" },
          { title: "Technology 4", description: "Description 4" }
        ]
      },
      services: {
        title: "Spine Surgery Techniques",
        description: "Advanced spine surgery procedures.",
        items: [
          { title: "Service 1", description: "Description 1" },
          { title: "Service 2", description: "Description 2" },
          { title: "Service 3", description: "Description 3" },
          { title: "Service 4", description: "Description 4" }
        ]
      },
      brainSurgery: {
        title: "Brain Surgery Techniques",
        description: "Advanced brain surgery procedures.",
        items: [
          { title: "Brain Surgery 1", description: "Description 1" },
          { title: "Brain Surgery 2", description: "Description 2" },
          { title: "Brain Surgery 3", description: "Description 3" },
          { title: "Brain Surgery 4", description: "Description 4" }
        ]
      }
    }
  };

  // Additional safety check for hero section
  const heroData = safeExpertisePage?.hero || {
    title: "Neurosurgical Expertise",
    subtitle: "Explore our neurosurgical specialties from spine surgery to brain tumor removal."
  };

  return (
    <StandardPageLayout pageType="expertise" seoData={expertiseSeoData} showHeader={false}>
      <PageHeader
        title={heroData.title}
        subtitle={heroData.subtitle}
        backgroundImage="/images/Ales-Aliashkevich-spine-brain-image-guided-neurosurgery-microsurgery-advanced-maximum-precision-robotic-spine-Melbourne.jpg"
        enableParallax={true}
      />

      <div className="flex-1">
        {/* Introduction Section */}
        <IntroductionSection introduction={safeExpertisePage.introduction} />

        {/* Doctor Profile Section - New content from expertise.md */}
        <DoctorProfileSection doctorProfile={expertisePageData.doctorProfile} />

        {/* Main Content */}
        <MainTreatmentAreasSection
          procedureDescriptions={safeProcedureDescriptions}
          expertiseCards={safeExpertiseCards}
          learnMoreText={safeHome?.welcome?.learnMore || "Learn More"}
        />

        {/* Specialised Procedures */}
        <section className="section-spacing section-background">
          <div className="container">
            <SpecialisedProceduresSection
              title={safeHome?.featuredProcedures?.title || "Specialised Treatment Areas"}
              procedures={safeExpertisePage.specialisedProcedures}
              navigation={safeNav?.expertiseSubmenu || {
                cervicalDisc: "Cervical Disc Replacement",
                lumbarDisc: "Lumbar Disc Replacement",
                imageGuided: "Image-Guided Surgery",
                roboticSpine: "Robotic Spine Surgery"
              }}
              learnMoreText={safeHome?.welcome?.learnMore || "Learn More"}
            />
          </div>
        </section>
        {/* Surgical Approach Section */}
        <SurgicalApproachSection surgicalApproach={safeExpertisePage.surgicalApproach} />

        {/* Treatment Approaches Section - New content from expertise.md */}
        <TreatmentApproachesSection treatmentApproaches={expertisePageData.treatmentApproaches} />

        {/* Advanced Technologies Section */}
        <section className="section-spacing section-background-alt">
          <div className="container">
            <div className="text-center max-w-3xl mx-auto mb-12">
              <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-6">{safeTechnologiesPage.title}</h2>
              <p className="text-enhanced-body">
                {safeTechnologiesPage.description}
              </p>
            </div>
          </div>
        </section>
        {/* Minimally-Invasive Techniques */}
        <TechnologySection
          title={safeTechnologiesPage.categories.wellness.title}
          description={safeTechnologiesPage.categories.wellness.description}
          items={safeTechnologiesPage.categories.wellness.items}
          images={[
            "/images/brain-tumour-image-guided-surgery-minimally-invasive-advanced-neurosurgery-aliashkevich-mineuro.jpg",
            "/images/lumbar-fusion-minimally-invasive-Mazor-Robotics-Renaissance-screw-planning-3D-neurosurgery.jpg"
          ]}
          backgroundColor="bg-primary/5"
          imageOrder="left"
        />
        {/* Image-Guided Technologies */}
        <TechnologySection
          title={safeTechnologiesPage.categories.imagingTech.title}
          description={safeTechnologiesPage.categories.imagingTech.description}
          items={safeTechnologiesPage.categories.imagingTech.items}
          images={[
            "/images/robotic-operating-theatre-spine-brain-navigation-microsurgery.jpg",
            "/images/neuromonitoring-EMG-MEP-SSEP-spinal-nerve-root-stimulation.jpg"
          ]}
          imageOrder="left"
        />
        {/* Spine Surgery Techniques */}
        <TechnologySection
          title={safeTechnologiesPage.categories.services.title}
          description={safeTechnologiesPage.categories.services.description}
          items={safeTechnologiesPage.categories.services.items}
          images={[
            "/images/CP-ESP-arthroplasty-disc-replacement-cervical-lumbar.jpg",
            "/images/scoliosis-stenosis-lumbar-spine-CT-fusion-minimally-invasive-XLIF-Medtronic-Aliashkevich-neurosurgery.jpg"
          ]}
          backgroundColor="bg-primary/5"
          imageOrder="left"
        />
        {/* Brain Surgery Techniques */}
        <TechnologySection
          title={safeTechnologiesPage.categories.brainSurgery.title}
          description={safeTechnologiesPage.categories.brainSurgery.description}
          items={safeTechnologiesPage.categories.brainSurgery.items}
          images={[
            "/images/brain-tumour-navigated-image-guided-surgery-miNEURO-Aliashekvich-robotic.jpg",
            "/images/MCA-aneurysm-cerebral-microsurgery-clipping-subarachnoid.jpg"
          ]}
          imageOrder="left"
        />

        {/* Patient Outcomes Section - New content from expertise.md */}
        <PatientOutcomesSection patientOutcomes={expertisePageData.patientOutcomes} />

        {/* Call to Action */}
        <CTASection />
      </div>
    </StandardPageLayout>
  );
};

Expertise.displayName = 'Expertise';

export default Expertise;
