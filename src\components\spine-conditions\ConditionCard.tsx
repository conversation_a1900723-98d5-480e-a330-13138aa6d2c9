import React from 'react';
import { Link } from 'react-router-dom';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import type { ConditionData } from '@/data/spine-conditions/spineConditionsData';

interface ConditionCardProps {
  condition: ConditionData;
}

/**
 * ConditionCard Component
 * Reusable card component for displaying spine condition information
 * Preserves exact styling and functionality from original implementation
 */
const ConditionCard: React.FC<ConditionCardProps> = ({ condition }) => {
  return (
    <Card className="medical-card/90 backdrop-blur-sm shadow-xl border border-border/50 hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30">
      <div className="relative h-52 overflow-hidden rounded-t-xl">
        <img
          src={condition.image.src}
          alt={condition.image.alt}
          className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
      </div>
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-bold text-foreground">{condition.title}</CardTitle>
      </CardHeader>
      <CardContent className="pb-6">
        <p className="text-foreground/80 mb-6 text-base leading-relaxed font-medium">
          {condition.description}
        </p>
        <h4 className="font-bold mb-4 text-foreground text-base">{condition.featuresLabel}</h4>
        <ul className="space-y-2">
          {condition.features.map((feature, index) => (
            <li key={index} className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
              <span className="text-foreground/80 text-base leading-relaxed">{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
      <CardFooter>
        <Button asChild variant="outline" className="w-full font-bold py-3 border-2 hover:shadow-lg transition-all duration-300 hover:scale-105">
          <Link to={condition.link.url}>{condition.link.text}</Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

ConditionCard.displayName = 'ConditionCard';

export default ConditionCard;
