import { Scissors, Activity, CheckCircle, AlertTriangle, Clock, TrendingUp, Target, Zap } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface SurgicalProcedure {
  name: string;
  description: string;
  technique: string;
  indications: string[];
  advantages: string[];
  risks: string[];
  successRate: string;
  recovery: string;
  longTermOutcomes: string[];
}

interface SurgicalTreatmentComparisonProps {
  title: string;
  description: string;
  procedures: SurgicalProcedure[];
}

export function SurgicalTreatmentComparison({ 
  title, 
  description, 
  procedures 
}: SurgicalTreatmentComparisonProps) {
  const deviceInfo = useDeviceDetection();
  const [activeTab, setActiveTab] = useState('overview');

  const getSurgicalIcon = (name: string) => {
    if (name.includes('ACDF') || name.includes('Anterior')) return Scissors;
    if (name.includes('Laminectomy')) return Activity;
    if (name.includes('Fusion')) return Target;
    return CheckCircle;
  };

  const getSurgicalColor = (name: string) => {
    if (name.includes('ACDF') || name.includes('Anterior')) return 'text-foreground bg-info-light border-info/30';
    if (name.includes('Laminectomy')) return 'text-foreground bg-success-light border-success/30';
    if (name.includes('Fusion')) return 'text-medical-blue bg-medical-blue-light border-medical-blue/30';
    return 'text-muted-foreground bg-muted border-border';
  };

  const getSurgicalType = (name: string) => {
    if (name.includes('ACDF') || name.includes('Anterior')) return { label: 'Anterior Approach', colour: 'bg-info-light text-foreground border border-info/30' };
    if (name.includes('Laminectomy')) return { label: 'Posterior Approach', colour: 'bg-success-light text-foreground border border-success/30' };
    if (name.includes('Fusion')) return { label: 'Fusion Procedure', colour: 'bg-medical-blue-light text-foreground border border-medical-blue/30' };
    return { label: 'Other', colour: 'bg-muted text-muted-foreground' };
  };

  const getComplexityScore = (procedure: SurgicalProcedure) => {
    // Calculate complexity based on technique and risks
    let score = 30; // Base score
    if (procedure.name.includes('Fusion')) score += 30;
    if (procedure.name.includes('Multi-level') || procedure.technique.includes('multiple')) score += 20;
    if (procedure.risks.length > 4) score += 20;
    return Math.min(score, 100);
  };

  const getSuccessRateNumber = (successRate: string) => {
    const match = successRate.match(/(\d+)-?(\d+)?%/);
    if (match) {
      return match[2] ? parseInt(match[2]) : parseInt(match[1]);
    }
    return 75; // Default if no match
  };

  return (
    <section className={cn(
      "py-20 bg-gradient-to-br from-background via-background/95 to-muted/30",
      deviceInfo.isMobile ? "px-4" : ""
    )}>
      <div className="container">
        <div className="text-center mb-16">
          <h2 className={cn(
            "font-bold text-foreground mb-6 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-4xl mx-auto leading-relaxed",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-10 bg-card/80 backdrop-blur-sm border border-border/50 shadow-lg",
            deviceInfo.isMobile ? "grid-cols-1 h-auto" : "grid-cols-2"
          )}>
            <TabsTrigger
              value="overview"
              className={cn(
                "text-center font-semibold transition-all duration-300",
                deviceInfo.isMobile ? "py-4 text-sm" : "py-5 text-base"
              )}
            >
              Surgical Overview
            </TabsTrigger>
            <TabsTrigger
              value="comparison"
              className={cn(
                "text-center font-semibold transition-all duration-300",
                deviceInfo.isMobile ? "py-4 text-sm" : "py-5 text-base"
              )}
            >
              Detailed Comparison
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-10">
            {/* Success Rate Comparison */}
            <Card className="medical-card/90 backdrop-blur-sm border border-border/50 shadow-xl">
              <CardHeader className="pb-8">
                <CardTitle className="flex items-center gap-3 text-foreground">
                  <TrendingUp className="h-6 w-6 text-primary" />
                  Success Rates and Complexity
                </CardTitle>
                <CardDescription className="text-base text-foreground/80 leading-relaxed">
                  Comparison of surgical success rates and procedure complexity
                </CardDescription>
              </CardHeader>
              <CardContent className="p-8">
                <div className="space-y-8">
                  {procedures.map((procedure, index) => {
                    const successRate = getSuccessRateNumber(procedure.successRate);
                    const complexityScore = getComplexityScore(procedure);
                    const surgicalType = getSurgicalType(procedure.name);

                    return (
                      <div key={index} className="space-y-4 p-6 bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-xl border border-border/50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <span className="font-bold text-foreground text-lg">{procedure.name}</span>
                            <Badge className={cn("font-semibold shadow-md", surgicalType.colour)}>
                              {surgicalType.label}
                            </Badge>
                          </div>
                          <span className="text-base font-bold text-primary">{procedure.successRate}</span>
                        </div>
                        <div className="space-y-4">
                          <div className="flex justify-between text-sm font-medium text-foreground/90">
                            <span>Success Rate</span>
                            <span>{successRate}%</span>
                          </div>
                          <Progress value={successRate} className="h-3 bg-muted/50" />
                          <div className="flex justify-between text-sm font-medium text-foreground/90">
                            <span>Complexity</span>
                            <span>{complexityScore}%</span>
                          </div>
                          <Progress value={complexityScore} className="h-3 bg-info/50 dark:bg-info/30" />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Quick Comparison Grid */}
            <div className="grid gap-8 md:grid-cols-3">
              {procedures.map((procedure, index) => {
                const Icon = getSurgicalIcon(procedure.name);
                const surgicalType = getSurgicalType(procedure.name);

                return (
                  <Card key={index} className={cn(
                    "bg-card/90 backdrop-blur-sm border border-border/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 hover:border-primary/30",
                    getSurgicalColor(procedure.name)
                  )}>
                    <CardHeader className="pb-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="p-3 rounded-xl medical-card/90 shadow-lg border border-border/50">
                            <Icon className="h-6 w-6 text-primary" />
                          </div>
                          <div>
                            <CardTitle className="text-xl text-foreground leading-tight">{procedure.name}</CardTitle>
                          </div>
                        </div>
                        <Badge className={cn("font-semibold shadow-md", surgicalType.colour)}>
                          {surgicalType.label}
                        </Badge>
                      </div>
                      <CardDescription className="text-base text-foreground/80 leading-relaxed">{procedure.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-6">
                        <div className="grid gap-4 text-sm bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                          <div className="flex justify-between">
                            <span className="text-foreground/70 font-medium">Success Rate:</span>
                            <span className="font-bold text-primary">{procedure.successRate}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-foreground/70 font-medium">Recovery:</span>
                            <span className="font-semibold text-foreground">{procedure.recovery.split(',')[0]}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-foreground/70 font-medium">Risks:</span>
                            <span className="font-semibold text-foreground">{procedure.risks.length} types</span>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <h4 className="font-bold text-sm text-foreground">Key Advantages:</h4>
                          <ul className="space-y-2">
                            {procedure.advantages.slice(0, 2).map((advantage, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                                <CheckCircle className="h-4 w-4 text-success dark:text-success mt-0.5 flex-shrink-0" />
                                <span className="leading-relaxed">{advantage}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="comparison" className="space-y-8">
            {procedures.map((procedure, index) => {
              const Icon = getSurgicalIcon(procedure.name);
              const surgicalType = getSurgicalType(procedure.name);

              return (
                <Card key={index} className="medical-card/90 backdrop-blur-sm border border-border/50 shadow-xl">
                  <CardHeader className="pb-8">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="p-3 rounded-xl medical-card/90 shadow-lg border border-border/50">
                          <Icon className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                          <CardTitle className="text-2xl text-foreground leading-tight">{procedure.name}</CardTitle>
                          <CardDescription className="text-base text-foreground/80 leading-relaxed mt-2">{procedure.description}</CardDescription>
                        </div>
                      </div>
                      <Badge className={cn("font-semibold shadow-md", surgicalType.colour)}>
                        {surgicalType.label}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Technique */}
                    <div className="bg-info-light border border-info/30 rounded-lg p-4">
                      <h4 className="font-semibold mb-2 text-foreground">Surgical Technique</h4>
                      <p className="text-sm text-foreground">{procedure.technique}</p>
                    </div>

                    {/* Detailed Information */}
                    <div className={cn(
                      "grid gap-6",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}>
                      <div>
                        <h4 className="font-semibold mb-3 text-success">Indications</h4>
                        <ul className="space-y-2">
                          {procedure.indications.map((indication, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                              <span className="text-sm">{indication}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-3 text-foreground">Advantages</h4>
                        <ul className="space-y-2">
                          {procedure.advantages.map((advantage, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <CheckCircle className="h-4 w-4 text-success mt-0.5 flex-shrink-0" />
                              <span className="text-sm text-foreground">{advantage}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-3 text-foreground">Risks and Complications</h4>
                        <ul className="space-y-2">
                          {procedure.risks.map((risk, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <AlertTriangle className="h-4 w-4 text-foreground mt-0.5 flex-shrink-0" />
                              <span className="text-sm">{risk}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-3 text-medical-blue">Long-term Outcomes</h4>
                        <ul className="space-y-2">
                          {procedure.longTermOutcomes.map((outcome, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-medical-blue rounded-full mt-2 flex-shrink-0" />
                              <span className="text-sm">{outcome}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    {/* Recovery Information */}
                    <div className="bg-success-light border border-success/30 rounded-lg p-4">
                      <h4 className="font-semibold text-foreground mb-2 flex items-center gap-2">
                        <Clock className="h-4 w-4 text-success" />
                        Recovery Timeline
                      </h4>
                      <p className="text-sm text-foreground">{procedure.recovery}</p>
                    </div>

                    {/* Clinical Notes */}
                    <div className="bg-muted border border-border rounded-lg p-4">
                      <h4 className="font-semibold text-foreground mb-2">Clinical Considerations</h4>
                      <p className="text-sm text-foreground">
                        {procedure.name.includes('ACDF') && 
                          "ACDF is the gold standard for anterior cervical compression. It provides excellent fusion rates and direct access to disc herniations and osteophytes. The anterior approach preserves posterior structures but may be associated with swallowing difficulties in the early post-operative period."
                        }
                        {procedure.name.includes('Laminectomy') && 
                          "Posterior laminectomy is ideal for multi-level stenosis and posterior compression. It preserves anterior structures but may lead to post-operative kyphosis if cervical lordosis is not maintained. Careful patient selection is crucial for optimal outcomes."
                        }
                        {procedure.name.includes('Fusion') && 
                          "Posterior fusion provides excellent stability and prevents post-laminectomy kyphosis. It's particularly useful for patients with instability or extensive decompression requirements. The fusion process typically takes 3-6 months to complete."
                        }
                      </p>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>
        </Tabs>

        {/* Surgical Selection Guide */}
        <div className="mt-16">
          <Card className="medical-card/90 backdrop-blur-sm border border-border/50 shadow-xl">
            <CardHeader className="pb-8">
              <CardTitle className="flex items-center gap-3 text-foreground">
                <Target className="h-6 w-6 text-primary" />
                Surgical Approach Selection Guide
              </CardTitle>
              <CardDescription className="text-base text-foreground/80 leading-relaxed">
                Factors to consider when choosing the most appropriate surgical approach
              </CardDescription>
            </CardHeader>
            <CardContent className="p-8">
              <div className={cn(
                "grid gap-8",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-4"
              )}>
                <div className="text-center p-6 bg-background/80 dark:bg-muted/80 backdrop-blur-sm rounded-xl border border-border/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <div className="w-14 h-14 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-4 shadow-lg">
                    1
                  </div>
                  <h4 className="font-bold mb-3 text-foreground">Compression Location</h4>
                  <p className="text-sm text-foreground/80 leading-relaxed">
                    Anterior vs posterior compression determines surgical approach
                  </p>
                </div>
                <div className="text-center p-6 bg-background/80 dark:bg-muted/80 backdrop-blur-sm rounded-xl border border-border/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <div className="w-14 h-14 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-4 shadow-lg">
                    2
                  </div>
                  <h4 className="font-bold mb-3 text-foreground">Number of Levels</h4>
                  <p className="text-sm text-foreground/80 leading-relaxed">
                    Single vs multi-level disease affects procedure choice
                  </p>
                </div>
                <div className="text-center p-6 bg-background/80 dark:bg-muted/80 backdrop-blur-sm rounded-xl border border-border/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <div className="w-14 h-14 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-4 shadow-lg">
                    3
                  </div>
                  <h4 className="font-bold mb-3 text-foreground">Spinal Alignment</h4>
                  <p className="text-sm text-foreground/80 leading-relaxed">
                    Cervical lordosis and stability considerations
                  </p>
                </div>
                <div className="text-center p-6 bg-background/80 dark:bg-muted/80 backdrop-blur-sm rounded-xl border border-border/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <div className="w-14 h-14 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-4 shadow-lg">
                    4
                  </div>
                  <h4 className="font-bold mb-3 text-foreground">Patient Factors</h4>
                  <p className="text-sm text-foreground/80 leading-relaxed">
                    Age, bone quality, and medical comorbidities
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Surgical Urgency */}
        <div className="mt-16">
          <Card className="bg-muted-light border border-border/70 shadow-xl backdrop-blur-sm">
            <CardHeader className="pb-6">
              <CardTitle className="flex items-center gap-3 text-foreground">
                <Zap className="h-6 w-6 text-foreground" />
                Timing of Surgical Intervention
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8">
              <p className="text-foreground text-base leading-relaxed font-medium">
                The timing of surgery in cervical myelopathy is crucial. Progressive myelopathy typically requires
                prompt surgical intervention to prevent irreversible neurological damage. Early surgery generally
                leads to better outcomes, while delayed treatment may result in limited recovery potential even
                with successful decompression.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <Card className="medical-card/90 backdrop-blur-sm border border-border/50 shadow-xl">
            <CardContent className="p-10">
              <h3 className="font-bold text-2xl text-foreground mb-4 leading-tight">Expert Cervical Myelopathy Treatment</h3>
              <p className="text-foreground/80 mb-8 text-lg leading-relaxed max-w-2xl mx-auto">
                Our neurosurgical team provides comprehensive evaluation and treatment for cervical myelopathy.
                Personalised surgical planning and advanced techniques for optimal neurological outcomes.
              </p>
              <Button
                size={deviceInfo.isMobile ? "default" : "lg"}
                className="px-8 py-4 font-semibold shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                Schedule Consultation
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default SurgicalTreatmentComparison;
