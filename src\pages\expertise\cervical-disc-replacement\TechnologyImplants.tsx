import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import ExpertiseCallToAction from '@/components/expertise/ExpertiseCallToAction';
import ExpertiseHero from '@/components/expertise/ExpertiseHero';
import ExpertiseSection from '@/components/expertise/ExpertiseSection';
import StandardPageLayout from '@/components/StandardPageLayout';

/**
 * Cervical Disc Replacement - Technology & Implants Sub-page
 * Comprehensive coverage of implant technology, materials, and device examples
 */

const TechnologyImplants: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const sections = [
    {
      id: 'history',
      title: 'History of Cervical Disc Replacement',
      content: [
        'Surgery of the cervical discs began evolving in the 1950s. Dr <PERSON>, an orthopaedic surgeon, and Dr <PERSON>, a neurosurgeon, from John Hopkins Hospital in Baltimore, United States published an article "Anterolateral cervical disc removal and interbody fusion for cervical disc syndrome" in 1955.',
        'Dr <PERSON><PERSON>, a surgeon from Uddevalla, Sweden, invented a solid stainless-steel sphere disc prosthesis now known as the <PERSON><PERSON><PERSON> Ball. In 1966 he published an article "Arthroplasty with intracorporal endoprosthesis in the herniated disc and in the painful disc". Because of complications related to ball migration, hypermobility, and subsidence, the use of Ferns<PERSON> ball implants eventually had to be abandoned.',
        'The development of lumbar disc prostheses in the 1980s also rekindled the evolution of cervical devices. Years of technological advancement and biomechanical studies lead to the development of further generations of robust and safe cervical implants.',
        'Cervical total disc replacement surgery began in Australia in the 1990s, but Medicare stopped funding it mainly because of poor results with the first-generation prostheses. After new randomized comparative evidence was published, MSAC supported its public funding for symptomatic single-level cervical degenerative disc disease in 2011.'
      ]
    },
    {
      id: 'disc-implants',
      title: 'Modern Disc Implants',
      content: [
        'Decades of biomechanical research, engineering, and meticulous clinical studies were required to achieve advances in medical device technology and to produce a current line of artificial intervertebral discs. Modern artificial cervical disc implants are available in variable shapes, sizes, heights and articulation types.',
        'An ideal artificial disc should achieve the following goals: distribute loads evenly across the front, back, and sides of the vertebrae; provide stability and natural neck movements in different postures; bear prolonged loads of the head\'s weight; unload facet joints; restore healthy anatomy and space between the vertebrae; restore foraminal height decompressing the nerve roots; be biologically compatible, corrosion-resistant, and durable.',
        'The crucial biomechanical factor in a cervical disc implant is the position of its center of rotation. As the natural human disc can move in all 3 planes and does not have a fixed rotational center, a surgeon needs to choose an implant design with similar kinematics.',
        'Many of the disc implant designs are uni- or bi-articular. Some old models are metal-on-metal ball-and-socket designs. Others are metal-on-plastic with one or two ball-in-socket or saddle-type articulations. The healthy disc is elastic, and apart from flexion/extension, lateral bending and rotation can also be compressed to absorb axial shock.'
      ]
    },
    {
      id: 'implant-classification',
      title: 'Implant Classification & Materials',
      content: [
        'Implants can be classified as constrained, semi-constrained, and unconstrained. A constrained disc has a mechanical stop that limits its range of motion and provides physical stability. Semi-constrained devices can move slightly outside of the physiological range of motion, and unconstrained prostheses rely only on natural limiters of segmental mobility like spinal ligaments and facet joints.',
        'Titanium is the most common disc implant material used, followed by cobalt alloys. Special surface treatment increases its integration with the vertebral bones through large footprint to cover the maximum area of the vertebral endplate, porosity, keels, spikes and screws, and specialised coatings (calcium phosphate, plasma-sprayed titanium, aluminum oxide, hydroxyapatite).',
        'The metallic components may produce distortions (artifacts) on magnetic resonance imaging (MRI) but because they are fixed to the bone, should not be considered as a contraindication for a scan.'
      ]
    }
  ];

  const implantExamples = [
    {
      name: 'Bryan Disc (Medtronic)',
      description: 'A semi-constrained device made of titanium alloy plates and a polyurethane center surrounded by a saline-filled sheath. The plate surfaces are porous with an anterior stop to prevent posterior dislocation.'
    },
    {
      name: 'Mobi-C Disc (Zimmer Biomet)',
      description: 'Made from cobalt-chromium-molybdenum alloy coated with titanium and hydroxyapatite spray. It has a polyethylene center which moves between two stops providing translational freedom.'
    },
    {
      name: 'ProDisc-C (Depuy Synthes)',
      description: 'A ball-and-socket semi-constrained device made of cobalt chrome alloy with a convex firm plastic insert. It has keels or teeth for fixation.'
    },
    {
      name: 'Prestige Implant (Medtronic)',
      description: 'An unconstrained metal-on-metal device coming in two variations: ST made of stainless steel, and LP made of titanium. ST model fixates by screws, and the LP has rails coated with titanium plasma spray.'
    },
    {
      name: 'M6 Disc (Spinal Kinetics)',
      description: 'A single-piece device with titanium alloy plates and a complex polymeric centerpiece surrounded by polyethylene woven fiber. This implant allows for motion in all six degrees of freedom.'
    },
    {
      name: 'Freedom Disc (AxioMed)',
      description: 'A one-piece viscoelastic implant bonded to two bead-coated titanium endplates with rails. The polymer cores are designed to provide stiffness similar to healthy human discs with natural biomechanics.'
    },
    {
      name: 'CP-ESP Disc (FH Orthopedics)',
      description: 'A one-piece implant with titanium alloy endplates covered by hydroxyapatite with spikes. The core consists of concentric inner and outer parts made from elastomeric polycarbonate urethane with variable center of rotation.'
    }
  ];

  const callToActionData = {
    title: 'Learn More About Cervical Disc Replacement',
    description: 'Explore the surgical procedure and recovery process, or learn about risks and how disc replacement compares to fusion surgery.',
    primaryButton: {
      text: 'Surgery & Recovery',
      link: '/expertise/cervical-disc-replacement/surgery-recovery'
    },
    secondaryButton: {
      text: 'Risks & Comparison',
      link: '/expertise/cervical-disc-replacement/risks-comparison'
    }
  };

  return (
    <StandardPageLayout title="Cervical Disc Replacement - Technology & Implants" showHeader={false}>
      <ExpertiseHero
        title="Technology & Implants"
        subtitle="Advanced artificial disc technology and biomaterial engineering for cervical spine treatment"
        backgroundImage="/images/CP-ESP-arthroplasty-disc-replacement-cervical-lumbar.jpg"
      />

      <div className="flex-1">
        {/* Breadcrumb Navigation */}
        <section className="py-4 bg-muted/30">
          <div className="container">
            <nav className="flex items-center space-x-2 text-sm text-enhanced-caption">
              <Link to="/expertise/cervical-disc-replacement" className="hover:text-primary">
                Cervical Disc Replacement
              </Link>
              <span>→</span>
              <span className="text-foreground">Technology & Implants</span>
            </nav>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                {/* Main Sections */}
                {sections.map((section) => (
                  <ExpertiseSection key={section.id} section={section} />
                ))}

                {/* Implant Examples */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Implant Examples</h2>
                  <p className="text-muted-foreground mb-6">
                    The market of cervical disc prostheses is rapidly expanding and constantly changing, bringing new models into consideration. Here are some of the most commonly used devices:
                  </p>
                  
                  <div className="grid grid-cols-1 gap-6">
                    {implantExamples.map((implant, index) => (
                      <div key={index} className="border rounded-lg p-6 medical-card">
                        <h3 className="text-lg font-semibold mb-3 text-primary">{implant.name}</h3>
                        <p className="text-muted-foreground">{implant.description}</p>
                      </div>
                    ))}
                  </div>
                  
                  <div className="mt-6 p-4 bg-primary/10 rounded-lg">
                    <p className="text-muted-foreground">
                      <strong>Note:</strong> For more information and examples of available cervical disc prostheses, please refer to: 
                      <a href="https://www.medicalexpo.com/medical-manufacturer/cervical-disc-prosthesis-4101.html" 
                         target="_blank" 
                         rel="noopener noreferrer" 
                         className="text-primary hover:underline ml-1">
                        Medical Expo Cervical Disc Prosthesis Directory
                      </a>
                    </p>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="sticky top-8 space-y-6">
                  <div className="medical-card p-6 rounded-lg shadow-md">
                    <h3 className="text-lg font-semibold mb-4">Related Pages</h3>
                    <div className="space-y-3">
                      <Link to="/expertise/cervical-disc-replacement" className="block text-primary hover:underline">
                        ← Back to Overview
                      </Link>
                      <Link to="/expertise/cervical-disc-replacement/surgery-recovery" className="block text-primary hover:underline">
                        Surgery & Recovery →
                      </Link>
                      <Link to="/expertise/cervical-disc-replacement/risks-comparison" className="block text-primary hover:underline">
                        Risks & Comparison →
                      </Link>
                    </div>
                  </div>
                  
                  <div className="bg-primary/5 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Key Technologies</h3>
                    <ul className="space-y-2 text-sm text-enhanced-caption">
                      <li>• Motion preservation technology</li>
                      <li>• Biomechanical engineering</li>
                      <li>• Advanced materials (titanium, cobalt alloys)</li>
                      <li>• Variable center of rotation</li>
                      <li>• Elastic shock absorption</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <ExpertiseCallToAction
          title={callToActionData.title}
          description={callToActionData.description}
          primaryButton={callToActionData.primaryButton}
          secondaryButton={callToActionData.secondaryButton}
        />
      </div>
    </StandardPageLayout>
  );
};

TechnologyImplants.displayName = 'TechnologyImplants';

export default TechnologyImplants;
