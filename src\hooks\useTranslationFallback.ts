import { useMemo } from 'react';

import { useLanguage } from '@/contexts/LanguageContext';
import en from '@/locales/en';

interface TranslationFallbackOptions {
  fallbackKey?: string;
  defaultValues?: Record<string, unknown>;
}

export const useTranslationFallback = (
  translationKey: string,
  options: TranslationFallbackOptions = {}
) => {
  const { t } = useLanguage();
  const { fallbackKey, defaultValues = {} } = options;

  const safeTranslations = useMemo(() => {
    // Primary translation source
    const primaryT = t?.[translationKey as keyof typeof t] as Record<string, unknown> | undefined;

    // Fallback translation source
    const fallbackT = fallbackKey
      ? (t?.[fallbackKey as keyof typeof t] as Record<string, unknown> | undefined)
      : null;

    // English fallback
    const enT = en?.[translationKey as keyof typeof en] as Record<string, unknown> | undefined;
    
    // Final fallback with default values
    const finalFallback = {
      title: 'Page Title',
      subtitle: 'Page Subtitle',
      description: 'Page description',
      ...defaultValues
    };

    // Merge in priority order
    return {
      ...finalFallback,
      ...enT,
      ...fallbackT,
      ...primaryT
    };
  }, [t, translationKey, fallbackKey, defaultValues]);

  const getNestedValue = (path: string, fallback?: unknown) => {
    const keys = path.split('.');
    let value = safeTranslations;
    
    for (const key of keys) {
      value = value?.[key];
      if (value === undefined) {
        return fallback;
      }
    }
    
    return value || fallback;
  };

  return {
    translations: safeTranslations,
    get: getNestedValue,
    // Convenience methods for common patterns
    title: safeTranslations.title,
    subtitle: safeTranslations.subtitle,
    description: safeTranslations.description
  };
};

export default useTranslationFallback;
