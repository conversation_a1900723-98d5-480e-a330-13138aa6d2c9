import { render, screen } from '@testing-library/react';
import React from 'react';
import { vi, describe, it, expect, beforeEach } from 'vitest';

import { TestWrapper } from '@/lib/test-utils';
import Index from '@/pages/Index';

// Mock all the dependencies to isolate the issue
vi.mock('@/components/CTASection', () => ({
  default: () => <div data-testid="cta-section">CTA Section</div>
}));

vi.mock('@/components/HeroSection', () => ({
  default: () => <div data-testid="hero-section">Hero Section</div>
}));

vi.mock('@/components/IndependentReviewsSection', () => ({
  default: () => <div data-testid="reviews-section">Reviews Section</div>
}));

vi.mock('@/components/SafeImage', () => ({
  default: ({ alt, ...props }: React.ImgHTMLAttributes<HTMLImageElement> & { alt: string }) => <img alt={alt} data-testid="safe-image" {...props} />
}));

vi.mock('@/components/StandardPageLayout', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <main role="main" data-testid="standard-page-layout">
      {children}
    </main>
  )
}));

vi.mock('@/lib/seo', () => ({
  generatePageSEO: vi.fn(() => ({
    title: 'Test Title',
    description: 'Test Description',
    keywords: 'test, keywords'
  })),
  generateMedicalPracticeStructuredData: vi.fn(() => ({})),
  PRACTICE_DATA: {
    name: 'Test Practice',
    address: 'Test Address'
  }
}));

vi.mock('@/locales/en', () => ({
  default: {
    home: {
      hero: {
        title: 'Test Hero Title',
        subtitle: 'Test Hero Subtitle'
      },
      services: {
        title: 'Test Services Title'
      }
    },
    navigation: {
      home: 'Home'
    }
  }
}));

describe('Index Component Debug', () => {
  beforeEach(() => {
    // Suppress console errors for cleaner test output
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  it('should render without throwing errors', () => {
    let renderError: Error | null = null;

    try {
      render(
        <TestWrapper disableErrorBoundary={true}>
          <Index />
        </TestWrapper>
      );
    } catch (error) {
      renderError = error as Error;
      if (import.meta.env.DEV) {
        console.log('Render Error:', renderError.message);
      }
      if (import.meta.env.DEV) {
        console.log('Stack:', renderError.stack);
      }
    }

    if (!renderError) {
      // Check what actually rendered
      const mainElements = screen.queryAllByRole('main');
      const heroSection = screen.queryByTestId('hero-section');

      if (import.meta.env.DEV) {

        console.log('Main Elements Count:', mainElements.length);

      }
      if (import.meta.env.DEV) {
        console.log('Hero Section Present:', !!heroSection);
      }

      // The test should pass if no error occurred
      expect(mainElements.length).toBeGreaterThan(0);
    } else {
      // If there was an error, we want to see it
      if (import.meta.env.DEV) {
        console.log('Component threw error during render');
      }
      throw renderError;
    }
  });

  it('should render with minimal mocking', () => {
    // Try with even more minimal setup
    const MinimalIndex = () => {
      try {
        return <Index />;
      } catch (error) {
        if (import.meta.env.DEV) {
          console.log('Component Error:', error);
        }
        return <div data-testid="component-error">Component Error: {String(error)}</div>;
      }
    };

    render(
      <TestWrapper>
        <MinimalIndex />
      </TestWrapper>
    );

    // Check what rendered
    const componentError = screen.queryByTestId('component-error');
    const errorBoundary = screen.queryByText(/something went wrong/i);
    
    if (componentError) {
      if (import.meta.env.DEV) {
        console.log('Component threw error:', componentError.textContent);
      }
    }
    
    if (errorBoundary) {
      if (import.meta.env.DEV) {
        console.log('Error boundary caught error');
      }
    }

    expect(true).toBe(true);
  });
});
