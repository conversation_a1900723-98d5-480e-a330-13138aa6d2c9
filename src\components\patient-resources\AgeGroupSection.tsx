import { CheckCircle2 } from 'lucide-react';
import React from 'react';

import { AgeGroupRecommendation } from '@/data/patient-resources/ageSpecificRecommendationsData';

interface AgeGroupSectionProps {
  ageGroup: AgeGroupRecommendation;
}

const AgeGroupSection: React.FC<AgeGroupSectionProps> = ({ ageGroup }) => {
  return (
    <div className="border border-primary/20 p-6 rounded-lg mb-12">
      <h2 className="text-enhanced-heading text-2xl font-bold mb-4">{ageGroup.title}</h2>

      <div className="bg-muted p-4 rounded-md mb-6">
        <h3 className="text-xl font-semibold mb-3">Common Spine Issues</h3>
        <ul className="space-y-2">
          {ageGroup.commonIssues.map((issue, index) => (
            <li key={index}>{issue}</li>
          ))}
        </ul>
      </div>

      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-3">Spinal Care Priorities</h3>
        <ul className="space-y-3">
          {ageGroup.priorities.map((priority, index) => (
            <li key={index} className="flex items-start">
              <CheckCircle2 className="h-6 w-6 text-primary mr-2 flex-shrink-0 mt-0.5" />
              <span>
                <strong>{priority.title}</strong> – {priority.description}
              </span>
            </li>
          ))}
        </ul>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-muted p-4 rounded-md">
          <h4 className="font-semibold mb-2">Strength</h4>
          <ul className="space-y-1 text-sm">
            {ageGroup.exercises.strength.map((exercise, index) => (
              <li key={index}>{exercise}</li>
            ))}
          </ul>
        </div>

        <div className="bg-muted p-4 rounded-md">
          <h4 className="font-semibold mb-2">
            {ageGroup.exercises.flexibility.length > 0 ? 'Flexibility' : 'Mobility'}
          </h4>
          <ul className="space-y-1 text-sm">
            {ageGroup.exercises.flexibility.map((exercise, index) => (
              <li key={index}>{exercise}</li>
            ))}
          </ul>
        </div>

        <div className="bg-muted p-4 rounded-md">
          <h3 className="font-semibold mb-2">Cardio</h3>
          <ul className="space-y-1 text-sm">
            {ageGroup.exercises.cardio.map((exercise, index) => (
              <li key={index}>{exercise}</li>
            ))}
          </ul>
        </div>
      </div>

      <div className="bg-primary/10 p-4 rounded-md">
        <h3 className="font-semibold mb-2">{ageGroup.caseExample.title}</h3>
        <p className="text-sm">{ageGroup.caseExample.description}</p>
      </div>
    </div>
  );
};

AgeGroupSection.displayName = 'AgeGroupSection';

export default AgeGroupSection;
