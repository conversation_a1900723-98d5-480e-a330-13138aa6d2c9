import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import ExpertiseCallToAction from '@/components/expertise/ExpertiseCallToAction';
import ExpertiseHero from '@/components/expertise/ExpertiseHero';
import ExpertiseSidebar from '@/components/expertise/ExpertiseSidebar';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { imageGuidedSpineSurgeryData } from '@/data/expertise/imageGuidedSpineSurgeryData';

/**
 * Image-Guided Spine Surgery Component
 * Comprehensive spine surgery page with specialised content from documentation
 * Focuses on spine-specific applications of image-guided surgery
 */

const ImageGuidedSpineSurgery: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const data = imageGuidedSpineSurgeryData;

  return (
    <StandardPageLayout title="Image-Guided Spine Surgery" showHeader={false}>
      <ExpertiseHero
        title={data.hero.title}
        subtitle={data.hero.subtitle}
        backgroundImage={data.hero.backgroundImage}
      />

      <div className="flex-1">
        {/* Modern Navigation Header */}
        <section className="py-6 bg-muted/30 border-b">
          <div className="container px-4 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Link
                  to="/expertise/image-guided-surgery"
                  className="flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors group"
                >
                  <svg className="w-5 h-5 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  <span className="text-sm font-medium">Image-Guided Surgery</span>
                </Link>
              </div>
              <div className="text-right">
                <h1 className="text-xl font-bold text-foreground">Spine Surgery</h1>
                <p className="text-sm text-muted-foreground">Sub-millimetre Precision</p>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                {/* Executive Summary */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.executiveSummary.title}</h2>
                  {data.executiveSummary.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-6">
                      {paragraph}
                    </p>
                  ))}
                </div>

                {/* Overview */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.overview.title}</h2>
                  {data.overview.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-6">
                      {paragraph}
                    </p>
                  ))}
                  
                  <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                    <SafeImage
                      src={data.images.overview}
                      alt="Image-guided spine surgery navigation system"
                      className="w-full h-full object-cover"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  </div>

                  {/* Core Components */}
                  <div className="mt-8">
                    <h3 className="text-xl font-semibold mb-4">{data.overview.coreComponents.title}</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {data.overview.coreComponents.items.map((component, index) => (
                        <div key={index} className="border rounded-lg p-4 medical-card">
                          <h4 className="font-semibold text-primary mb-2">{component.name}</h4>
                          <p className="text-sm text-muted-foreground">{component.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Imaging Modalities */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.imagingModalities.title}</h2>
                  <p className="text-muted-foreground mb-6">{data.imagingModalities.introduction}</p>
                  
                  <div className="space-y-4">
                    {data.imagingModalities.modalities.map((modality, index) => (
                      <div key={index} className="border rounded-lg p-6 medical-card">
                        <h3 className="text-lg font-semibold mb-4 text-primary">{modality.modality}</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="font-semibold text-success mb-2">Key Strengths</h4>
                            <ul className="list-disc pl-5 space-y-1 text-sm text-muted-foreground">
                              {modality.strengths.map((strength, sIndex) => (
                                <li key={sIndex}>{strength}</li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <h4 className="font-semibold text-info mb-2">Key Limitations</h4>
                            <ul className="list-disc pl-5 space-y-1 text-sm text-muted-foreground">
                              {modality.limitations.map((limitation, lIndex) => (
                                <li key={lIndex}>{limitation}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Workflow */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.workflow.title}</h2>
                  <p className="text-muted-foreground mb-6">{data.workflow.introduction}</p>
                  
                  <div className="space-y-4 mb-6">
                    {data.workflow.steps.map((step, index) => (
                      <div key={index} className="flex items-start space-x-4 p-4 bg-muted/30 rounded-lg">
                        <div className="flex-shrink-0 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                          {index + 1}
                        </div>
                        <div>
                          <h3 className="font-semibold mb-2">{step.step}</h3>
                          <p className="text-muted-foreground text-sm">{step.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="bg-info border border-info p-4 rounded-lg">
                    <p className="text-info text-sm">{data.workflow.additionalInfo}</p>
                  </div>
                </div>

                {/* Conditions */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.conditions.title}</h2>
                  <p className="text-muted-foreground mb-6">{data.conditions.introduction}</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {data.conditions.conditions.map((condition, index) => (
                      <div key={index} className="flex items-center text-sm text-foreground-foreground bg-muted/30 p-3 rounded">
                        <span className="w-2 h-2 bg-primary rounded-full mr-3"></span>
                        {condition}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Accuracy Benefits */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.accuracyBenefits.title}</h2>
                  {data.accuracyBenefits.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-4">
                      {paragraph}
                    </p>
                  ))}
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                    {data.accuracyBenefits.metrics.map((metric, index) => (
                      <div key={index} className="text-center p-4 bg-primary/5 rounded-lg">
                        <div className="text-enhanced-heading text-2xl font-bold text-primary mb-2">{metric.value}</div>
                        <div className="text-sm font-semibold mb-1">{metric.metric}</div>
                        <div className="text-xs text-muted-foreground">{metric.description}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Patient Benefits */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.patientBenefits.title}</h2>
                  {data.patientBenefits.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-4">
                      {paragraph}
                    </p>
                  ))}
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                    {data.patientBenefits.benefits.map((benefit, index) => (
                      <div key={index} className="flex items-center text-sm text-muted-foreground bg-success border border-success p-3 rounded">
                        <span className="w-2 h-2 bg-success rounded-full mr-3"></span>
                        {benefit}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Related Pages Navigation */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Related Image-Guided Procedures</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="card p-6 rounded-lg shadow-md medical-card border-l-4 border-l-primary">
                      <h3 className="text-xl font-semibold mb-3 text-primary">Image-Guided Surgery Overview</h3>
                      <p className="text-muted-foreground mb-4">
                        Learn about the general principles and applications of image-guided surgery across neurosurgery.
                      </p>
                      <Button asChild variant="outline">
                        <Link to="/expertise/image-guided-surgery">Learn More</Link>
                      </Button>
                    </div>
                    
                    <div className="card p-6 rounded-lg shadow-md medical-card border-l-4 border-l-secondary">
                      <h3 className="text-xl font-semibold mb-3 text-secondary">Image-Guided Brain Surgery</h3>
                      <p className="text-muted-foreground mb-4">
                        Discover how image-guided technology enhances precision in brain tumor and vascular procedures.
                      </p>
                      <Button asChild variant="outline">
                        <Link to="/expertise/image-guided-brain-surgery">Learn More</Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <ExpertiseSidebar sections={data.sidebar} />
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <ExpertiseCallToAction
          title={data.callToAction.title}
          description={data.callToAction.description}
          primaryButton={data.callToAction.primaryButton}
          secondaryButton={data.callToAction.secondaryButton}
        />
      </div>
    </StandardPageLayout>
  );
};

ImageGuidedSpineSurgery.displayName = 'ImageGuidedSpineSurgery';

export default ImageGuidedSpineSurgery;
