import {
  Brain,
  Users,
  TrendingUp,
  AlertTriangle,
  Search,
  ArrowRight,
  CheckCircle,
  Clock,
  Stethoscope,
  Activity,
  Shield,
  Filter,
  Star,
  Zap,
  Eye
} from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import AppointmentCallToActionSection from '@/components/patient-resources/AppointmentCallToActionSection';
import SafeImage from '@/components/SafeImage';
import { SectionHeader, GridLayout } from '@/components/shared/CommonSectionPatterns';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface BrainCondition {
  id: string;
  name: string;
  description: string;
  prevalence: string;
  urgency: 'emergency' | 'urgent' | 'routine';
  symptoms: string[];
  path: string;
  frequency: number; // 1-9, 1 being most common
  category: 'tumour' | 'vascular' | 'functional' | 'structural';
  treatmentOptions: string[];
  keyFeatures: string[];
  valueProposition: string;
  estimatedReadTime: string;
}

const brainConditions: BrainCondition[] = [
  {
    id: 'brain-tumour',
    name: 'Brain Tumour',
    description: 'Abnormal growth of cells within the brain or central nervous system, requiring specialised diagnosis and treatment.',
    prevalence: '24 per 100,000 people',
    urgency: 'urgent',
    symptoms: ['Persistent headaches', 'Seizures', 'Cognitive changes', 'Vision problems'],
    path: '/patient-resources/conditions/brain-tumour',
    frequency: 1,
    category: 'tumour',
    treatmentOptions: ['Surgical Resection', 'Radiation Therapy', 'Chemotherapy', 'Image-Guided Surgery'],
    keyFeatures: ['Advanced Imaging', 'Minimally Invasive Options', 'Multidisciplinary Care', 'Personalised Treatment'],
    valueProposition: 'Comprehensive brain tumour care with advanced surgical techniques and personalised treatment plans.',
    estimatedReadTime: '12-15 min'
  },
  {
    id: 'trigeminal-neuralgia',
    name: 'Trigeminal Neuralgia',
    description: 'Severe facial pain caused by dysfunction of the trigeminal nerve, often described as electric shock-like pain.',
    prevalence: '12 per 100,000 people',
    urgency: 'urgent',
    symptoms: ['Sharp facial pain', 'Triggered by light touch', 'Pain in jaw/cheek', 'Brief pain episodes'],
    path: '/patient-resources/conditions/trigeminal-neuralgia',
    frequency: 2,
    category: 'functional',
    treatmentOptions: ['Microvascular Decompression', 'Gamma Knife', 'Medication Management', 'Balloon Compression'],
    keyFeatures: ['Pain-Free Solutions', 'Minimally Invasive Options', 'High Success Rates', 'Rapid Recovery'],
    valueProposition: 'Effective pain relief through proven surgical and non-surgical treatments.',
    estimatedReadTime: '10-12 min'
  },
  {
    id: 'cerebral-aneurysm',
    name: 'Cerebral Aneurysm',
    description: 'Weakened area in a brain blood vessel that can rupture, potentially causing life-threatening bleeding.',
    prevalence: '3-5% of population',
    urgency: 'emergency',
    symptoms: ['Sudden severe headache', 'Neck stiffness', 'Vision changes', 'Loss of consciousness'],
    path: '/patient-resources/conditions/cerebral-aneurysm',
    frequency: 3,
    category: 'vascular',
    treatmentOptions: ['Endovascular Coiling', 'Surgical Clipping', 'Flow Diverters', 'Stent-Assisted Coiling'],
    keyFeatures: ['Emergency Care', 'Advanced Imaging', 'Minimally Invasive Options', 'Rapid Intervention'],
    valueProposition: 'Life-saving aneurysm treatment with advanced endovascular and surgical techniques.',
    estimatedReadTime: '8-10 min'
  },
  {
    id: 'hemifacial-spasm',
    name: 'Hemifacial Spasm',
    description: 'Involuntary muscle contractions on one side of the face, typically caused by blood vessel compression.',
    prevalence: '8 per 100,000 people',
    urgency: 'routine',
    symptoms: ['Facial twitching', 'Eye closure spasms', 'Mouth pulling', 'Progressive symptoms'],
    path: '/patient-resources/conditions/hemifacial-spasm',
    frequency: 4,
    category: 'functional',
    treatmentOptions: ['Microvascular Decompression', 'Botox Injections', 'Medication Management', 'Surgical Options'],
    keyFeatures: ['Symptom Relief', 'Minimally Invasive Options', 'High Success Rates', 'Quality of Life Improvement'],
    valueProposition: 'Effective treatment for facial spasms with proven surgical and non-surgical approaches.',
    estimatedReadTime: '8-10 min'
  },
  {
    id: 'cerebral-meningioma',
    name: 'Cerebral Meningioma',
    description: 'Usually benign tumour arising from the protective membranes covering the brain and spinal cord.',
    prevalence: '8 per 100,000 people',
    urgency: 'routine',
    symptoms: ['Gradual headaches', 'Seizures', 'Weakness', 'Personality changes'],
    path: '/patient-resources/conditions/cerebral-meningioma',
    frequency: 5,
    category: 'tumour',
    treatmentOptions: ['Surgical Resection', 'Stereotactic Radiosurgery', 'Observation', 'Radiation Therapy'],
    keyFeatures: ['Precise Surgery', 'Brain Preservation', 'Excellent Outcomes', 'Comprehensive Care'],
    valueProposition: 'Expert meningioma treatment with focus on brain function preservation and optimal outcomes.',
    estimatedReadTime: '10-12 min'
  },
  {
    id: 'hydrocephalus',
    name: 'Hydrocephalus',
    description: 'Accumulation of cerebrospinal fluid in the brain, causing increased pressure and potential brain damage.',
    prevalence: '1-2 per 1,000 births',
    urgency: 'urgent',
    symptoms: ['Enlarged head (infants)', 'Headaches', 'Nausea', 'Balance problems'],
    path: '/patient-resources/conditions/hydrocephalus',
    frequency: 6,
    category: 'structural',
    treatmentOptions: ['Ventriculoperitoneal Shunt', 'Endoscopic Third Ventriculostomy', 'Lumboperitoneal Shunt', 'External Ventricular Drain'],
    keyFeatures: ['Pressure Relief', 'Advanced Shunt Systems', 'Minimally Invasive Options', 'Long-term Management'],
    valueProposition: 'Comprehensive hydrocephalus management with advanced shunt technology and ongoing care.',
    estimatedReadTime: '10-12 min'
  },
  {
    id: 'cerebral-avm',
    name: 'Cerebral AVM',
    description: 'Abnormal connection between arteries and veins in the brain, bypassing normal brain tissue.',
    prevalence: '1 per 100,000 people',
    urgency: 'urgent',
    symptoms: ['Seizures', 'Headaches', 'Neurological deficits', 'Brain hemorrhage'],
    path: '/patient-resources/conditions/cerebral-avm',
    frequency: 7,
    category: 'vascular',
    treatmentOptions: ['Surgical Resection', 'Endovascular Embolisation', 'Stereotactic Radiosurgery', 'Combined Approach'],
    keyFeatures: ['Bleeding Prevention', 'Advanced Imaging', 'Multidisciplinary Care', 'Seizure Control'],
    valueProposition: 'Expert AVM treatment with advanced surgical and endovascular techniques for optimal outcomes.',
    estimatedReadTime: '12-15 min'
  },
  {
    id: 'cerebral-cavernoma',
    name: 'Cerebral Cavernoma',
    description: 'Cluster of abnormal blood vessels in the brain that can cause seizures and bleeding.',
    prevalence: '0.5% of population',
    urgency: 'routine',
    symptoms: ['Seizures', 'Headaches', 'Neurological symptoms', 'Brain hemorrhage'],
    path: '/patient-resources/conditions/cerebral-cavernoma',
    frequency: 8,
    category: 'vascular',
    treatmentOptions: ['Surgical Resection', 'Observation', 'Seizure Management', 'Stereotactic Surgery'],
    keyFeatures: ['Precise Surgery', 'Seizure Control', 'Brain Function Preservation', 'Risk Assessment'],
    valueProposition: 'Specialised cavernoma treatment with focus on seizure control and neurological preservation.',
    estimatedReadTime: '8-10 min'
  },
  {
    id: 'chiari-malformation',
    name: 'Chiari Malformation',
    description: 'Structural defect where brain tissue extends into the spinal canal, affecting cerebrospinal fluid flow.',
    prevalence: '1 per 1,000 people',
    urgency: 'routine',
    symptoms: ['Neck pain', 'Headaches', 'Balance problems', 'Numbness'],
    path: '/patient-resources/conditions/chiari-malformation',
    frequency: 9,
    category: 'structural',
    treatmentOptions: ['Posterior Fossa Decompression', 'Duraplasty', 'Syrinx Drainage', 'Conservative Management'],
    keyFeatures: ['Symptom Relief', 'CSF Flow Restoration', 'Minimally Invasive Options', 'Comprehensive Care'],
    valueProposition: 'Expert Chiari treatment with advanced surgical techniques for symptom relief and improved quality of life.',
    estimatedReadTime: '10-12 min'
  }
];

const BrainConditions: React.FC = () => {
  const deviceInfo = useDeviceDetection();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUrgency, setSelectedUrgency] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Filter conditions based on search, urgency, and category
  const filteredConditions = brainConditions
    .filter(condition =>
      condition.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      condition.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      condition.symptoms.some(symptom => symptom.toLowerCase().includes(searchTerm.toLowerCase())) ||
      condition.treatmentOptions.some(treatment => treatment.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .filter(condition => selectedUrgency === 'all' || condition.urgency === selectedUrgency)
    .filter(condition => selectedCategory === 'all' || condition.category === selectedCategory)
    .sort((a, b) => a.frequency - b.frequency);

  // Category statistics
  const categoryStats = {
    tumour: brainConditions.filter(c => c.category === 'tumour').length,
    vascular: brainConditions.filter(c => c.category === 'vascular').length,
    functional: brainConditions.filter(c => c.category === 'functional').length,
    structural: brainConditions.filter(c => c.category === 'structural').length
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'emergency': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'urgent': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'routine': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'emergency': return AlertTriangle;
      case 'urgent': return TrendingUp;
      case 'routine': return Users;
      default: return Brain;
    }
  };

  return (
    <StandardPageLayout showHeader={false}>
      <Helmet>
        <title>Brain Conditions | Expert Neurosurgical Care | miNEURO</title>
        <meta
          name="description"
          content="Comprehensive guide to 9 brain conditions including brain tumours, aneurysms, trigeminal neuralgia, and more. Expert neurosurgical care with advanced treatment options and personalised care pathways."
        />
        <meta name="keywords" content="brain conditions, brain tumour, cerebral aneurysm, trigeminal neuralgia, neurosurgery, brain surgery, hydrocephalus, AVM, meningioma, chiari malformation" />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/brain-conditions" />
      </Helmet>

      <PageHeader
        title="Brain Conditions"
        subtitle="Expert neurosurgical care for 9 brain conditions with advanced treatment options, comprehensive information, and personalised care pathways"
        backgroundImage="/images/brain-conditions/brain-anatomy-hero.jpg"
        enableParallax={true}
      />

      <main className="flex-1">
        {/* Overview Section */}
        <section className="py-16 bg-gradient-to-br from-primary/5 to-background">
          <div className="container max-w-7xl">
            <div className="text-center mb-12">
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4">
                Comprehensive Brain Condition Care
              </h2>
              <p className="text-enhanced-body max-w-4xl mx-auto text-lg">
                Expert neurosurgical care for 9 brain conditions with advanced treatment options,
                personalised care pathways, and comprehensive support throughout your journey.
              </p>
            </div>

            {/* Category Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              <Card className="text-center p-6 border-2 hover:border-primary/30 transition-colors">
                <CardContent className="p-0">
                  <Brain className="h-10 w-10 text-primary mx-auto mb-3" />
                  <h3 className="text-xl font-bold text-enhanced-heading mb-2">Tumour Conditions</h3>
                  <p className="text-sm text-muted-foreground mb-2">{categoryStats.tumour} Conditions</p>
                  <p className="text-xs text-muted-foreground">Brain tumours and meningiomas</p>
                </CardContent>
              </Card>
              <Card className="text-center p-6 border-2 hover:border-primary/30 transition-colors">
                <CardContent className="p-0">
                  <Activity className="h-10 w-10 text-primary mx-auto mb-3" />
                  <h3 className="text-xl font-bold text-enhanced-heading mb-2">Vascular Conditions</h3>
                  <p className="text-sm text-muted-foreground mb-2">{categoryStats.vascular} Conditions</p>
                  <p className="text-xs text-muted-foreground">Aneurysms, AVMs, cavernomas</p>
                </CardContent>
              </Card>
              <Card className="text-center p-6 border-2 hover:border-primary/30 transition-colors">
                <CardContent className="p-0">
                  <Zap className="h-10 w-10 text-primary mx-auto mb-3" />
                  <h3 className="text-xl font-bold text-enhanced-heading mb-2">Functional Conditions</h3>
                  <p className="text-sm text-muted-foreground mb-2">{categoryStats.functional} Conditions</p>
                  <p className="text-xs text-muted-foreground">Trigeminal neuralgia, spasms</p>
                </CardContent>
              </Card>
              <Card className="text-center p-6 border-2 hover:border-primary/30 transition-colors">
                <CardContent className="p-0">
                  <Shield className="h-10 w-10 text-primary mx-auto mb-3" />
                  <h3 className="text-xl font-bold text-enhanced-heading mb-2">Structural Conditions</h3>
                  <p className="text-sm text-muted-foreground mb-2">{categoryStats.structural} Conditions</p>
                  <p className="text-xs text-muted-foreground">Hydrocephalus, Chiari malformation</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Search and Filter Section */}
        <section className="py-16 bg-muted/30">
          <div className="container max-w-7xl">
            <div className="text-center mb-8">
              <h2 className="text-enhanced-heading text-2xl font-bold mb-2">
                Find Your Condition
              </h2>
              <p className="text-enhanced-body text-muted-foreground">
                Search by condition name, symptoms, or treatment options
              </p>
            </div>

            <div className={cn(
              "flex gap-4 mb-8",
              deviceInfo.isMobile ? "flex-col" : "flex-row items-center"
            )}>
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search conditions, symptoms, or treatments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="flex gap-2">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-[140px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="tumour">Tumour</SelectItem>
                    <SelectItem value="vascular">Vascular</SelectItem>
                    <SelectItem value="functional">Functional</SelectItem>
                    <SelectItem value="structural">Structural</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={selectedUrgency} onValueChange={setSelectedUrgency}>
                  <SelectTrigger className="w-[120px]">
                    <Clock className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Urgency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Urgency</SelectItem>
                    <SelectItem value="emergency">Emergency</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                    <SelectItem value="routine">Routine</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="text-center text-sm text-muted-foreground">
              Showing {filteredConditions.length} of {brainConditions.length} conditions
            </div>
          </div>
        </section>

        {/* Conditions Grid */}
        <section className="py-16">
          <div className="container max-w-7xl">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredConditions.map((condition) => {
                const UrgencyIcon = getUrgencyIcon(condition.urgency);
                return (
                  <Card
                    key={condition.id}
                    className="group hover:shadow-xl transition-all duration-300 border-2 hover:border-primary/20 h-full flex flex-col"
                  >
                    <CardHeader className="pb-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <div className="p-2 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                            <Brain className="h-5 w-5 text-primary" />
                          </div>
                          <Badge className={getUrgencyColor(condition.urgency)}>
                            {condition.urgency}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          {condition.estimatedReadTime}
                        </div>
                      </div>

                      <CardTitle className="text-xl font-bold group-hover:text-primary transition-colors mb-2">
                        {condition.name}
                      </CardTitle>

                      <div className="flex items-center gap-2 mb-3">
                        <Badge variant="outline" className="text-xs">
                          {condition.category}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {condition.prevalence}
                        </span>
                      </div>

                      <CardDescription className="text-sm leading-relaxed">
                        {condition.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="flex-1 pb-4">
                      <div className="mb-4">
                        <h4 className="font-semibold mb-2 text-sm flex items-center gap-1">
                          <Stethoscope className="h-4 w-4 text-primary" />
                          Key Symptoms:
                        </h4>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {condition.symptoms.slice(0, 3).map((symptom, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <CheckCircle className="h-3 w-3 text-primary flex-shrink-0" />
                              {symptom}
                            </li>
                          ))}
                          {condition.symptoms.length > 3 && (
                            <li className="text-xs text-muted-foreground italic">
                              +{condition.symptoms.length - 3} more symptoms
                            </li>
                          )}
                        </ul>
                      </div>

                      <div className="mb-4">
                        <h4 className="font-semibold mb-2 text-sm flex items-center gap-1">
                          <Star className="h-4 w-4 text-primary" />
                          Treatment Options:
                        </h4>
                        <div className="flex flex-wrap gap-1">
                          {condition.treatmentOptions.slice(0, 2).map((treatment, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {treatment}
                            </Badge>
                          ))}
                          {condition.treatmentOptions.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{condition.treatmentOptions.length - 2} more
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div className="mb-4 p-3 bg-primary/5 rounded-lg">
                        <p className="text-sm text-primary font-medium">
                          {condition.valueProposition}
                        </p>
                      </div>
                    </CardContent>

                    <div className="p-6 pt-0">
                      <Button asChild className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                        <Link to={condition.path} className="flex items-center justify-center gap-2">
                          <span>Learn More</span>
                          <ArrowRight className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </Card>
                );
              })}
            </div>

            {filteredConditions.length === 0 && (
              <div className="text-center py-16">
                <Brain className="h-20 w-20 text-muted-foreground mx-auto mb-6" />
                <h3 className="text-2xl font-bold mb-4">No conditions found</h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  Try adjusting your search terms or filters to find the information you're looking for.
                </p>
                <div className="flex gap-2 justify-center">
                  <Button
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedUrgency('all');
                      setSelectedCategory('all');
                    }}
                  >
                    Clear All Filters
                  </Button>
                  <Button variant="outline" asChild>
                    <Link to="/patient-resources">
                      Browse All Resources
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </div>
        </section>

        {/* Comprehensive Brain Anatomy */}
        <section className="py-20 bg-gradient-to-br from-muted/20 to-background">
          <div className="container max-w-7xl">
            <div className="text-center mb-16">
              <h2 className="text-enhanced-heading text-4xl font-bold mb-6">
                Understanding Your Brain Anatomy
              </h2>
              <p className="text-enhanced-body text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                The human brain is the most complex organ in your body, controlling everything from basic functions
                to complex thoughts and emotions. Understanding brain anatomy helps you appreciate how different
                conditions affect specific regions and why targeted treatment is essential.
              </p>
            </div>

            {/* Main Brain Overview with Image */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
              <div className="space-y-6">
                <h3 className="text-enhanced-heading text-2xl font-bold mb-4">
                  The Human Brain Structure
                </h3>
                <p className="text-enhanced-body text-lg leading-relaxed">
                  Your brain weighs approximately 1.4 kilograms and contains over 86 billion neurons. It's divided into
                  distinct regions, each with specialised functions that work together to control your body and mind.
                </p>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Brain className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading">Cerebrum</h4>
                      <p className="text-muted-foreground text-sm">Largest part controlling thinking, memory, and voluntary movements</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Activity className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading">Cerebellum</h4>
                      <p className="text-muted-foreground text-sm">Controls balance, coordination, and fine motor skills</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Zap className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading">Brainstem</h4>
                      <p className="text-muted-foreground text-sm">Controls vital functions like breathing and heart rate</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="relative">
                <SafeImage
                  src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Detailed brain anatomy illustration showing cerebrum, cerebellum, and brainstem structures"
                  className="w-full h-[400px] object-cover rounded-xl shadow-2xl"
                  fallbackSrc="/images/brain-conditions/brain-anatomy.jpg"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl" />
              </div>
            </div>

            {/* Brain Lobes and Functions */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              <Card className="border-2 hover:border-primary/30 transition-colors">
                <CardHeader className="text-center pb-4">
                  <div className="p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <Brain className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Frontal Lobe</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-muted-foreground mb-4">
                    Controls personality, decision-making, and motor functions.
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Executive functions</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Motor control</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Speech production</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 hover:border-primary/30 transition-colors">
                <CardHeader className="text-center pb-4">
                  <div className="p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <Eye className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Parietal Lobe</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-muted-foreground mb-4">
                    Processes sensory information and spatial awareness.
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Touch sensation</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Spatial processing</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Integration</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 hover:border-primary/30 transition-colors">
                <CardHeader className="text-center pb-4">
                  <div className="p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <Activity className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Temporal Lobe</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-muted-foreground mb-4">
                    Handles hearing, language, and memory formation.
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Auditory processing</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Memory formation</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Language comprehension</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 hover:border-primary/30 transition-colors">
                <CardHeader className="text-center pb-4">
                  <div className="p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <Eye className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Occipital Lobe</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-muted-foreground mb-4">
                    Primary visual processing center of the brain.
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Visual processing</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Image interpretation</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Depth perception</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Brain Protection and Circulation */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <div className="space-y-8">
                <h3 className="text-enhanced-heading text-2xl font-bold">
                  Brain Protection & Circulation
                </h3>
                <div className="space-y-6">
                  <div className="flex items-start gap-4 p-4 bg-white/50 rounded-lg">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Shield className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading mb-2">Protective Layers</h4>
                      <p className="text-muted-foreground text-sm mb-2">
                        Three layers of meninges (dura mater, arachnoid mater, pia mater) protect the brain
                      </p>
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="outline" className="text-xs">Skull Protection</Badge>
                        <Badge variant="outline" className="text-xs">CSF Cushioning</Badge>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-4 bg-white/50 rounded-lg">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Activity className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading mb-2">Blood Supply</h4>
                      <p className="text-muted-foreground text-sm mb-2">
                        Complex vascular network delivers oxygen and nutrients while removing waste
                      </p>
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="outline" className="text-xs">Arterial System</Badge>
                        <Badge variant="outline" className="text-xs">Venous Drainage</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="relative">
                <SafeImage
                  src="https://images.pexels.com/photos/5327585/pexels-photo-5327585.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="Medical illustration showing brain protection layers and blood circulation system"
                  className="w-full h-[350px] object-cover rounded-xl shadow-2xl"
                  fallbackSrc="/images/brain-conditions/brain-circulation.jpg"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl" />
              </div>
            </div>
          </div>
        </section>

        {/* Appointment Call to Action */}
        <AppointmentCallToActionSection
          title="Expert Brain Condition Care"
          description="Our specialised neurosurgical team provides comprehensive diagnosis and treatment for all brain conditions. Schedule a consultation to discuss your specific needs and explore personalised treatment options."
          primaryButtonText="Book Consultation"
          primaryButtonLink="/appointments"
          secondaryButtonText="Contact Us"
          secondaryButtonLink="/contact"
        />
      </main>
    </StandardPageLayout>
  );
};

export default BrainConditions;
