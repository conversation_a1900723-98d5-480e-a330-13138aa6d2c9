/**
 * Medical Conditions Components Barrel Export
 * Provides clean imports for all medical condition components
 */

// Core components
export { default as ConditionHero } from './ConditionHero';
export { default as ConditionOverview } from './ConditionOverview';
export { default as DegenerationProcess } from './DegenerationProcess';

// New reusable medical condition components
export { default as MedicalConditionHero } from './MedicalConditionHero';
export { default as MedicalConditionQuickFacts } from './MedicalConditionQuickFacts';
export { default as MedicalConditionOverview } from './MedicalConditionOverview';
export { default as MedicalConditionTabs } from './MedicalConditionTabs';
export { default as CausesAndRiskFactors } from './CausesAndRiskFactors';

// Specialised condition components
export { default as RadiculopathyTypesSection } from './RadiculopathyTypesSection';
export { default as SpinalStenosisTypesSection } from './SpinalStenosisTypesSection';

// Types
export type * from './types';

// New component types
export type { MedicalConditionHeroProps } from './MedicalConditionHero';
export type { MedicalConditionQuickFactsProps, QuickFact } from './MedicalConditionQuickFacts';
export type { MedicalConditionOverviewProps } from './MedicalConditionOverview';
export type { MedicalConditionTabsProps, TabContent } from './MedicalConditionTabs';
export type { CausesAndRiskFactorsProps, CauseRiskFactor } from './CausesAndRiskFactors';