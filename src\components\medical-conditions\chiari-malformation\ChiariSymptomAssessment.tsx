import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>cle, <PERSON><PERSON>dingUp, ArrowRight, ArrowLeft, Activity, Zap } from 'lucide-react';
import React, { useState, useId } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface AssessmentQuestion {
  id: string;
  question: string;
  description?: string;
  options: Array<{
    value: string;
    label: string;
    score: number;
  }>;
}

interface AssessmentResult {
  totalScore: number;
  likelihood: 'low' | 'moderate' | 'high';
  recommendation: string;
  urgency: 'routine' | 'urgent' | 'prompt';
  nextSteps: string[];
  symptoms: string[];
}

const assessmentQuestions: AssessmentQuestion[] = [
  {
    id: 'headaches',
    question: 'Do you experience headaches, particularly at the back of your head?',
    description: 'Occipital headaches are the most common symptom of Chiari malformation',
    options: [
      { value: 'severe-triggered', label: 'Yes, severe headaches triggered by coughing/straining', score: 5 },
      { value: 'frequent-occipital', label: 'Yes, frequent headaches at back of head', score: 4 },
      { value: 'occasional-headaches', label: 'Yes, occasional headaches', score: 2 },
      { value: 'no-headaches', label: 'No headaches', score: 0 }
    ]
  },
  {
    id: 'balance-coordination',
    question: 'Do you have problems with balance, coordination, or walking?',
    description: 'Cerebellar dysfunction can cause balance and coordination problems',
    options: [
      { value: 'severe-balance', label: 'Yes, significant balance problems affecting daily activities', score: 4 },
      { value: 'moderate-coordination', label: 'Yes, noticeable coordination difficulties', score: 3 },
      { value: 'mild-unsteadiness', label: 'Yes, mild unsteadiness or clumsiness', score: 2 },
      { value: 'no-balance-issues', label: 'No balance or coordination problems', score: 0 }
    ]
  },
  {
    id: 'neck-pain',
    question: 'Do you experience neck pain or stiffness?',
    description: 'Neck pain is common in Chiari malformation due to anatomical changes',
    options: [
      { value: 'severe-neck-pain', label: 'Yes, severe chronic neck pain', score: 3 },
      { value: 'moderate-neck-pain', label: 'Yes, moderate neck pain or stiffness', score: 2 },
      { value: 'mild-neck-discomfort', label: 'Yes, mild neck discomfort', score: 1 },
      { value: 'no-neck-pain', label: 'No neck pain', score: 0 }
    ]
  },
  {
    id: 'arm-symptoms',
    question: 'Do you have weakness, numbness, or tingling in your arms or hands?',
    description: 'Upper extremity symptoms may indicate associated syringomyelia',
    options: [
      { value: 'severe-arm-symptoms', label: 'Yes, significant weakness and numbness in arms/hands', score: 4 },
      { value: 'moderate-arm-symptoms', label: 'Yes, noticeable weakness or numbness', score: 3 },
      { value: 'mild-tingling', label: 'Yes, mild tingling or occasional numbness', score: 2 },
      { value: 'no-arm-symptoms', label: 'No arm or hand symptoms', score: 0 }
    ]
  },
  {
    id: 'swallowing-voice',
    question: 'Do you have difficulty swallowing or voice changes?',
    description: 'Brainstem compression can affect swallowing and voice function',
    options: [
      { value: 'severe-swallowing', label: 'Yes, significant swallowing difficulties or choking', score: 4 },
      { value: 'voice-changes', label: 'Yes, voice changes or mild swallowing problems', score: 3 },
      { value: 'occasional-difficulty', label: 'Yes, occasional difficulty swallowing', score: 2 },
      { value: 'no-swallowing-issues', label: 'No swallowing or voice problems', score: 0 }
    ]
  },
  {
    id: 'sleep-breathing',
    question: 'Do you have sleep problems or breathing difficulties?',
    description: 'Sleep apnoea and breathing problems can occur with brainstem compression',
    options: [
      { value: 'severe-sleep-apnoea', label: 'Yes, diagnosed sleep apnoea or significant breathing problems', score: 4 },
      { value: 'sleep-disturbances', label: 'Yes, frequent sleep disturbances or snoring', score: 3 },
      { value: 'mild-sleep-issues', label: 'Yes, mild sleep problems', score: 2 },
      { value: 'no-sleep-issues', label: 'No sleep or breathing problems', score: 0 }
    ]
  }
];

export function ChiariSymptomAssessment() {
  const deviceInfo = useDeviceDetection();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);
  const assessmentId = useId();

  const progress = ((currentQuestion + 1) / assessmentQuestions.length) * 100;
  const isLastQuestion = currentQuestion === assessmentQuestions.length - 1;
  const canProceed = answers[assessmentQuestions[currentQuestion]?.id];

  const handleAnswer = (value: string) => {
    setAnswers(prev => ({
      ...prev,
      [assessmentQuestions[currentQuestion].id]: value
    }));
  };

  const handleNext = () => {
    if (isLastQuestion) {
      setShowResults(true);
    } else {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const calculateResults = (): AssessmentResult => {
    const totalScore = Object.entries(answers).reduce((total, [questionId, answer]) => {
      const question = assessmentQuestions.find(q => q.id === questionId);
      const option = question?.options.find(o => o.value === answer);
      return total + (option?.score || 0);
    }, 0);

    let likelihood: 'low' | 'moderate' | 'high';
    let recommendation: string;
    let urgency: 'routine' | 'urgent' | 'prompt';
    let nextSteps: string[];
    const symptoms: string[] = [];

    // Identify specific symptoms
    if (answers['headaches'] && !answers['headaches'].includes('no-headaches')) {
      symptoms.push('Headaches');
    }
    if (answers['balance-coordination'] && !answers['balance-coordination'].includes('no-balance-issues')) {
      symptoms.push('Balance/coordination problems');
    }
    if (answers['neck-pain'] && !answers['neck-pain'].includes('no-neck-pain')) {
      symptoms.push('Neck pain');
    }
    if (answers['arm-symptoms'] && !answers['arm-symptoms'].includes('no-arm-symptoms')) {
      symptoms.push('Arm/hand symptoms');
    }
    if (answers['swallowing-voice'] && !answers['swallowing-voice'].includes('no-swallowing-issues')) {
      symptoms.push('Swallowing/voice issues');
    }
    if (answers['sleep-breathing'] && !answers['sleep-breathing'].includes('no-sleep-issues')) {
      symptoms.push('Sleep/breathing problems');
    }

    if (totalScore >= 15) {
      likelihood = 'high';
      urgency = 'prompt';
      recommendation = 'Your symptoms are highly suggestive of Chiari malformation. The combination and severity of symptoms warrant prompt neurological evaluation and MRI imaging.';
      nextSteps = [
        'Schedule appointment with neurologist within 1-2 weeks',
        'Request MRI brain and cervical spine',
        'Keep detailed symptom diary',
        'Consider neurosurgical consultation if Chiari confirmed'
      ];
    } else if (totalScore >= 8) {
      likelihood = 'moderate';
      urgency = 'prompt';
      recommendation = 'Your symptoms suggest possible Chiari malformation and should be evaluated by a medical professional. Several features are consistent with this condition.';
      nextSteps = [
        'Schedule appointment with GP within 2-3 weeks',
        'Discuss symptoms and request neurologist referral',
        'Monitor symptom progression',
        'Consider MRI if symptoms persist or worsen'
      ];
    } else if (totalScore >= 4) {
      likelihood = 'low';
      urgency = 'routine';
      recommendation = 'You have some symptoms that could be related to various conditions. While Chiari malformation is less likely, medical evaluation may be beneficial if symptoms persist.';
      nextSteps = [
        'Discuss symptoms with your GP at next routine visit',
        'Monitor symptoms for any changes or worsening',
        'Consider lifestyle factors that may contribute to symptoms',
        'Seek medical attention if symptoms significantly worsen'
      ];
    } else {
      likelihood = 'low';
      urgency = 'routine';
      recommendation = 'Your symptoms are minimal and Chiari malformation is unlikely. Continue with routine health maintenance and monitor for any new symptoms.';
      nextSteps = [
        'Continue routine health check-ups',
        'Be aware of Chiari malformation warning signs',
        'Maintain healthy lifestyle and stress management',
        'Seek medical attention if new neurological symptoms develop'
      ];
    }

    return { totalScore, likelihood, recommendation, urgency, nextSteps, symptoms };
  };

  const results = showResults ? calculateResults() : null;

  const getLikelihoodColor = (likelihood: string) => {
    switch (likelihood) {
      case 'high': return 'bg-muted-light text-foreground border border-border';
      case 'moderate': return 'bg-info-light text-info border border-info/50';
      default: return 'bg-success-light text-success border border-success/50';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'prompt': return <AlertTriangle className="h-5 w-5 text-info" />;
      case 'urgent': return <Brain className="h-5 w-5 text-foreground" />;
      default: return <CheckCircle className="h-5 w-5 text-success" />;
    }
  };

  if (showResults && results) {
    return (
      <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
        <div className="container max-w-4xl">
          <Card className={cn("border-2", getLikelihoodColor(results.likelihood))}>
            <CardHeader className="text-center">
              <div className="flex items-center justify-center gap-2 mb-4">
                {getUrgencyIcon(results.urgency)}
                <CardTitle className="text-2xl text-enhanced-heading">Chiari Malformation Assessment Results</CardTitle>
              </div>
              <CardDescription className="text-enhanced-body">
                Based on your responses, here's your personalised assessment
              </CardDescription>
            </CardHeader>
            <CardContent className="content-spacing-md">
              {/* Score and Likelihood */}
              <div className="text-center p-6 bg-background/50 rounded-lg">
                <div className="flex items-center justify-center gap-4 mb-4">
                  <div>
                    <div className="text-enhanced-heading text-3xl font-bold text-primary">{results.totalScore}</div>
                    <div className="text-sm text-muted-foreground">Assessment Score</div>
                  </div>
                  <div>
                    <Badge className={getLikelihoodColor(results.likelihood)}>
                      {results.likelihood.toUpperCase()} likelihood
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Identified Symptoms */}
              {results.symptoms.length > 0 && (
                <div className="bg-background rounded-lg p-6 border">
                  <h3 className="font-semibold mb-3 flex items-center gap-2">
                    <Zap className="h-5 w-5 text-info" />
                    Identified Symptoms
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {results.symptoms.map((symptom, index) => (
                      <Badge key={index} variant="secondary" className="bg-info-light text-foreground">
                        {symptom}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendation */}
              <div className="bg-background rounded-lg p-6 border">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Activity className="h-5 w-5 text-primary" />
                  Recommendation
                </h3>
                <p className="text-sm">{results.recommendation}</p>
              </div>

              {/* Next Steps */}
              <div className="bg-background rounded-lg p-6 border">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <ArrowRight className="h-5 w-5 text-primary" />
                  Recommended Next Steps
                </h3>
                <ul className="space-y-2">
                  {results.nextSteps.map((step, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold mt-0.5 flex-shrink-0">
                        {index + 1}
                      </div>
                      <span className="text-sm">{step}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Important Disclaimer */}
              <div className="bg-info border border-info rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-info mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-info mb-1">Important Disclaimer</h4>
                    <p className="text-sm text-info">
                      This assessment tool is for educational purposes only and does not replace professional medical diagnosis. 
                      Many symptoms can have multiple causes. Always consult with qualified healthcare professionals for proper evaluation.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={cn("flex gap-3", deviceInfo.isMobile ? "flex-col" : "flex-row justify-center")}>
                <Button size="lg">
                  <Brain className="mr-2 h-4 w-4" />
                  Find a Specialist
                </Button>
                <Button variant="outline" size="lg" onClick={() => {
                  setShowResults(false);
                  setCurrentQuestion(0);
                  setAnswers({});
                }}>
                  Retake Assessment
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    );
  }

  return (
    <section 
      className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}
      aria-labelledby={`${assessmentId}-title`}
    >
      <div className="container max-w-3xl">
        <div className="text-center mb-8">
          <h2 
            id={`${assessmentId}-title`}
            className={cn("font-bold mb-4", deviceInfo.isMobile ? "text-2xl" : "text-3xl")}
          >
            Chiari Malformation Symptom Assessment
          </h2>
          <p className={cn("text-muted-foreground", deviceInfo.isMobile ? "text-sm" : "text-lg")}>
            Answer these questions to assess your symptoms and receive personalised guidance about when to seek medical attention
          </p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Progress</span>
            <span className="text-sm text-muted-foreground">
              {currentQuestion + 1} of {assessmentQuestions.length}
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Question Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              Question {currentQuestion + 1}
            </CardTitle>
            <CardDescription className="text-lg font-medium">
              {assessmentQuestions[currentQuestion]?.question}
            </CardDescription>
            {assessmentQuestions[currentQuestion]?.description && (
              <p className="text-sm text-muted-foreground">
                {assessmentQuestions[currentQuestion].description}
              </p>
            )}
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={answers[assessmentQuestions[currentQuestion]?.id] || ''}
              onValueChange={handleAnswer}
              className="space-y-3"
            >
              {assessmentQuestions[currentQuestion]?.options.map((option) => (
                <div key={option.value} className="flex items-start space-x-3 p-3 rounded-lg border hover:bg-muted/50">
                  <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                  <Label htmlFor={option.value} className="font-medium cursor-pointer flex-1">
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className={cn("flex justify-between mt-8", deviceInfo.isMobile ? "flex-col gap-3" : "")}>
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestion === 0}
            className={deviceInfo.isMobile ? "order-2" : ""}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button
            onClick={handleNext}
            disabled={!canProceed}
            className={deviceInfo.isMobile ? "order-1" : ""}
          >
            {isLastQuestion ? 'Get Results' : 'Next'}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  );
}

export default ChiariSymptomAssessment;
