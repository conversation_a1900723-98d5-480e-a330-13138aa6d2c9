export interface ConsultationFee {
  id: string;
  title: string;
  description: string;
  fee: string;
  rebate: string;
  outOfPocket: string;
}

export interface InsuranceOption {
  id: string;
  title: string;
  description: string;
}

export const consultationFees: ConsultationFee[] = [
  {
    id: 'initial-consultation',
    title: 'Initial Consultation',
    description: 'Comprehensive assessment and treatment planning.',
    fee: '$450',
    rebate: '$150',
    outOfPocket: '$300'
  },
  {
    id: 'review-consultation',
    title: 'Review Consultation',
    description: 'Follow-up consultation for ongoing care.',
    fee: '$250',
    rebate: '$75',
    outOfPocket: '$175'
  }
];

export const insuranceOptions: InsuranceOption[] = [
  {
    id: 'private-health',
    title: 'Private Health Insurance',
    description: 'Most private health insurers provide coverage for neurosurgical consultations.'
  },
  {
    id: 'tac',
    title: 'TAC',
    description: 'Transport Accident Commission claims are accepted.'
  },
  {
    id: 'veteran-affairs',
    title: 'Veteran Affairs',
    description: 'DVA gold and white card holders welcome.'
  },
  {
    id: 'workcover',
    title: 'WorkCover',
    description: 'WorkCover claims are processed efficiently.'
  }
];

export const getConsultationFeeById = (id: string): ConsultationFee | undefined => {
  return consultationFees.find(fee => fee.id === id);
};

export const getInsuranceOptionById = (id: string): InsuranceOption | undefined => {
  return insuranceOptions.find(option => option.id === id);
};
