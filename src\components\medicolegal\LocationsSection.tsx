import React from 'react';

import SafeImage from '@/components/SafeImage';

interface LocationsSectionProps {
  translations: {
    locations?: {
      title?: string;
      description?: string;
    };
  };
}

const LocationsSection: React.FC<LocationsSectionProps> = ({ translations }) => {

  return (
    <section className="py-16">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{translations.locations?.title || 'Our Locations'}</h2>
          <p className="text-enhanced-body max-w-3xl mx-auto">
            {translations.locations?.description || 'Convenient locations for medicolegal assessments across Melbourne.'}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
          <div>
            <h3 className="text-enhanced-heading text-2xl font-bold mb-4 text-primary">miNEURO Consulting Suites</h3>
            <p className="text-muted-foreground mb-4">
              Our primary location offers state-of-the-art facilities for comprehensive medicolegal assessments in a professional medical environment.
            </p>
            <div className="medical-card p-4 rounded-lg mb-6">
              <p className="font-medium text-primary mb-2">Address:</p>
              <p className="text-muted-foreground">Suite 4, Ground Floor, 619 Canterbury Road, Surrey Hills VIC 3127</p>
            </div>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Fully equipped examination rooms</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Accessible facilities</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Professional environment</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Convenient parking</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Public transport access</span>
              </li>
            </ul>
          </div>
          <div className="relative rounded-xl overflow-hidden shadow-lg">
            <SafeImage
              src="/images/neurosurgical-consultation-suite-Surrey-Hills-miNEURO-entrance.jpg"
              alt="miNEURO Consulting Suites"
              className="w-full h-auto"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="order-2 lg:order-1 relative rounded-xl overflow-hidden shadow-lg">
            <SafeImage
              src="/images/neurosurgical-consultation-suite-Surrey-Hills-miNEURO-waiting-area.jpg"
              alt="Professional consulting rooms"
              className="w-full h-auto"
            />
          </div>
          <div className="order-1 lg:order-2">
            <h3 className="text-enhanced-heading text-2xl font-bold mb-4 text-primary">Additional Consulting Facilities</h3>
            <p className="text-muted-foreground mb-6">
              We also provide assessments at other professional medical facilities to accommodate specific requirements and ensure convenient access for all parties.
            </p>
            <ul className="space-y-2">
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Private consultation rooms</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Modern medical equipment</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Comfortable waiting areas</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Professional staff support</span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2">•</span>
                <span className="text-muted-foreground">Confidential environment</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

LocationsSection.displayName = 'LocationsSection';

export default LocationsSection;
