import React from 'react';

interface SpineRegionHeaderProps {
  title: string;
  description: string;
  anatomyImage: {
    src: string;
    alt: string;
  };
}

/**
 * SpineRegionHeader Component
 * Header section for each spine region tab
 * Preserves exact styling and layout from original implementation
 */
const SpineRegionHeader: React.FC<SpineRegionHeaderProps> = ({
  title,
  description,
  anatomyImage
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-10 mb-10">
      <div className="md:col-span-2">
        <h3 className="text-enhanced-heading text-2xl lg:text-enhanced-heading text-3xl font-bold mb-6 text-foreground">{title}</h3>
        <p className="text-foreground/80 mb-8 text-lg leading-relaxed font-medium">
          {description}
        </p>
      </div>
      <div className="relative rounded-xl overflow-hidden shadow-xl border border-border/30">
        <img
          src={anatomyImage.src}
          alt={anatomyImage.alt}
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  );
};

SpineRegionHeader.displayName = 'SpineRegionHeader';

export default SpineRegionHeader;
