import React from 'react';

import { cn } from '@/lib/utils';

interface IconContainerProps {
  icon: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'primary' | 'secondary' | 'muted' | 'accent';
  shape?: 'circle' | 'rounded' | 'square';
  className?: string;
}

const IconContainer: React.FC<IconContainerProps> = ({
  icon,
  size = 'md',
  variant = 'primary',
  shape = 'circle',
  className = ''
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'h-8 w-8';
      case 'lg':
        return 'h-16 w-16';
      case 'xl':
        return 'h-20 w-20';
      default:
        return 'h-12 w-12';
    }
  };

  const getVariantClass = () => {
    switch (variant) {
      case 'secondary':
        return 'bg-secondary/10 text-secondary';
      case 'muted':
        return 'bg-muted text-muted-foreground';
      case 'accent':
        return 'bg-accent/10 text-accent-foreground';
      default:
        return 'bg-primary/10 text-primary';
    }
  };

  const getShapeClass = () => {
    switch (shape) {
      case 'rounded':
        return 'rounded-lg';
      case 'square':
        return 'rounded-none';
      default:
        return 'rounded-full';
    }
  };

  return (
    <div className={cn(
      getSizeClass(),
      getVariantClass(),
      getShapeClass(),
      'flex items-center justify-center flex-shrink-0',
      className
    )}>
      {icon}
    </div>
  );
};

export default IconContainer;
