import { Zap, Shield, AlertTriangle, CheckCircle, Target, Lightbulb } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface TriggerCategory {
  category: string;
  triggers: string[];
  mechanism: string;
}

interface PainTriggerManagementProps {
  title: string;
  description: string;
  commonTriggers: TriggerCategory[];
}

interface ManagementStrategy {
  trigger: string;
  strategies: string[];
  difficulty: 'easy' | 'moderate' | 'challenging';
}

const managementStrategies: ManagementStrategy[] = [
  {
    trigger: "Light touch to face",
    strategies: [
      "Use soft fabrics for pillowcases and clothing",
      "Avoid tight-fitting hats or glasses",
      "Be gentle when washing face",
      "Use electric razor instead of manual"
    ],
    difficulty: "moderate"
  },
  {
    trigger: "Eating or chewing",
    strategies: [
      "Eat soft foods during flare-ups",
      "Chew on the unaffected side",
      "Cut food into small pieces",
      "Avoid very hot or cold foods",
      "Use a straw for liquids"
    ],
    difficulty: "easy"
  },
  {
    trigger: "Brushing teeth",
    strategies: [
      "Use a soft-bristled toothbrush",
      "Try an electric toothbrush on gentle setting",
      "Use lukewarm water",
      "Consider prescription toothpaste",
      "Brush gently and slowly"
    ],
    difficulty: "easy"
  },
  {
    trigger: "Cold weather exposure",
    strategies: [
      "Wear a scarf or face mask outdoors",
      "Avoid direct air conditioning",
      "Use a humidifier in dry conditions",
      "Gradually adjust to temperature changes"
    ],
    difficulty: "easy"
  },
  {
    trigger: "Speaking or smiling",
    strategies: [
      "Practice gentle facial movements",
      "Speak slowly and deliberately",
      "Take breaks during long conversations",
      "Consider communication aids during severe episodes"
    ],
    difficulty: "challenging"
  },
  {
    trigger: "Stress and fatigue",
    strategies: [
      "Practice stress reduction techniques",
      "Maintain regular sleep schedule",
      "Consider meditation or mindfulness",
      "Engage in gentle exercise",
      "Seek counselling support"
    ],
    difficulty: "moderate"
  }
];

export function PainTriggerManagement({ 
  title, 
  description, 
  commonTriggers 
}: PainTriggerManagementProps) {
  const deviceInfo = useDeviceDetection();
  const [selectedTriggers, setSelectedTriggers] = useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = useState('triggers');

  const handleTriggerToggle = (trigger: string) => {
    const newSelected = new Set(selectedTriggers);
    if (newSelected.has(trigger)) {
      newSelected.delete(trigger);
    } else {
      newSelected.add(trigger);
    }
    setSelectedTriggers(newSelected);
  };

  const getRelevantStrategies = () => {
    return managementStrategies.filter(strategy => 
      Array.from(selectedTriggers).some(trigger => 
        strategy.trigger.toLowerCase().includes(trigger.toLowerCase()) ||
        trigger.toLowerCase().includes(strategy.trigger.toLowerCase())
      )
    );
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'badge-routine';
      case 'moderate': return 'badge-info';
      case 'challenging': return 'badge-emergency';
      default: return 'badge-info';
    }
  };

  const getCategoryIcon = (category: string) => {
    if (category.toLowerCase().includes('facial')) return Target;
    if (category.toLowerCase().includes('oral')) return Zap;
    if (category.toLowerCase().includes('environmental')) return Shield;
    return AlertTriangle;
  };

  return (
    <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-8",
            deviceInfo.isMobile ? "grid-cols-1 h-auto" : "grid-cols-3"
          )}>
            <TabsTrigger value="triggers" className={deviceInfo.isMobile ? "py-3" : ""}>
              Identify Triggers
            </TabsTrigger>
            <TabsTrigger value="strategies" className={deviceInfo.isMobile ? "py-3" : ""}>
              Management Strategies
            </TabsTrigger>
            <TabsTrigger value="personalised" className={deviceInfo.isMobile ? "py-3" : ""}>
              Personalised Plan
            </TabsTrigger>
          </TabsList>

          {/* Trigger Identification */}
          <TabsContent value="triggers" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-info" />
                  Common Pain Triggers
                </CardTitle>
                <CardDescription>
                  Select the triggers that commonly cause your trigeminal neuralgia pain
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {commonTriggers.map((category, categoryIndex) => {
                    const Icon = getCategoryIcon(category.category);
                    
                    return (
                      <div key={categoryIndex}>
                        <h4 className="font-semibold mb-3 flex items-center gap-2">
                          <Icon className="h-4 w-4 text-primary" />
                          {category.category}
                        </h4>
                        <p className="text-sm text-muted-foreground mb-4">{category.mechanism}</p>
                        <div className={cn(
                          "grid gap-3",
                          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2 md:grid-cols-3"
                        )}>
                          {category.triggers.map((trigger, triggerIndex) => (
                            <div
                              key={triggerIndex}
                              className={cn(
                                "flex items-center space-x-3 p-3 rounded-lg border transition-colors cursor-pointer",
                                selectedTriggers.has(trigger) 
                                  ? "bg-info border-info" 
                                  : "hover:bg-muted"
                              )}
                              onClick={() => handleTriggerToggle(trigger)}
                            >
                              <Checkbox
                                checked={selectedTriggers.has(trigger)}
                                onChange={() => handleTriggerToggle(trigger)}
                              />
                              <span className="text-sm">{trigger}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Management Strategies */}
          <TabsContent value="strategies" className="space-y-6">
            <div className="grid gap-6">
              {managementStrategies.map((strategy, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{strategy.trigger}</CardTitle>
                      <Badge className={getDifficultyColor(strategy.difficulty)}>
                        {strategy.difficulty}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {strategy.strategies.map((strategyItem, strategyIndex) => (
                        <li key={strategyIndex} className="flex items-start gap-2">
                          <CheckCircle className="h-4 w-4 text-success mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{strategyItem}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Personalised Plan */}
          <TabsContent value="personalised" className="space-y-6">
            {selectedTriggers.size > 0 ? (
              <>
                <Card className="bg-info-light/30 border-info/50">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-info">
                      <Lightbulb className="h-5 w-5" />
                      Your Personalised Management Plan
                    </CardTitle>
                    <CardDescription className="text-info">
                      Based on your selected triggers, here are targeted strategies
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-2">Your Selected Triggers:</h4>
                        <div className="flex flex-wrap gap-2">
                          {Array.from(selectedTriggers).map((trigger, index) => (
                            <Badge key={index} variant="secondary">
                              {trigger}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <div className="grid gap-6">
                  {getRelevantStrategies().map((strategy, index) => (
                    <Card key={index} className="border-success">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg flex items-center gap-2">
                            <Shield className="h-5 w-5 text-success" />
                            Managing: {strategy.trigger}
                          </CardTitle>
                          <Badge className={getDifficultyColor(strategy.difficulty)}>
                            {strategy.difficulty}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {strategy.strategies.map((strategyItem, strategyIndex) => (
                            <li key={strategyIndex} className="flex items-start gap-2">
                              <CheckCircle className="h-4 w-4 text-success mt-0.5 flex-shrink-0" />
                              <span className="text-sm">{strategyItem}</span>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </>
            ) : (
              <Card>
                <CardContent className="pt-6 text-center">
                  <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="font-semibold mb-2">No Triggers Selected</h3>
                  <p className="text-muted-foreground mb-4">
                    Please go to the "Identify Triggers" tab to select your pain triggers 
                    and receive personalised management strategies.
                  </p>
                  <Button onClick={() => setActiveTab('triggers')}>
                    Identify Your Triggers
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        {/* Emergency Information */}
        <div className="mt-12">
          <Card className="bg-muted/50 border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-foreground">
                <AlertTriangle className="h-5 w-5" />
                Important Safety Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground text-sm">
                While trigger avoidance is important, complete avoidance of all triggers may not be 
                possible or healthy. Work with your healthcare team to develop a balanced approach 
                that maintains quality of life while minimising pain episodes. If you experience 
                sudden changes in pain patterns or new neurological symptoms, seek immediate medical attention.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default PainTriggerManagement;
