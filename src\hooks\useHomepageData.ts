import { useMemo } from 'react';

import {
  servicesData,
  expertiseData,
  misPhilosophyPrinciples,
  whenLessIsMoreBenefits,
  imageGuidedBenefits,
  patientOutcomes,
  treatmentDecisionsContent,
  consultationSteps,
  consultationPreparation,
  feedbackSections,
  sectionHeaders
} from '@/data/homepage';
import type { HomepageData } from '@/types/homepage';

/**
 * Custom hook for managing homepage data
 * Provides centralised access to all homepage content and data structures
 * 
 * @returns {HomepageData} Complete homepage data object
 */
export const useHomepageData = (): HomepageData => {
  const homepageData = useMemo<HomepageData>(() => ({
    services: servicesData,
    expertise: expertiseData,
    misPhilosophyPrinciples,
    whenLessIsMoreBenefits,
    imageGuidedBenefits,
    patientOutcomes,
    treatmentDecisionsContent,
    consultationSteps,
    consultationPreparation,
    feedbackSections,
    sectionHeaders
  }), []);

  return homepageData;
};

/**
 * Hook for getting specific section data
 * Useful when components only need specific sections
 * 
 * @param sectionName - Name of the section to retrieve
 * @returns Specific section data
 */
export const useHomepageSection = <T extends keyof HomepageData>(
  sectionName: T
): HomepageData[T] => {
  const data = useHomepageData();
  return data[sectionName];
};

/**
 * Hook for getting section headers
 * Provides easy access to section titles and subtitles
 * 
 * @param sectionKey - Key for the specific section header
 * @returns Section header object with title and subtitle
 */
export const useSectionHeader = (sectionKey: string) => {
  const { sectionHeaders } = useHomepageData();
  return sectionHeaders[sectionKey] || { title: '', subtitle: '' };
};

/**
 * Hook for getting services data with optional filtering
 * 
 * @param filter - Optional filter function
 * @returns Filtered services array
 */
export const useServicesData = (filter?: (service: unknown) => boolean) => {
  const { services } = useHomepageData();
  
  return useMemo(() => {
    if (filter) {
      return services.filter(filter);
    }
    return services;
  }, [services, filter]);
};

/**
 * Hook for getting expertise data with optional filtering
 * 
 * @param filter - Optional filter function
 * @returns Filtered expertise array
 */
export const useExpertiseData = (filter?: (expertise: unknown) => boolean) => {
  const { expertise } = useHomepageData();
  
  return useMemo(() => {
    if (filter) {
      return expertise.filter(filter);
    }
    return expertise;
  }, [expertise, filter]);
};

/**
 * Hook for getting consultation data
 * Combines consultation steps and preparation information
 * 
 * @returns Object with consultation steps and preparation data
 */
export const useConsultationData = () => {
  const { consultationSteps, consultationPreparation } = useHomepageData();
  
  return useMemo(() => ({
    steps: consultationSteps,
    preparation: consultationPreparation
  }), [consultationSteps, consultationPreparation]);
};

/**
 * Hook for getting treatment decisions content
 * Provides structured access to treatment philosophy content
 * 
 * @returns Treatment decisions content object
 */
export const useTreatmentDecisions = () => {
  const { treatmentDecisionsContent } = useHomepageData();
  return treatmentDecisionsContent;
};

/**
 * Hook for getting feedback sections data
 * Provides access to feedback and complaints section content
 * 
 * @returns Array of feedback sections
 */
export const useFeedbackData = () => {
  const { feedbackSections } = useHomepageData();
  return feedbackSections;
};

/**
 * Hook for getting minimally invasive surgery content
 * Combines philosophy principles and benefits
 * 
 * @returns Object with MIS principles and benefits
 */
export const useMinimallyInvasiveData = () => {
  const { misPhilosophyPrinciples, whenLessIsMoreBenefits } = useHomepageData();
  
  return useMemo(() => ({
    principles: misPhilosophyPrinciples,
    benefits: whenLessIsMoreBenefits
  }), [misPhilosophyPrinciples, whenLessIsMoreBenefits]);
};

/**
 * Hook for getting image-guided surgery content
 * Combines key benefits and patient outcomes
 * 
 * @returns Object with benefits and outcomes
 */
export const useImageGuidedData = () => {
  const { imageGuidedBenefits, patientOutcomes } = useHomepageData();
  
  return useMemo(() => ({
    keyBenefits: imageGuidedBenefits,
    patientOutcomes
  }), [imageGuidedBenefits, patientOutcomes]);
};

export default useHomepageData;
