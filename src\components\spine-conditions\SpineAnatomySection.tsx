import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';

interface SpineAnatomySectionProps {
  title: string;
  subtitle: string;
  paragraphs: string[];
  anatomyList: {
    label: string;
    description: string;
  }[];
  image: {
    src: string;
    alt: string;
  };
  ctaButton: {
    text: string;
    link: string;
  };
}

/**
 * SpineAnatomySection Component
 * Educational section about spine anatomy
 * Preserves exact styling and content from original implementation
 */
const SpineAnatomySection: React.FC<SpineAnatomySectionProps> = ({
  title,
  subtitle,
  paragraphs,
  anatomyList,
  image,
  ctaButton
}) => {
  return (
    <section className="py-20 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50">
      <div className="container">
        <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-16 text-center text-foreground">{title}</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-16 max-w-6xl mx-auto">
          <div className="relative rounded-xl overflow-hidden shadow-xl border border-border/30">
            <img
              src={image.src}
              alt={image.alt}
              className="w-full h-auto"
            />
          </div>
          <div>
            <h3 className="text-enhanced-heading text-2xl lg:text-enhanced-heading text-3xl font-bold mb-8 text-foreground">{subtitle}</h3>
            {paragraphs.map((paragraph, index) => (
              <p key={index} className="text-foreground/80 mb-6 text-lg leading-relaxed font-medium">
                {paragraph}
              </p>
            ))}
            <div className="space-y-4 mb-10">
              {anatomyList.map((item, index) => (
                <div key={index} className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-5 border border-border/30 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                    <div>
                      <span className="font-bold text-foreground text-base">{item.label}:</span>
                      <span className="text-foreground/80 text-base ml-2">{item.description}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <Button asChild className="font-bold px-8 py-4 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105 border-2">
              <Link to={ctaButton.link}>{ctaButton.text}</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

SpineAnatomySection.displayName = 'SpineAnatomySection';

export default SpineAnatomySection;
