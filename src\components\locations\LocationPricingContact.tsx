import { MapPin, Phone, Mail } from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';

interface ContactInfo {
  address: {
    line1: string;
    line2: string;
  };
  email: string;
  phone: string;
}

interface LocationPricingContactProps {
  title: string;
  description: string;
  contactInfo: ContactInfo;
  contactDescription: string;
  schedulingTitle: string;
  schedulingDescription: string;
  schedulingButtonText: string;
  schedulingButtonLink: string;
}

/**
 * Location Pricing and Contact Component
 * Displays pricing information and contact details for facility rental
 * Preserves all original pricing and contact content from Surrey Hills location page
 */
const LocationPricingContact: React.FC<LocationPricingContactProps> = ({
  title,
  description,
  contactInfo,
  contactDescription,
  schedulingTitle,
  schedulingDescription,
  schedulingButtonText,
  schedulingButtonLink
}) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="mb-12 text-center">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            {description}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">Contact Us</h3>
            <p className="text-muted-foreground mb-6">
              {contactDescription}
            </p>

            <div className="space-y-4">
              <div className="flex items-start">
                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                  <MapPin className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-semibold mb-1">Address</h4>
                  <p className="text-muted-foreground">
                    {contactInfo.address.line1}<br />
                    {contactInfo.address.line2}
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                  <Mail className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-semibold mb-1">Email</h4>
                  <p className="text-muted-foreground">
                    {contactInfo.email}
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                  <Phone className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-semibold mb-1">Phone</h4>
                  <p className="text-muted-foreground">
                    {contactInfo.phone}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card p-6 rounded-lg shadow-md medical-card">
            <h3 className="text-xl font-semibold mb-3 text-primary">{schedulingTitle}</h3>
            <p className="text-muted-foreground mb-4">
              {schedulingDescription}
            </p>

            <div className="mt-4">
              <Button asChild size="lg" className="w-full">
                <Link to={schedulingButtonLink}>{schedulingButtonText}</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

LocationPricingContact.displayName = 'LocationPricingContact';

export default LocationPricingContact;
