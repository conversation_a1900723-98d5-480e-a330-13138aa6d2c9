import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import type { MinimallyInvasiveSectionProps } from '@/types/homepage';

/**
 * Why Minimally-Invasive Surgery Section Component
 * Displays Dr<PERSON>'s surgical philosophy and approach
 * Preserves all original content and styling from Index.tsx lines 654-699
 */
const MinimallyInvasiveSection: React.FC<MinimallyInvasiveSectionProps> = ({ principles }) => {
  return (
    <section className="section-spacing section-background-primary">
      <div className="section-container">
        <div className="section-header-spacing text-center">
          <span className="text-primary font-semibold uppercase tracking-wider text-sm border-b-2 border-primary/30 pb-2 inline-block mb-6">
            NEUROSURGEON WHO BELIEVES THAT LESS IS MORE
          </span>
          <h2 className="text-headline">
            Why Minimally-Invasive Surgery?
          </h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-6">
            <p className="lead">
              The ultimate goal of any neurosurgeon and spine surgeon is to improve the patient's quality and even length of life by providing good recommendations regarding available treatment options.
            </p>

            <p className="text-foreground/80 leading-relaxed">
              The evolution of surgery and anaesthesiology allowed for achieving this goal and successfully managing previously untreatable conditions. However, there is still a lot of space for improvement, in particular in the field of brain and spine surgery.
            </p>

            <p className="text-foreground/80 leading-relaxed">
              Minimally invasive procedures represent a logical evolution of surgical philosophy focusing on patient safety and satisfaction. The surgical philosophy of <a href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/" className="text-primary font-semibold hover:text-primary/80 transition-colors duration-200" target="_blank" rel="noopener noreferrer">Dr Aliashkevich</a> is based on three main principles:
            </p>

            <div className="bg-card/50 rounded-lg p-6 border border-border/30">
              <ul className="space-y-3">
                {principles.map((principle, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-primary font-bold mr-3">•</span>
                    <span className="text-foreground/90 leading-relaxed">{principle}</span>
                  </li>
                ))}
              </ul>
            </div>

            <p className="text-foreground/80 leading-relaxed">
              Dr Aliashkevich believes that the crucial goal of a neurosurgeon and spine surgeon is to avoid complex surgery whenever possible and to consider the least aggressive treatment options as the primary choice.
            </p>

            <div className="pt-4">
              <Button asChild className="btn-primary">
                <Link to="/expertise">
                  Learn More About Our Approach
                </Link>
              </Button>
            </div>
          </div>

          <div className="relative">
            <div className="medical-card-elevated rounded-xl overflow-hidden shadow-2xl">
              <img
                src="/images/brain-tumour-image-guided-surgery-minimally-invasive-advanced-neurosurgery-aliashkevich-mineuro.jpg"
                alt="Dr. Aliashkevich performing minimally-invasive neurosurgery with advanced image-guided technology"
                className="w-full h-96 object-cover transition-transform duration-500 hover:scale-105"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

MinimallyInvasiveSection.displayName = 'MinimallyInvasiveSection';

export default MinimallyInvasiveSection;
