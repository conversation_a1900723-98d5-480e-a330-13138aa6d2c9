import React from 'react';
import { Link } from 'react-router-dom';

import { But<PERSON> } from '@/components/ui/button';
import type { MinimallyInvasiveSectionProps } from '@/types/homepage';

/**
 * Why Minimally-Invasive Surgery Section Component
 * Displays Dr<PERSON>'s surgical philosophy and approach
 * Preserves all original content and styling from Index.tsx lines 654-699
 */
const MinimallyInvasiveSection: React.FC<MinimallyInvasiveSectionProps> = ({ principles }) => {
  return (
    <section className="section medical-card">
      <div className="container">
        <div className="text-center mb-12">
          <span className="text-primary font-medium uppercase tracking-wider text-sm">
            NEUROSURGEON WHO BELIEVES THAT LESS IS MORE
          </span>
          <h2 className="text-enhanced-heading text-3xl md:text-enhanced-heading text-4xl font-bold mt-2 mb-6">Why Minimally-Invasive Surgery?</h2>
        </div>
        <div className="flex flex-col md:flex-row items-center gap-8">
          <div className="md:w-1/2">
            <p className="text-muted-foreground mb-4">
              The ultimate goal of any neurosurgeon and spine surgeon is to improve the patient's quality and even length of life by providing good recommendations regarding available treatment options.
            </p>
            <p className="text-muted-foreground mb-4">
              The evolution of surgery and anaesthesiology allowed for achieving this goal and successfully managing previously untreatable conditions. However, there is still a lot of space for improvement, in particular in the field of brain and spine surgery.
            </p>
            <p className="text-muted-foreground mb-4">
              Minimally invasive procedures represent a logical evolution of surgical philosophy focusing on patient safety and satisfaction. The surgical philosophy of <a href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">Dr Aliashkevich</a> is based on three main principles:
            </p>
            <ul className="text-muted-foreground mb-6 list-none space-y-2">
              {principles.map((principle, index) => (
                <li key={index}>– {principle}</li>
              ))}
            </ul>
            <p className="text-muted-foreground mb-6">
              Dr Aliashkevich believes that the crucial goal of a neurosurgeon and spine surgeon is to avoid complex surgery whenever possible and to consider the least aggressive treatment options as the primary choice.
            </p>
            <Button asChild>
              <Link to="/expertise">
                Learn More About Our Approach
              </Link>
            </Button>
          </div>
          <div className="md:w-1/2">
            <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl">
              <img
                src="/images/brain-tumour-image-guided-surgery-minimally-invasive-advanced-neurosurgery-aliashkevich-mineuro.jpg"
                alt="Dr. Aliashkevich performing minimally-invasive neurosurgery with advanced image-guided technology"
                className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

MinimallyInvasiveSection.displayName = 'MinimallyInvasiveSection';

export default MinimallyInvasiveSection;
