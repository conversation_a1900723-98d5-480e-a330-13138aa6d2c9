import { useEffect } from 'react';

import { useLanguage } from '@/contexts/LanguageContext';
import en from '@/locales/en';

interface PatientResourceData {
  title: string;
  subtitle: string;
  introduction: string;
  sections: Array<{
    id: string;
    title: string;
    content: Record<string, unknown>;
  }>;
}

export const usePatientResourceData = (resourceKey: string): PatientResourceData => {
  const { t } = useLanguage();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Safe fallback for translations
  const safeT = t || en;
  const resourceData = safeT?.[resourceKey as keyof typeof safeT] as Record<string, unknown> | undefined;

  // Default fallback data
  const defaultData: PatientResourceData = {
    title: 'Patient Resource',
    subtitle: 'Comprehensive healthcare information',
    introduction: 'Expert guidance for your health and wellbeing.',
    sections: []
  };

  // Extract and structure the data
  return {
    title: resourceData?.title || defaultData.title,
    subtitle: resourceData?.subtitle || defaultData.subtitle,
    introduction: resourceData?.introduction || defaultData.introduction,
    sections: resourceData?.sections || defaultData.sections
  };
};

export default usePatientResourceData;
