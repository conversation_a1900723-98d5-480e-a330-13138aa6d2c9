import React from 'react';

import SafeImage from '@/components/SafeImage';
import { ExpertiseSection as ExpertiseSectionType } from '@/data/expertise/cervicalDiscReplacementData';

interface ExpertiseSectionProps {
  section: ExpertiseSectionType;
}

const ExpertiseSection: React.FC<ExpertiseSectionProps> = ({ section }) => {
  return (
    <div className="mb-8">
      <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{section.title}</h2>
      
      {section.content.map((paragraph, index) => (
        <p key={index} className="text-muted-foreground mb-6">
          {paragraph}
        </p>
      ))}

      {section.imageSrc && (
        <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
          <SafeImage
            src={section.imageSrc}
            alt={section.imageAlt || section.title}
            className="w-full h-full object-cover"
            fallbackSrc="/images/medical-consulting.jpg"
          />
        </div>
      )}
    </div>
  );
};

ExpertiseSection.displayName = 'ExpertiseSection';

export default ExpertiseSection;
