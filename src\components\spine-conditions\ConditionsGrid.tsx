import React from 'react';

import ConditionCard from './ConditionCard';

import type { ConditionData } from '@/data/spine-conditions/spineConditionsData';

interface ConditionsGridProps {
  conditions: ConditionData[];
}

/**
 * ConditionsGrid Component
 * Grid layout component for displaying condition cards
 * Preserves exact grid styling from original implementation
 */
const ConditionsGrid: React.FC<ConditionsGridProps> = ({ conditions }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
      {conditions.map((condition) => (
        <ConditionCard key={condition.id} condition={condition} />
      ))}
    </div>
  );
};

ConditionsGrid.displayName = 'ConditionsGrid';

export default ConditionsGrid;
