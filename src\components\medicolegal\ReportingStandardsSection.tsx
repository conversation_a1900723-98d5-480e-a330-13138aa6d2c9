import React from 'react';

interface ReportingStandardsSectionProps {
  reportingStandards: {
    title: string;
    description: string;
    standards: {
      title: string;
      description: string;
      requirements: string[];
    }[];
  };
}

const ReportingStandardsSection: React.FC<ReportingStandardsSectionProps> = ({ reportingStandards }) => {
  return (
    <section className="py-16">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{reportingStandards.title}</h2>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            {reportingStandards.description}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {reportingStandards.standards.map((standard, index) => (
            <div key={index} className="card p-6 rounded-lg shadow-md medical-card">
              <h3 className="text-xl font-semibold mb-3 text-primary">
                {standard.title}
              </h3>
              <p className="text-muted-foreground mb-4">
                {standard.description}
              </p>
              <ul className="space-y-2">
                {standard.requirements.map((requirement, reqIndex) => (
                  <li key={reqIndex} className="text-muted-foreground text-sm">
                    • {requirement}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

ReportingStandardsSection.displayName = 'ReportingStandardsSection';

export default ReportingStandardsSection;
