import React from 'react';

// Chart item type for payload
interface ChartItem {
  name?: string;
  value?: string | number;
  colour?: string;
}

// Simple chart configuration type
export type ChartConfig = {
  [k in string]: {
    label?: React.ReactNode;
    icon?: React.ComponentType;
    colour?: string;
  };
};

// Chart context props
interface ChartContextProps {
  config: ChartConfig
}

const ChartContext = React.createContext<ChartContextProps | null>(null)

/**
 * Hook to access chart configuration
 * Separated from component file to support React Fast Refresh
 */
export function useChart() {
  const context = React.useContext(ChartContext)
  if (!context) {
    throw new Error("useChart must be used within a <ChartContainer />")
  }
  return context
}

export { ChartContext };
export type { ChartContextProps, ChartItem };
