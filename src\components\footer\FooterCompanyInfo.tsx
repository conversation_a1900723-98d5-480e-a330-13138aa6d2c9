import { Facebook, Instagram, Twitter } from 'lucide-react';
import React from 'react';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { SocialLink } from '@/data/footer/footerData';
import { cn } from '@/lib/utils';

interface FooterCompanyInfoProps {
  companyName: string;
  description: string;
  socialLinks: SocialLink[];
}

const iconMap = {
  Facebook,
  Instagram,
  Twitter
};

const FooterCompanyInfo: React.FC<FooterCompanyInfoProps> = ({
  companyName,
  description,
  socialLinks
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <div className={cn(
      deviceInfo.isMobile ? "mobile-fade-in" : "animate-fade-in [animation-delay:100ms]"
    )}>
      <h3 className={cn(
        "font-bold mb-mobile-md",
        deviceInfo.isMobile ? "mobile-subheading" : "text-xl mb-4"
      )}>
        {companyName}
      </h3>
      <p className={cn(
        "text-muted-foreground mb-mobile-md",
        deviceInfo.isMobile ? "mobile-text" : "mb-4"
      )}>
        {description}
      </p>
      <nav aria-label="Social media links" className={cn(
        "flex",
        deviceInfo.isMobile ? "space-x-mobile-md" : "space-x-4"
      )}>
        {socialLinks.map((social) => {
          const IconComponent = iconMap[social.icon as keyof typeof iconMap];
          
          return (
            <a
              key={social.id}
              href={social.url}
              aria-label={social.ariaLabel}
              className={cn(
                "text-muted-foreground transition-colors touch-feedback",
                deviceInfo.isMobile
                  ? "touch-target"
                  : "hover:text-primary"
              )}
            >
              {IconComponent && (
                <IconComponent 
                  size={deviceInfo.isMobile ? 24 : 20} 
                  aria-hidden="true" 
                />
              )}
            </a>
          );
        })}
      </nav>
    </div>
  );
};

FooterCompanyInfo.displayName = 'FooterCompanyInfo';

export default FooterCompanyInfo;
