import { useCallback, useEffect, useRef } from 'react';

/**
 * Custom hook for managing timeouts with automatic cleanup
 * Replaces duplicate timeout cleanup patterns found throughout the codebase
 * 
 * @example
 * ```typescript
 * const { setTimer, clearTimer, isActive } = useTimeout();
 * 
 * // Set a timeout
 * setTimer(() => {
 *   console.log('Timer executed');
 * }, 1000);
 * 
 * // Clear manually if needed
 * clearTimer();
 * ```
 */
export const useTimeout = () => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Set a new timeout, automatically clearing any existing one
   */
  const setTimer = useCallback((callback: () => void, delay: number) => {
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // Set new timeout
    timeoutRef.current = setTimeout(() => {
      callback();
      timeoutRef.current = null;
    }, delay);

    return timeoutRef.current;
  }, []);

  /**
   * Clear the current timeout
   */
  const clearTimer = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  /**
   * Check if timeout is currently active
   */
  const isActive = useCallback(() => {
    return timeoutRef.current !== null;
  }, []);

  /**
   * Automatic cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    setTimer,
    clearTimer,
    isActive,
  };
};

/**
 * Custom hook for managing animation timeouts specifically
 * Provides additional utilities for animation-related timing
 */
export const useAnimationTimeout = () => {
  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const setAnimationTimer = useCallback((callback: () => void, delay: number) => {
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
      animationTimeoutRef.current = null;
    }

    animationTimeoutRef.current = setTimeout(() => {
      callback();
      animationTimeoutRef.current = null;
    }, delay);

    return animationTimeoutRef.current;
  }, []);

  const clearAnimationTimer = useCallback(() => {
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
      animationTimeoutRef.current = null;
    }
  }, []);

  const isAnimationActive = useCallback(() => {
    return animationTimeoutRef.current !== null;
  }, []);

  useEffect(() => {
    return () => {
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, []);

  return {
    setAnimationTimer,
    clearAnimationTimer,
    isAnimationActive,
  };
};

/**
 * Custom hook for managing multiple named timers
 * Useful when a component needs to manage several different timeouts
 */
export const useMultipleTimeouts = () => {
  const timersRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  const setNamedTimer = useCallback((name: string, callback: () => void, delay: number) => {
    // Clear existing timer with this name
    const existingTimer = timersRef.current.get(name);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new timer
    const timerId = setTimeout(() => {
      callback();
      timersRef.current.delete(name);
    }, delay);

    timersRef.current.set(name, timerId);
    return timerId;
  }, []);

  const clearNamedTimer = useCallback((name: string) => {
    const timer = timersRef.current.get(name);
    if (timer) {
      clearTimeout(timer);
      timersRef.current.delete(name);
    }
  }, []);

  const clearAllTimers = useCallback(() => {
    timersRef.current.forEach((timer) => {
      clearTimeout(timer);
    });
    timersRef.current.clear();
  }, []);

  const isTimerActive = useCallback((name: string) => {
    return timersRef.current.has(name);
  }, []);

  const getActiveTimers = useCallback(() => {
    return Array.from(timersRef.current.keys());
  }, []);

  useEffect(() => {
    return () => {
      const timers = timersRef.current;
      timers.forEach((timer) => {
        clearTimeout(timer);
      });
      timers.clear();
    };
  }, []);

  return {
    setNamedTimer,
    clearNamedTimer,
    clearAllTimers,
    isTimerActive,
    getActiveTimers,
  };
};

export default useTimeout;
