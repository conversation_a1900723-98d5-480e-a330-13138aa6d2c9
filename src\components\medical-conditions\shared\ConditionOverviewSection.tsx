import { CheckCircle } from "lucide-react";
import React from 'react';

import SafeImage from '@/components/SafeImage';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface OverviewPoint {
  text: string;
  icon?: React.ReactNode;
}

interface ConditionOverviewSectionProps {
  title: string;
  description: string[];
  keyPoints?: OverviewPoint[];
  imageSrc?: string;
  imageAlt?: string;
  fallbackImageSrc?: string;
  imageCaption?: string;
  className?: string;
  reverseLayout?: boolean;
}

const ConditionOverviewSection: React.FC<ConditionOverviewSectionProps> = ({
  title,
  description,
  keyPoints = [],
  imageSrc,
  imageAlt = "Medical condition illustration",
  fallbackImageSrc = "https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
  imageCaption,
  className,
  reverseLayout = false
}) => {
  const deviceInfo = useDeviceDetection();

  const contentSection = (
    <div className={cn(
      "space-y-6",
      imageSrc && "lg:pr-8",
      "flex flex-col justify-center"
    )}>
      <h2 className={cn(
        "text-enhanced-heading font-bold leading-tight",
        deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
      )}>
        {title}
      </h2>
      <div className="content-spacing">
        {description.map((paragraph, index) => (
          <p
            key={index}
            className={cn(
              "text-enhanced-body leading-relaxed font-medium",
              deviceInfo.isMobile ? "text-base" : "text-lg"
            )}
          >
            {paragraph}
          </p>
        ))}
      </div>
      {keyPoints.length > 0 && (
        <div className="content-spacing mt-10">
          <h3 className="text-enhanced-heading font-bold text-xl mb-6">
            Key Points:
          </h3>
          <div className="grid gap-4">
            {keyPoints.map((point, index) => (
              <div
                key={index}
                className={cn(
                  "flex items-start gap-4 p-5 rounded-xl",
                  "medical-card hover:border-primary/40 hover:scale-[1.02]"
                )}
              >
                <div className="flex-shrink-0 mt-1">
                  <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
                    {point.icon || (
                      <CheckCircle className="h-5 w-5 text-primary" />
                    )}
                  </div>
                </div>
                <p className={cn(
                  "text-enhanced-body font-semibold leading-relaxed",
                  deviceInfo.isMobile ? "text-base" : "text-lg"
                )}>
                  {point.text}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  const imageSection = imageSrc ? (
    <div className={cn(
      "relative group",
      imageSrc && "lg:pl-8"
    )}>
      <div className="relative overflow-hidden rounded-xl shadow-xl border border-border/30">
        <SafeImage
          src={imageSrc}
          alt={imageAlt}
          className={cn(
            "w-full h-auto transition-transform duration-300",
            "group-hover:scale-105"
          )}
          fallbackSrc={fallbackImageSrc}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>
      {imageCaption && (
        <div className={cn(
          "mt-4 p-4 rounded-xl",
          "bg-muted/50 border border-border/50",
          "text-sm font-medium leading-relaxed text-muted-foreground"
        )}>
          {imageCaption}
        </div>
      )}
    </div>
  ) : null;

  return (
    <section className={cn(
      "section-background",
      deviceInfo.isMobile ? "py-16" : "section-spacing",
      className
    )}>
      <div className="container">
        <div className={cn(
          "grid items-center gap-16",
          imageSrc ? "lg:grid-cols-2" : "max-w-6xl mx-auto"
        )}>
          {reverseLayout ? (
            <>
              {imageSection}
              {contentSection}
            </>
          ) : (
            <>
              {contentSection}
              {imageSection}
            </>
          )}
        </div>
      </div>
    </section>
  );
};

ConditionOverviewSection.displayName = 'ConditionOverviewSection';

export default ConditionOverviewSection;
