import { CheckCircle } from "lucide-react";
import React from 'react';

import SafeImage from '@/components/SafeImage';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface OverviewPoint {
  text: string;
  icon?: React.ReactNode;
}

interface ConditionOverviewSectionProps {
  title: string;
  description: string[];
  keyPoints?: OverviewPoint[];
  imageSrc?: string;
  imageAlt?: string;
  fallbackImageSrc?: string;
  imageCaption?: string;
  className?: string;
  reverseLayout?: boolean;
}

const ConditionOverviewSection: React.FC<ConditionOverviewSectionProps> = ({
  title,
  description,
  keyPoints = [],
  imageSrc,
  imageAlt = "Medical condition illustration",
  fallbackImageSrc = "https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
  imageCaption,
  className,
  reverseLayout = false
}) => {
  const deviceInfo = useDeviceDetection();

  const contentSection = (
    <div className="medical-content-layout">
      <h2 className="medical-heading">
        {title}
      </h2>
      <div className="space-y-4">
        {description.map((paragraph, index) => (
          <p
            key={index}
            className="medical-body"
          >
            {paragraph}
          </p>
        ))}
      </div>
      {keyPoints.length > 0 && (
        <div className="medical-section-divider">
          <h3 className="medical-subheading">
            Key Points:
          </h3>
          <div className="grid gap-4">
            {keyPoints.map((point, index) => (
              <div
                key={index}
                className="medical-info-panel flex items-start gap-4"
              >
                <div className="flex-shrink-0 mt-1">
                  <div className="p-2 rounded-lg bg-primary/20 border border-primary/30">
                    {point.icon || (
                      <CheckCircle className="h-5 w-5 text-primary" />
                    )}
                  </div>
                </div>
                <p className="medical-body mb-0">
                  {point.text}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  const imageSection = imageSrc ? (
    <div className="relative">
      <div className="medical-image-container medical-content-card">
        <SafeImage
          src={imageSrc}
          alt={imageAlt}
          className="w-full h-auto object-cover rounded-lg transition-transform duration-300 hover:scale-105"
          fallbackSrc={fallbackImageSrc}
        />
      </div>
      {imageCaption && (
        <div className="medical-caption text-center">
          {imageCaption}
        </div>
      )}
    </div>
  ) : null;

  return (
    <section className={cn(
      "section-spacing section-background",
      className
    )}>
      <div className="section-container">
        <div className={cn(
          "medical-content-grid items-center",
          !imageSrc && "content-width-standard"
        )}>
          {reverseLayout ? (
            <>
              {imageSection}
              {contentSection}
            </>
          ) : (
            <>
              {contentSection}
              {imageSection}
            </>
          )}
        </div>
      </div>
    </section>
  );
};

ConditionOverviewSection.displayName = 'ConditionOverviewSection';

export default ConditionOverviewSection;
