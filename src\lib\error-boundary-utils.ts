import React, { ErrorInfo, ReactNode } from 'react';

import { logError } from '@/lib/dev-console';

// Note: Import ErrorBoundary component when using these utilities
// import ErrorBoundary from '@/components/ErrorBoundary';

/**
 * Higher-order component for wrapping components with error boundary
 * Note: You need to import ErrorBoundary component to use this function
 */
export function withErrorBoundary(
  Component: React.ComponentType<Record<string, unknown>>,
  ErrorBoundaryComponent: React.ComponentType<{ children: React.ReactNode; fallback?: ReactNode }>,
  fallback?: ReactNode
) {
  const WrappedComponent = (props: Record<string, unknown>) =>
    React.createElement(ErrorBoundaryComponent, { fallback },
      React.createElement(Component, props)
    );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

/**
 * Hook for error boundary functionality in functional components
 */
export function useErrorHandler() {
  return (_error: Error, _errorInfo?: ErrorInfo) => {
    // In a real application, you might want to log this to an error service
    // Example: logErrorToService(error, errorInfo);

    // You could also trigger a state update to show an error UI
    // Log error in development
    logError('Error caught by error handler', _error);
    logError('Error info', _errorInfo);
  };
}
