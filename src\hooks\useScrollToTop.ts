import { useEffect } from 'react';

interface UseScrollToTopOptions {
  smooth?: boolean;
  delay?: number;
  condition?: boolean;
}

export const useScrollToTop = (options: UseScrollToTopOptions = {}) => {
  const { smooth = false, delay = 0, condition = true } = options;

  useEffect(() => {
    if (!condition) return;

    const scrollToTop = () => {
      if (smooth) {
        window.scrollTo({
          top: 0,
          left: 0,
          behaviour: 'smooth'
        });
      } else {
        window.scrollTo(0, 0);
      }
    };

    if (delay > 0) {
      const timeoutId = setTimeout(scrollToTop, delay);
      return () => clearTimeout(timeoutId);
    } else {
      scrollToTop();
    }
  }, [smooth, delay, condition]);
};

export default useScrollToTop;
