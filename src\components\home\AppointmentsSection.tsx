import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import type { AppointmentsSectionProps } from '@/types/homepage';

/**
 * Comprehensive Appointments Section Component
 * Displays consultation process and booking information
 * Preserves all original content and styling from Index.tsx lines 918-1009
 */
const AppointmentsSection: React.FC<AppointmentsSectionProps> = ({ consultationSteps, preparation }) => {
  return (
    <section className="section">
      <div className="container">
        <div className="text-center mb-12">
          <span className="text-primary font-medium uppercase tracking-wider text-sm">
            CONSULTATION PROCESS
          </span>
          <h2 className="text-enhanced-heading text-3xl md:text-enhanced-heading text-4xl font-bold mt-2 mb-6">Appointments and Consultation</h2>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            Our comprehensive consultation process ensures thorough evaluation and personalised treatment planning for each patient.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {consultationSteps.map((step) => (
            <ConsultationStepCard key={step.id} step={step} />
          ))}
        </div>

        <div className="bg-primary/5 p-8 rounded-lg mb-8">
          <h3 className="text-xl font-semibold mb-4 text-center">What to Expect During Your Consultation</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Before Your Visit:</h4>
              <ul className="space-y-1 text-muted-foreground">
                {preparation.beforeVisit.map((item, index) => (
                  <li key={index}>– {item}</li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">During Your Visit:</h4>
              <ul className="space-y-1 text-muted-foreground">
                {preparation.duringVisit.map((item, index) => (
                  <li key={index}>– {item}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <div className="text-center">
          <p className="text-muted-foreground mb-6">
            We are committed to providing thorough, compassionate care and ensuring you have all the information needed to make informed decisions about your treatment.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link to="/appointments">
                Book Consultation
              </Link>
            </Button>
            <Button variant="outline" asChild size="lg">
              <Link to="/patient-resources">
                Patient Resources
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

/**
 * Individual Consultation Step Card Component
 */
interface ConsultationStepCardProps {
  step: {
    id: string;
    title: string;
    description: string;
    icon: string;
  };
}

const ConsultationStepCard: React.FC<ConsultationStepCardProps> = ({ step }) => {
  const renderIcon = (iconType: string) => {
    switch (iconType) {
      case 'calendar':
        return (
          <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        );
      case 'document':
        return (
          <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
      case 'lightbulb':
        return (
          <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div className="text-center">
      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
        {renderIcon(step.icon)}
      </div>
      <h3 className="text-xl font-semibold mb-3">{step.title}</h3>
      <p className="text-muted-foreground">
        {step.description}
      </p>
    </div>
  );
};

AppointmentsSection.displayName = 'AppointmentsSection';

export default AppointmentsSection;
