import { CheckCircle2 } from 'lucide-react';
import React from 'react';

import { FinalTakeaway } from '@/data/patient-resources/ageSpecificRecommendationsData';

interface FinalTakeawaysSectionProps {
  title: string;
  takeaways: FinalTakeaway[];
}

const FinalTakeawaysSection: React.FC<FinalTakeawaysSectionProps> = ({ title, takeaways }) => {
  return (
    <div>
      <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{title}</h2>

      <ul className="space-y-4 list-none pl-0 my-6">
        {takeaways.map((takeaway) => (
          <li key={takeaway.id} className="flex items-start">
            <CheckCircle2 className="h-6 w-6 text-primary mr-2 flex-shrink-0 mt-0.5" />
            <span>
              <strong>{takeaway.title}</strong> – {takeaway.description}
            </span>
          </li>
        ))}
      </ul>
    </div>
  );
};

FinalTakeawaysSection.displayName = 'FinalTakeawaysSection';

export default FinalTakeawaysSection;
