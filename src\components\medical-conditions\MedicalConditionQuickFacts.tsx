import { LucideIcon } from 'lucide-react';
import React from 'react';

import { Card, CardContent } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

export interface QuickFact {
  icon: LucideIcon;
  title: string;
  value: string;
  description?: string;
}

export interface MedicalConditionQuickFactsProps {
  facts: QuickFact[];
  className?: string;
}

const MedicalConditionQuickFacts: React.FC<MedicalConditionQuickFactsProps> = ({
  facts,
  className
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "bg-gradient-to-r from-muted/20 via-muted/30 to-muted/20",
      "border-y border-border/50",
      deviceInfo.isMobile ? "py-12" : "py-16",
      className
    )}>
      <div className="container">
        <div className={cn(
          "grid gap-6",
          facts.length <= 2
            ? "grid-cols-1 md:grid-cols-2"
            : facts.length === 3
            ? "grid-cols-1 md:grid-cols-3"
            : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
        )}>
          {facts.map((fact, index) => {
            const IconComponent = fact.icon;
            return (
              <Card
                key={index}
                className={cn(
                  "text-center transition-all duration-300 hover:shadow-lg hover:scale-105",
                  "bg-card/80 backdrop-blur-sm border-border/50",
                  "hover:bg-card hover:border-primary/20"
                )}
              >
                <CardContent className="pt-8 pb-6 px-6">
                  <div className={cn(
                    "w-16 h-16 mx-auto mb-4 rounded-full",
                    "bg-primary/10 dark:bg-primary/20",
                    "flex items-center justify-center",
                    "border-2 border-primary/20"
                  )}>
                    <IconComponent className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="font-bold text-foreground mb-2 text-lg">
                    {fact.title}
                  </h3>
                  <p className="text-base font-semibold text-primary mb-2">
                    {fact.value}
                  </p>
                  {fact.description && (
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {fact.description}
                    </p>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default MedicalConditionQuickFacts;
