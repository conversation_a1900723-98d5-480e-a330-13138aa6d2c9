import { Target, Calendar } from 'lucide-react';
import { Link } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ConditionHeroProps {
  title: string;
  subtitle: string;
  backgroundImage: string;
  fallbackImage?: string;
  badge?: string;
  showAssessment?: boolean;
  showBooking?: boolean;
  assessmentLink?: string;
  bookingLink?: string;
}

export function ConditionHero({
  title,
  subtitle,
  backgroundImage,
  fallbackImage = "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
  badge = "Spine Conditions Library",
  showAssessment = true,
  showBooking = true,
  assessmentLink = "#assessment",
  bookingLink = "/contact"
}: ConditionHeroProps) {
  const deviceInfo = useDeviceDetection();

  return (
    <header className={cn(
      "relative section-background-alt",
      "border-b border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24"
    )}>
      <div className="absolute inset-0 overflow-hidden opacity-5 dark:opacity-10">
        <SafeImage
          src={backgroundImage}
          alt={`${title} anatomy`}
          className="w-full h-full object-cover"
          fallbackSrc={fallbackImage}
        />
      </div>
      <div className="container relative z-10">
        <div className="text-center max-w-5xl mx-auto">
          <Badge
            variant="secondary"
            className={cn(
              "mb-6 px-4 py-2 text-sm font-medium",
              "bg-primary/10 text-primary border-primary/20",
              "dark:bg-primary/20 dark:text-primary-foreground dark:border-primary/30"
            )}
          >
            {badge}
          </Badge>
          <h1 className={cn(
            "font-bold text-foreground mb-6 leading-tight",
            deviceInfo.isMobile ? "text-3xl" : "text-4xl md:text-5xl lg:text-6xl"
          )}>
            {title}
          </h1>
          <p className={cn(
            "text-muted-foreground mb-10 leading-relaxed max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-base" : "text-lg md:text-xl"
          )}>
            {subtitle}
          </p>
          <div className={cn(
            "flex gap-4 justify-center",
            deviceInfo.isMobile ? "flex-col items-center" : "flex-row items-center"
          )}>
            {showAssessment && (
              <Button
                asChild
                size={deviceInfo.isMobile ? "lg" : "lg"}
                className={cn(
                  "px-8 py-3 font-semibold shadow-lg hover:shadow-xl",
                  "bg-primary hover:bg-primary/90 text-primary-foreground",
                  "transition-all duration-300 hover:scale-105",
                  "border-2 border-primary hover:border-primary/80",
                  deviceInfo.isMobile ? "w-full max-w-sm" : "min-w-[200px]"
                )}
              >
                <Link to={assessmentLink}>
                  <Target className="mr-2 h-5 w-5" />
                  Take Assessment
                </Link>
              </Button>
            )}
            {showBooking && (
              <Button
                asChild
                variant="outline"
                size={deviceInfo.isMobile ? "lg" : "lg"}
                className={cn(
                  "px-8 py-3 font-semibold shadow-lg hover:shadow-xl",
                  "bg-background hover:bg-muted text-foreground",
                  "border-2 border-primary hover:border-primary/80",
                  "transition-all duration-300 hover:scale-105",
                  deviceInfo.isMobile ? "w-full max-w-sm" : "min-w-[200px]"
                )}
              >
                <Link to={bookingLink}>
                  <Calendar className="mr-2 h-5 w-5" />
                  Book Consultation
                </Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}