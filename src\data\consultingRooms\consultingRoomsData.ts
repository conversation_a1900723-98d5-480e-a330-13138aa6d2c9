export interface FeatureCard {
  id: string;
  title: string;
  description: string;
  icon: string;
}

export interface FacilityCard {
  id: string;
  title: string;
  description: string;
}

export interface GalleryImage {
  id: string;
  src: string;
  alt: string;
}

export interface FormField {
  id: string;
  name: string;
  label: string;
  type: 'text' | 'email' | 'tel' | 'select' | 'checkbox' | 'textarea';
  placeholder?: string;
  options?: { value: string; label: string }[];
  required?: boolean;
}

export interface ConsultingRoomsConfiguration {
  hero: {
    subtitle: string;
    title: string;
    description: string;
    ctaText: string;
    ctaLink: string;
  };
  whyChoose: {
    title: string;
    description: string;
  };
  features: FeatureCard[];
  facilities: {
    title: string;
    items: FacilityCard[];
    mainImage: {
      src: string;
      alt: string;
    };
  };
  location: {
    title: string;
    description: string;
    address: string;
  };
  form: {
    title: string;
    fields: FormField[];
    submitText: string;
    confirmationText: string;
    thankYouMessage: string;
  };
  gallery: {
    title: string;
    description: string;
    images: GalleryImage[];
  };
  cta: {
    title: string;
    description: string;
    buttonText: string;
    buttonLink: string;
  };
}

interface TranslationObject {
  consultingRooms?: {
    hero?: {
      title?: string;
      subtitle?: string;
      description?: string;
      scheduleViewing?: string;
    };
    features?: Record<string, { title?: string; description?: string }>;
    whyChoose?: { title?: string; description?: string };
    facilities?: Record<string, { title?: string; description?: string }>;
    cta?: { title?: string; description?: string; buttonText?: string; buttonLink?: string };
  };
}

export const getConsultingRoomsData = (t?: TranslationObject): ConsultingRoomsConfiguration => {

  // Safe fallback for translations
  const finalT = (t && t.consultingRooms) ? t : {
    consultingRooms: {
      hero: {
        subtitle: "Professional Environment",
        title: "Expand Your Medical Practice in Surrey Hills",
        description: "Rent a fully-equipped medical consulting room in Surrey Hills at miNEURO Medical Consulting Suites. Perfect for healthcare professionals, our state-of-the-art space is located near major medical facilities and transport links."
      },
      whyChoose: {
        title: "Why You Should Choose Our Suites",
        description: "At miNEURO Medical Consulting Rooms, we are committed to providing healthcare professionals with the resources and support tailored to meet the diverse needs of healthcare professionals."
      },
      features: {
        convenientLocation: {
          title: "Convenient Location",
          description: "Our facility is within a few minutes drive from the bustling Box Hill Medical Hub, Epworth Eastern Hospital, several GP clinics and busy shopping/business/transportation areas."
        },
        fullyEquipped: {
          title: "Fully-Equipped Rooms",
          description: "Our consulting rooms are equipped with modern medical equipment, experienced reception staff, examination tables, ergonomic seating, computers, sinks, and high-speed internet access."
        },
        flexibleTerms: {
          title: "Flexible Lease Terms",
          description: "We offer flexible lease terms, including sessional, short-term and long-term rental options, to accommodate your needs."
        }
      },
      facilities: {
        title: "Facilities",
        consultingRooms: {
          title: "Consulting Rooms",
          description: "Our consulting rooms are meticulously designed and equipped to support various medical specialties. Each room has modern medical equipment, including examination tables, sinks, ergonomic seating, and ample storage space."
        },
        waitingArea: {
          title: "Comfortable Waiting Area",
          description: "Our welcoming waiting area is designed to provide comfort and convenience for patients and their companions. Furnished with comfortable seating, reading materials, a fridge with refreshing drinks, a TV, a HiFi audio system and ambient lighting."
        },
        technology: {
          title: "Technology Infrastructure",
          description: "High-speed internet access is available in every consulting room, allowing healthcare professionals to seamlessly access electronic medical records and research resources and communicate with colleagues."
        }
      },
      location: {
        title: "Location",
        description: "Our consulting suites are strategically located at 619 Canterbury Road, Surrey Hills, offering a prime location for healthcare professionals seeking a convenient and accessible workspace."
      },
      contact: {
        address: "Suite 4, 619 Canterbury Road, Surrey Hills VIC 3127"
      },
      form: {
        title: "Consulting Room Inquiry",
        firstName: "First Name",
        lastName: "Last Name",
        specialty: "Medical Specialty",
        email: "Email",
        phone: "Phone",
        preferredLocation: "Preferred Location",
        selectLocation: "Select a location",
        rentalFrequency: "Session Frequency",
        selectFrequency: "Select frequency",
        preferredDays: "Preferred Days",
        additionalRequirements: "Additional Requirements",
        additionalRequirementsPlaceholder: "Please specify any additional requirements or questions you may have",
        submit: "Submit Inquiry",
        confirmation: "We will contact you within 1-2 business days to discuss your requirements and availability.",
        thankYou: "Thank you for your inquiry. We will contact you shortly.",
        locations: {
          surreyHills: "Surrey Hills",
          mornington: "Mornington",
          frankston: "Frankston"
        },
        frequencies: {
          weekly: "Weekly",
          fortnightly: "Fortnightly",
          monthly: "Monthly",
          casual: "Casual"
        },
        days: {
          monday: "Monday",
          tuesday: "Tuesday",
          wednesday: "Wednesday",
          thursday: "Thursday",
          friday: "Friday"
        }
      },
      gallery: {
        title: "Facility Gallery",
        description: "Our Surrey Hills medical consulting facility features modern, fully-equipped consulting rooms designed to provide a professional and comfortable environment for healthcare practitioners and their patients."
      },
      cta: {
        title: "Have Questions?",
        description: "If you have any questions about our consulting room options or would like to arrange a viewing, please don't hesitate to contact us.",
        contactUs: "Contact Us"
      }
    }
  };

  return {
    hero: {
      subtitle: finalT.consultingRooms?.hero?.subtitle || "Professional Environment",
      title: finalT.consultingRooms?.hero?.title || "Expand Your Medical Practice in Surrey Hills",
      description: finalT.consultingRooms?.hero?.description || "Rent a fully-equipped medical consulting room in Surrey Hills at miNEURO Medical Consulting Suites.",
      ctaText: "Schedule Viewing",
      ctaLink: "#inquiry-form"
    },
    whyChoose: {
      title: finalT.consultingRooms?.whyChoose?.title || "Why You Should Choose Our Suites",
      description: finalT.consultingRooms?.whyChoose?.description || "At miNEURO Medical Consulting Rooms, we are committed to providing healthcare professionals with the resources and support tailored to meet the diverse needs of healthcare professionals."
    },
    features: [
      {
        id: 'convenient-location',
        title: finalT.consultingRooms.features.convenientLocation.title,
        description: finalT.consultingRooms.features.convenientLocation.description,
        icon: 'MapPin'
      },
      {
        id: 'fully-equipped',
        title: finalT.consultingRooms.features.fullyEquipped.title,
        description: finalT.consultingRooms.features.fullyEquipped.description,
        icon: 'Building'
      },
      {
        id: 'flexible-terms',
        title: finalT.consultingRooms.features.flexibleTerms.title,
        description: finalT.consultingRooms.features.flexibleTerms.description,
        icon: 'Clock'
      }
    ],
    facilities: {
      title: finalT.consultingRooms?.facilities?.title || "Facilities",
      items: [
        {
          id: 'consulting-rooms',
          title: finalT.consultingRooms?.facilities?.consultingRooms?.title || "Consulting Rooms",
          description: finalT.consultingRooms?.facilities?.consultingRooms?.description || "Modern, fully-equipped consulting rooms designed for healthcare professionals."
        },
        {
          id: 'waiting-area',
          title: finalT.consultingRooms?.facilities?.waitingArea?.title || "Waiting Area",
          description: finalT.consultingRooms?.facilities?.waitingArea?.description || "Comfortable waiting area for patients and their companions."
        },
        {
          id: 'technology',
          title: finalT.consultingRooms?.facilities?.technology?.title || "Technology",
          description: finalT.consultingRooms?.facilities?.technology?.description || "State-of-the-art medical technology and equipment."
        }
      ],
      mainImage: {
        src: '/images/medical-consulting-room-lease-Surrey-Hills-miNEURO-doctor-desk.jpg',
        alt: 'Medical Consulting Room'
      }
    },
    location: {
      title: finalT.consultingRooms?.location?.title || "Location",
      description: finalT.consultingRooms?.location?.description || "Conveniently located in Surrey Hills with excellent transport links.",
      address: finalT.consultingRooms?.contact?.address || "Suite 4, 619 Canterbury Road, Surrey Hills VIC 3127"
    },
    form: {
      title: finalT.consultingRooms?.form?.title || "Consulting Room Inquiry",
      fields: [
        {
          id: 'firstName',
          name: 'firstName',
          label: finalT.consultingRooms?.form?.firstName || "First Name",
          type: 'text',
          placeholder: finalT.consultingRooms?.form?.firstName || "First Name",
          required: true
        },
        {
          id: 'lastName',
          name: 'lastName',
          label: finalT.consultingRooms?.form?.lastName || "Last Name",
          type: 'text',
          placeholder: finalT.consultingRooms?.form?.lastName || "Last Name",
          required: true
        },
        {
          id: 'specialty',
          name: 'specialty',
          label: finalT.consultingRooms?.form?.specialty || "Medical Specialty",
          type: 'text',
          placeholder: finalT.consultingRooms?.form?.specialty || "Medical Specialty",
          required: true
        },
        {
          id: 'email',
          name: 'email',
          label: finalT.consultingRooms?.form?.email || "Email",
          type: 'email',
          placeholder: finalT.consultingRooms?.form?.email || "Email",
          required: true
        },
        {
          id: 'phone',
          name: 'phone',
          label: finalT.consultingRooms?.form?.phone || "Phone",
          type: 'tel',
          placeholder: finalT.consultingRooms?.form?.phone || "Phone",
          required: true
        },
        {
          id: 'preferredLocation',
          name: 'preferredLocation',
          label: finalT.consultingRooms?.form?.preferredLocation || "Preferred Location",
          type: 'select',
          options: [
            { value: '', label: finalT.consultingRooms?.form?.selectLocation || "Select Location" },
            { value: 'surrey-hills', label: finalT.consultingRooms?.form?.locations?.surreyHills || "Surrey Hills" },
            { value: 'mornington', label: finalT.consultingRooms?.form?.locations?.mornington || "Mornington" },
            { value: 'frankston', label: finalT.consultingRooms?.form?.locations?.frankston || "Frankston" }
          ],
          required: true
        },
        {
          id: 'rentalFrequency',
          name: 'rentalFrequency',
          label: finalT.consultingRooms?.form?.rentalFrequency || "Rental Frequency",
          type: 'select',
          options: [
            { value: '', label: finalT.consultingRooms?.form?.selectFrequency || "Select Frequency" },
            { value: 'weekly', label: finalT.consultingRooms?.form?.frequencies?.weekly || "Weekly" },
            { value: 'fortnightly', label: finalT.consultingRooms?.form?.frequencies?.fortnightly || "Fortnightly" },
            { value: 'monthly', label: finalT.consultingRooms?.form?.frequencies?.monthly || "Monthly" },
            { value: 'casual', label: finalT.consultingRooms?.form?.frequencies?.casual || "Casual" }
          ],
          required: true
        },
        {
          id: 'additionalRequirements',
          name: 'additionalRequirements',
          label: finalT.consultingRooms?.form?.additionalRequirements || "Additional Requirements",
          type: 'textarea',
          placeholder: finalT.consultingRooms?.form?.additionalRequirementsPlaceholder || "Please describe any additional requirements or questions"
        }
      ],
      submitText: finalT.consultingRooms?.form?.submit || "Submit Inquiry",
      confirmationText: finalT.consultingRooms?.form?.confirmation || "Thank you for your inquiry. We will contact you soon.",
      thankYouMessage: finalT.consultingRooms?.form?.thankYou || "Thank you for your interest in our consulting rooms."
    },
    gallery: {
      title: finalT.consultingRooms?.gallery?.title || "Gallery",
      description: finalT.consultingRooms?.gallery?.description || "Take a virtual tour of our consulting rooms and facilities.",
      images: [
        {
          id: 'entrance',
          src: '/images/medical-consulting-room-rent-Surrey-Hills-miNEURO-entrance.jpg',
          alt: 'miNEURO Medical Consulting Entrance'
        },
        {
          id: 'reception',
          src: '/images/medical-consulting-room-lease-Surrey-Hills-miNEURO-reception.jpg',
          alt: 'miNEURO Medical Consulting Reception'
        },
        {
          id: 'waiting-area',
          src: '/images/medical-consulting-room-lease-Surrey-Hills-miNEURO-waiting-area.jpg',
          alt: 'miNEURO Medical Consulting Waiting Area'
        },
        {
          id: 'doctor-desk',
          src: '/images/medical-consulting-room-lease-Surrey-Hills-miNEURO-doctor-desk.jpg',
          alt: 'miNEURO Medical Consulting Doctor\'s Desk'
        },
        {
          id: 'examination-area',
          src: '/images/medical-consulting-room-lease-Surrey-Hills-miNEURO-doctor-desk-sink.jpg',
          alt: 'miNEURO Medical Consulting Examination Area'
        },
        {
          id: 'kitchenette',
          src: '/images/medical-consulting-room-rent-Surrey-Hills-miNEURO-kitchenette.jpg',
          alt: 'miNEURO Medical Consulting Kitchenette'
        }
      ]
    },
    cta: {
      title: finalT.consultingRooms?.cta?.title || "Have Questions?",
      description: finalT.consultingRooms?.cta?.description || "If you have any questions about our consulting room options or would like to arrange a viewing, please don't hesitate to contact us.",
      buttonText: finalT.consultingRooms?.cta?.contactUs || "Contact Us",
      buttonLink: '/contact'
    }
  };
};
