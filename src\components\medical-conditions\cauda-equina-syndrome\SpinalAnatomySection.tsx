import { Brain, Network, Zap, Info, ChevronDown, ChevronUp, Activity, Layers, Droplets } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface SpinalComponent {
  component: string;
  description: string;
  normalFunction: string[];
  compressionEffects: string[];
}

interface SpinalAnatomySectionProps {
  title: string;
  description: string;
  spinalComponents: SpinalComponent[];
}

export function SpinalAnatomySection({ 
  title, 
  description, 
  spinalComponents 
}: SpinalAnatomySectionProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedComponent, setExpandedComponent] = useState<string | null>(null);

  const toggleExpanded = (component: string) => {
    setExpandedComponent(expandedComponent === component ? null : component);
  };

  const getComponentIcon = (component: string) => {
    if (component.includes('Cauda Equina')) return Brain;
    if (component.includes('Spinal Canal')) return Layers;
    if (component.includes('Disc')) return Network;
    return Activity;
  };

  const getComponentColor = (component: string) => {
    if (component.includes('Cauda Equina')) return 'text-foreground bg-muted/30 border-border';
    if (component.includes('Spinal Canal')) return 'text-primary bg-primary/10 border-primary/50';
    if (component.includes('Disc')) return 'text-foreground dark:text-foreground bg-success/10 border-success/50';
    return 'text-muted-foreground bg-muted border-border';
  };

  const getCompressionLevel = (component: string) => {
    if (component.includes('Cauda Equina')) {
      return { level: 'Critical Structure', colour: 'bg-muted/30 text-foreground' };
    }
    if (component.includes('Spinal Canal')) {
      return { level: 'Protective Space', colour: 'bg-primary/10 text-primary' };
    }
    return { level: 'Support Structure', colour: 'bg-success/10 text-success dark:text-success' };
  };

  return (
    <section className={cn("py-16", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Anatomical Overview */}
        <div className="mb-12">
          <Card className="bg-muted">
            <CardContent className="pt-6">
              <div className={cn(
                "grid gap-8 items-center",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Cauda Equina and Spinal Compression</h3>
                  <p className="text-muted-foreground mb-4">
                    Cauda equina syndrome occurs when the bundle of nerve roots at the lower end of the spinal cord 
                    becomes compressed. These nerve roots, resembling a horse's tail, control critical functions 
                    including bladder, bowel, sexual function, and leg movement.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-muted rounded-full"></div>
                      <span className="text-sm">Cauda Equina Nerve Roots</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-info rounded-full"></div>
                      <span className="text-sm">Lumbar Spinal Canal</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-success rounded-full"></div>
                      <span className="text-sm">Intervertebral Discs</span>
                    </div>
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="relative">
                    <img 
                      src="/images/brain-conditions/brain-anatomy-detailed.jpg" 
                      alt="Cauda equina anatomy showing nerve root compression"
                      className="rounded-lg shadow-lg max-w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Spinal Component Details */}
        <div className="space-y-6">
          {spinalComponents.map((component, index) => {
            const Icon = getComponentIcon(component.component);
            const isExpanded = expandedComponent === component.component;
            const compressionLevel = getCompressionLevel(component.component);
            
            return (
              <Card key={index} className={cn("transition-all duration-200", getComponentColor(component.component))}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-full bg-background/80">
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{component.component}</CardTitle>
                        <CardDescription className="text-sm">{component.description}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={compressionLevel.colour}>
                        {compressionLevel.level}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(component.component)}
                        className="flex items-center gap-1"
                      >
                        <Info className="h-4 w-4" />
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        {/* Normal Functions */}
                        <div>
                          <h4 className="font-semibold mb-3">Normal Functions</h4>
                          <ul className="space-y-2">
                            {component.normalFunction.map((func, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{func}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Compression Effects */}
                        <div>
                          <h4 className="font-semibold mb-3">Effects of Compression</h4>
                          <ul className="space-y-2">
                            {component.compressionEffects.map((effect, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{effect}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Additional Information */}
                      <div className="mt-6 p-4 bg-background/50 rounded-lg">
                        <h4 className="font-semibold mb-2">Clinical Significance</h4>
                        <p className="text-sm text-muted-foreground">
                          {component.component.includes('Cauda Equina') && 
                            "The cauda equina nerve roots are critical for bladder, bowel, sexual, and leg function. Compression of these nerves constitutes a surgical emergency requiring immediate intervention to prevent permanent disability."
                          }
                          {component.component.includes('Spinal Canal') && 
                            "The lumbar spinal canal provides protection for the cauda equina. Narrowing of this space from any cause can lead to nerve compression and the development of cauda equina syndrome."
                          }
                          {component.component.includes('Disc') && 
                            "Intervertebral discs can herniate into the spinal canal, causing compression of the cauda equina. Large central disc herniations are the most common cause of cauda equina syndrome."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Compression Mechanism */}
        <div className="mt-12">
          <Card className="bg-muted">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-foreground" />
                How Cauda Equina Compression Develops
              </CardTitle>
              <CardDescription>
                Understanding the mechanism of nerve compression in cauda equina syndrome
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="text-center">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    1
                  </div>
                  <h4 className="font-semibold mb-2">Initial Compression</h4>
                  <p className="text-sm text-muted-foreground">
                    Disc herniation, stenosis, or mass lesion begins to compress cauda equina nerve roots
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    2
                  </div>
                  <h4 className="font-semibold mb-2">Progressive Dysfunction</h4>
                  <p className="text-sm text-muted-foreground">
                    Nerve function deteriorates causing bladder, bowel, and leg symptoms
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    3
                  </div>
                  <h4 className="font-semibold mb-2">Permanent Damage</h4>
                  <p className="text-sm text-muted-foreground">
                    Without urgent treatment, irreversible nerve damage and permanent disability occur
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Emergency Warning */}
        <div className="mt-12">
          <Card className="bg-muted/50 border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-foreground">
                <Droplets className="h-5 w-5" />
                Medical Emergency
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground text-sm">
                Cauda equina syndrome is a surgical emergency. Compression of these critical nerve roots 
                requires immediate surgical decompression within 48-72 hours to prevent permanent loss of 
                bladder, bowel, sexual, and leg function. Early recognition and urgent treatment are 
                essential for optimal outcomes.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default SpinalAnatomySection;
