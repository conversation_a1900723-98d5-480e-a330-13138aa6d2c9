import React, { useEffect } from 'react';

import ConditionSpecificSection from '@/components/lifestyle-modifications/ConditionSpecificSection';
import InterventionalProceduresSection from '@/components/lifestyle-modifications/InterventionalProceduresSection';
import TreatmentModalitiesSection from '@/components/lifestyle-modifications/TreatmentModalitiesSection';
import PageHeader from '@/components/PageHeader';
import StandardPageLayout from '@/components/StandardPageLayout';
import { conditionCategories } from '@/data/lifestyle-modifications/conditionData';
import { procedureCategories } from '@/data/lifestyle-modifications/interventionalProceduresData';
import { treatmentModalities } from '@/data/lifestyle-modifications/treatmentModalitiesData';

/**
 * Lifestyle Modifications Page Component
 *
 * Comprehensive information about lifestyle modifications for spine conditions,
 * treatment modalities, and interventional procedures using modular architecture.
 */

const LifestyleModifications: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout showHeader={false}>
      <PageHeader
        title="Lifestyle Modifications for Spine Health"
        subtitle="Evidence-based recommendations to manage spine conditions and improve quality of life"
        backgroundImage="/images/patient-resources/lifestyle-modifications-hero.jpg"
        enableParallax={true}
      />

      <main className="flex-1">
        {/* Introduction Section */}
        <section className="py-16">
          <div className="container max-w-7xl">
            <div className="max-w-4xl mx-auto text-center">
              <div className="space-y-6">
                <p className="text-lg text-muted-foreground leading-relaxed">
                  Lifestyle modifications play a crucial role in managing spine conditions and can significantly impact your quality of life. 
                  This comprehensive guide provides evidence-based recommendations tailored to specific conditions and treatment approaches.
                </p>
                
                <p className="text-base text-muted-foreground leading-relaxed">
                  Whether you're dealing with neck pain, back problems, or considering various treatment options, understanding how 
                  lifestyle changes can support your recovery is essential for optimal outcomes.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Condition-Specific Recommendations */}
        <ConditionSpecificSection 
          categories={conditionCategories}
          title="Condition-Specific Recommendations"
        />

        {/* Treatment Modalities */}
        <TreatmentModalitiesSection 
          modalities={treatmentModalities}
          title="Treatment Modalities"
          description="There are various approaches to managing spine conditions, each with its own benefits and considerations. Understanding these options can help you make informed decisions about your care."
        />

        {/* Interventional Procedures */}
        <InterventionalProceduresSection 
          categories={procedureCategories}
          title="Interventional Procedures & Minimally Invasive Surgery"
          description="When conservative treatments don't provide adequate relief, interventional procedures or minimally invasive surgeries may be considered. These approaches aim to provide targeted treatment with less tissue disruption and faster recovery than traditional surgery."
        />

        {/* Call to Action Section */}
        <section className="py-16">
          <div className="container max-w-7xl">
            <div className="max-w-3xl mx-auto text-center">
              <div className="medical-card/50 backdrop-blur-sm border border-border/50 p-8 rounded-lg shadow-md">
                <h2 className="text-enhanced-heading text-2xl font-bold mb-4 text-foreground">
                  Ready to Take Control of Your Spine Health?
                </h2>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  Our comprehensive spine health program combines personalised assessments, evidence-based recommendations, 
                  and ongoing support to help you achieve optimal spine health.
                </p>
                <div className="flex flex-col sm:flex-row justify-center gap-4">
                  <a 
                    href="/patient-resources/individual-spine-health-programme"
                    className="inline-flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                  >
                    Start Your Assessment
                  </a>
                  <a 
                    href="/appointments"
                    className="inline-flex items-center justify-center px-6 py-3 border border-border rounded-md hover:bg-muted transition-colors"
                  >
                    Book Consultation
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </StandardPageLayout>
  );
};

LifestyleModifications.displayName = 'LifestyleModifications';

export default LifestyleModifications;
