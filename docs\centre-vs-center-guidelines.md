# Centre vs Center Usage Guidelines

## Clear Definition

This document establishes the clear distinction between **content language** (Australian English) and **code styling** (technical requirements) for the use of "centre" vs "center" throughout the codebase.

## ✅ **CONTENT USAGE - Australian English "centre"**

### When to use "centre":
- **Medical facility names**: "Moonee Ponds Specialist Centre", "Mornington Specialist Centre"
- **Location references**: "specialist centres", "stroke centres", "medical centres"
- **Content text**: Any user-facing text content, headings, descriptions, labels
- **Business names**: "Clocktower Centre", "Shopping Centre"
- **Australian addresses**: Street names, facility names
- **Content in markdown files**: Documentation, page content, descriptions

### Examples:
```markdown
- "Dr<PERSON> consults at Moonee Ponds Specialist Centre"
- "Available through major hospitals and stroke centres"
- "Mornington Specialist Centre provides comprehensive care"
```

## ✅ **CODE STYLING - Technical "center"**

### When to use "center":
- **CSS classes**: `text-center`, `items-center`, `justify-center`
- **Tailwind CSS**: All alignment utilities
- **HTML attributes**: `align="center"`, `text-align: center`
- **JavaScript/TypeScript**: Alignment properties, CSS-in-JS
- **CSS properties**: `transform-origin: center`, `background-position: center`
- **Configuration files**: Tailwind config, CSS frameworks

### Examples:
```tsx
// ✅ Correct - Code styling
<div className="text-center items-center justify-center">
  <h2 className="text-enhanced-heading">Moonee Ponds Specialist Centre</h2>
</div>

// ✅ Correct - CSS property
.wave-animation {
  transform-origin: center bottom;
}

// ✅ Correct - Tailwind config
container: {
  center: true
}
```

## ❌ **INCORRECT USAGE**

### Don't use "centre" in code styling:
```tsx
// ❌ Wrong - Code styling should use "center"
<div className="text-centre items-centre justify-centre">

// ❌ Wrong - CSS property should use "center"
.animation {
  transform-origin: centre bottom;
}
```

### Don't use "center" in Australian content:
```markdown
<!-- ❌ Wrong - Content should use Australian English "centre" -->
Dr. Aliashkevich consults at Moonee Ponds Specialist Center

<!-- ✅ Correct - Australian English -->
Dr. Aliashkevich consults at Moonee Ponds Specialist Centre
```

## 🔧 **Implementation Rules**

### For Developers:
1. **Always use "center" in CSS classes and technical code**
2. **Always use "centre" in content text and Australian business names**
3. **Check context**: Is this technical styling or user-facing content?
4. **When in doubt**: Technical = "center", Content = "centre"

### For Content Writers:
1. **Use Australian English "centre" for all content**
2. **Medical facilities, locations, and business names use "centre"**
3. **Don't change CSS class names or technical references**

## 📝 **Quick Reference**

| Context | Use | Example |
|---------|-----|---------|
| CSS Classes | `center` | `text-center`, `items-center` |
| Content Text | `centre` | "Specialist Centre", "Medical Centre" |
| CSS Properties | `center` | `transform-origin: center` |
| Business Names | `centre` | "Moonee Ponds Specialist Centre" |
| Tailwind Config | `center` | `center: true` |
| Location Names | `centre` | "Clocktower Centre" |

## ✅ **Validation Checklist**

- [ ] All CSS classes use "center"
- [ ] All content text uses Australian English "centre"
- [ ] Medical facility names use "centre"
- [ ] CSS properties use "center"
- [ ] Business and location names use "centre"
- [ ] Technical configuration uses "center"

This ensures consistency while maintaining both technical requirements and Australian English content standards.
