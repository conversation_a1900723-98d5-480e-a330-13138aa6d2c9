/**
 * Foundation Utilities Index
 * Centralised exports for all Foundation Phase utilities
 * Provides easy access to common patterns and utilities
 */

// Timeout Management
export { 
  useTimeout,
  useAnimationTimeout,
  useMultipleTimeouts
} from '@/hooks/useTimeout';

// Logging Utilities
export { 
  logger,
  logComponentError,
  logApiError,
  logUserAction,
  logNavigation,
  logPerformance,
  logTest,
  devLog
} from './logger';

// Translation Utilities
export {
  getTranslation,
  createSafeTranslationFunction,
  getTranslationWithFallbacks,
  validateTranslationStructure,
  createSectionTranslationGetter,
  getHeroTranslation,
  getNavTranslation,
  getFooterTranslation,
  createPageTranslationContext,
  defaultTranslationFallback
} from './translation-utils';

// Enhanced Error Handling
export {
  SimpleAppError,
  NetworkError,
  ComponentError,
  safeAsync,
  safeExecute,
  withRetry,
  createErrorBoundaryHandler,
  ErrorPatterns,
  ErrorRecovery,
  DevErrorHelpers,
  isSimpleAppError,
  isNetworkError,
  isComponentError
} from './enhanced-error-utils';

// Type exports
export type { LogLevel, LogContext, LogEntry } from './logger';
export type { 
  TranslationPath, 
  TranslationValue, 
  TranslationObject 
} from './translation-utils';
export type { ErrorContext } from './enhanced-error-utils';

/**
 * Common utility patterns for quick access
 */
export const FoundationUtils = {
  // Timeout management
  timeout: {
    useTimeout,
    useAnimationTimeout,
    useMultipleTimeouts,
  },
  
  // Logging
  log: {
    logger,
    component: logComponentError,
    api: logApiError,
    user: logUserAction,
    navigation: logNavigation,
    performance: logPerformance,
    test: logTest,
    dev: devLog,
  },
  
  // Translation
  translation: {
    get: getTranslation,
    safe: createSafeTranslationFunction,
    fallback: getTranslationWithFallbacks,
    validate: validateTranslationStructure,
    section: createSectionTranslationGetter,
    hero: getHeroTranslation,
    nav: getNavTranslation,
    footer: getFooterTranslation,
    page: createPageTranslationContext,
    default: defaultTranslationFallback,
  },
  
  // Error handling
  error: {
    SimpleAppError,
    NetworkError,
    ComponentError,
    safeAsync,
    safeExecute,
    withRetry,
    boundary: createErrorBoundaryHandler,
    patterns: ErrorPatterns,
    recovery: ErrorRecovery,
    dev: DevErrorHelpers,
    guards: {
      isSimpleAppError,
      isNetworkError,
      isComponentError,
    },
  },
};

export default FoundationUtils;
