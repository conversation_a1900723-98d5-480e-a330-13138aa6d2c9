import { ArrowRight } from 'lucide-react';
import React from 'react';
import { <PERSON> } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import type { WelcomeSectionProps } from '@/types/homepage';

/**
 * Welcome Section Component
 * Displays the welcome content with <PERSON> introduction
 * Preserves all original content and styling from Index.tsx lines 67-135
 */
const WelcomeSection: React.FC<WelcomeSectionProps> = ({
  deviceInfo,
  getTranslation
}) => {
  return (
    <section id="welcome" className="section-spacing section-background">
      <div className="section-container">
        <div className={cn(
          "items-center section-content-spacing",
          deviceInfo.isMobile
            ? "grid grid-cols-1 gap-8"
            : "grid grid-cols-1 lg:grid-cols-2 gap-16"
        )}>
          <div className="animate-fade-in [animation-delay:100ms] space-y-6">
            <span className={cn(
              "text-primary font-semibold uppercase tracking-wider text-sm border-b-2 border-primary/30 pb-2 inline-block",
              deviceInfo.isMobile ? "text-xs" : "text-sm"
            )}>
              {getTranslation('welcome.subtitle', 'Neurosurgical Excellence')}
            </span>
            <h2 className={cn(
              "text-headline",
              deviceInfo.isMobile
                ? "text-3xl"
                : "text-4xl md:text-5xl"
            )}>
              {getTranslation('welcome.title', 'Advanced Neurosurgical Care')}
            </h2>
            <p className={cn(
              "lead",
              deviceInfo.isMobile ? "text-base" : "text-lg"
            )}>
              {getTranslation('welcome.description1', 'Comprehensive neurosurgical services with cutting-edge technology.')}
            </p>
            <p className={cn(
              "text-foreground/80 text-base leading-relaxed",
              deviceInfo.isMobile ? "text-sm" : "text-base"
            )}>
              <a
                href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/"
                className={cn(
                  "text-primary font-semibold touch-feedback transition-colors duration-200",
                  deviceInfo.isMobile ? "" : "hover:text-primary/80 hover:underline"
                )}
                target="_blank"
                rel="noopener noreferrer"
              >
                Dr Ales Aliashkevich
              </a> {getTranslation('welcome.description2', 'provides expert neurosurgical care.')}
            </p>
            <div className="pt-4">
              <Button asChild className="btn-primary">
                <Link to="/expertise" className="inline-flex items-center">
                  {getTranslation('welcome.learnMore', 'Learn More')}
                  <ArrowRight className={cn(
                    "ml-2 transition-transform duration-200",
                    deviceInfo.isMobile ? "h-5 w-5" : "h-4 w-4"
                  )} />
                </Link>
              </Button>
            </div>
          </div>

          <div className={cn(
            "relative rounded-xl overflow-hidden shadow-2xl medical-card-elevated",
            deviceInfo.isMobile && "order-first"
          )}>
            <SafeImage
              src="/images/Ales-Aliashkevich-operating-theatre-spine-brain-image-guided-neurosurgery.jpg"
              alt="Dr Ales Aliashkevich - Neurosurgeon and Spine Surgeon"
              className="w-full h-auto object-cover aspect-[4/3]"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

WelcomeSection.displayName = 'WelcomeSection';

export default WelcomeSection;
