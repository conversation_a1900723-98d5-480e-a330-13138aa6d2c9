import { ArrowR<PERSON> } from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import type { WelcomeSectionProps } from '@/types/homepage';

/**
 * Welcome Section Component
 * Displays the welcome content with <PERSON> introduction
 * Preserves all original content and styling from Index.tsx lines 67-135
 */
const WelcomeSection: React.FC<WelcomeSectionProps> = ({
  deviceInfo,
  getTranslation
}) => {
  return (
    <section id="welcome" className={deviceInfo.isMobile ? "mobile-section" : "section"}>
      <div className={deviceInfo.isMobile ? "mobile-container" : "container"}>
        <div className={cn(
          "items-center",
          deviceInfo.isMobile
            ? "grid grid-cols-1 gap-mobile-lg"
            : "grid grid-cols-1 lg:grid-cols-2 gap-12"
        )}>
          <div className="animate-fade-in [animation-delay:100ms]">
            <span className={cn(
              "text-primary font-medium uppercase tracking-wider",
              deviceInfo.isMobile ? "mobile-text" : "text-sm"
            )}>
              {getTranslation('welcome.subtitle', 'Neurosurgical Excellence')}
            </span>
            <h2 className={cn(
              "font-bold mt-2 mb-mobile-lg",
              deviceInfo.isMobile
                ? "mobile-3xl"
                : "text-3xl md:text-4xl mb-6"
            )}>
              {getTranslation('welcome.title', 'Advanced Neurosurgical Care')}
            </h2>
            <p className={cn(
              "text-enhanced-body mb-mobile-lg",
              deviceInfo.isMobile ? "mobile-text" : "mb-6"
            )}>
              {getTranslation('welcome.description1', 'Comprehensive neurosurgical services with cutting-edge technology.')}
            </p>
            <p className={cn(
              "text-enhanced-body mb-mobile-lg",
              deviceInfo.isMobile ? "mobile-text" : "mb-8"
            )}>
              <a
                href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/"
                className={cn(
                  "text-primary touch-feedback",
                  deviceInfo.isMobile ? "" : "hover:underline"
                )}
                target="_blank"
                rel="noopener noreferrer"
              >
                Dr Ales Aliashkevich
              </a> {getTranslation('welcome.description2', 'provides expert neurosurgical care.')}
            </p>
            <Button asChild className="btn-primary">
              <Link to="/expertise">
                {getTranslation('welcome.learnMore', 'Learn More')} <ArrowRight className={cn(
                  "ml-2",
                  deviceInfo.isMobile ? "h-5 w-5" : "h-4 w-4"
                )} />
              </Link>
            </Button>
          </div>

          <div className={cn(
            "relative rounded-lg overflow-hidden shadow-xl",
            deviceInfo.isMobile && "order-first"
          )}>
            <SafeImage
              src="/images/Ales-Aliashkevich-operating-theatre-spine-brain-image-guided-neurosurgery.jpg"
              alt="Dr Ales Aliashkevich - Neurosurgeon and Spine Surgeon"
              className="w-full h-auto object-cover"
              fallbackSrc="/images/medical-consulting.jpg"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

WelcomeSection.displayName = 'WelcomeSection';

export default WelcomeSection;
