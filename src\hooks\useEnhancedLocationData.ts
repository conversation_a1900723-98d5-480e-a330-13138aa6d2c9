import { useCallback, useEffect, useMemo, useState } from 'react';

import { LocationSpecialSections } from '@/components/locations/GenericLocationTemplate';
import { getLocationConfig, LocationConfig } from '@/lib/location-config';
import { logger } from '@/lib/logger';
import { CompleteLocationData } from '@/types/location';

/**
 * Enhanced Location Data Injection Hook
 * Dynamically loads location data based on slug with comprehensive error handling
 * Provides loading states, validation, and retry mechanisms
 */

interface LocationDataState {
  data: CompleteLocationData | null;
  config: LocationConfig | null;
  specialSections: LocationSpecialSections | undefined;
  isLoading: boolean;
  error: string | null;
}

export const useEnhancedLocationData = (locationSlug: string) => {
  const [state, setState] = useState<LocationDataState>({
    data: null,
    config: null,
    specialSections: undefined,
    isLoading: true,
    error: null,
  });

  // Get location configuration
  const config = useMemo(() => {
    return getLocationConfig(locationSlug);
  }, [locationSlug]);

  // Dynamic data loader with comprehensive error handling
  const loadLocationData = useCallback(async (slug: string) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Dynamic import based on location slug
      let locationData: CompleteLocationData;
      let specialSections: LocationSpecialSections | undefined = undefined;

      switch (slug) {
        case 'sunbury': {
          const sunburyModule = await import('@/data/locations/sunburyData');
          locationData = sunburyModule.sunburyLocationData;
          specialSections = sunburyModule.sunburySpecialSections;
          break;
        }

        case 'heidelberg': {
          const heidelbergModule = await import('@/data/locations/heidelbergData');
          locationData = heidelbergModule.heidelbergLocationData;
          specialSections = heidelbergModule.heidelbergSpecialSections;
          break;
        }

        case 'frankston': {
          const frankstonModule = await import('@/data/locations/frankstonData');
          locationData = frankstonModule.frankstonLocationData;
          specialSections = frankstonModule.frankstonSpecialSections || undefined;
          break;
        }

        case 'bundoora': {
          const bundooraModule = await import('@/data/locations/bundooraData');
          locationData = bundooraModule.bundooraLocationData;
          specialSections = bundooraModule.bundooraSpecialSections || null;
          break;
        }

        case 'dandenong': {
          const dandenongModule = await import('@/data/locations/dandenongData');
          locationData = dandenongModule.dandenongLocationData;
          specialSections = dandenongModule.dandenongSpecialSections || null;
          break;
        }

        case 'langwarrin': {
          const langwarrinModule = await import('@/data/locations/langwarrinData');
          locationData = langwarrinModule.langwarrinLocationData;
          specialSections = langwarrinModule.langwarrinSpecialSections || null;
          break;
        }

        case 'moonee-ponds': {
          const mooneePondsModule = await import('@/data/locations/mooneePondsData');
          locationData = mooneePondsModule.mooneePondsLocationData;
          specialSections = mooneePondsModule.mooneePondsSpecialSections || undefined;
          break;
        }

        case 'mornington': {
          const morningtonModule = await import('@/data/locations/morningtonData');
          locationData = morningtonModule.morningtonLocationData;
          specialSections = morningtonModule.morningtonSpecialSections || undefined;
          break;
        }

        case 'surrey-hills': {
          const surreyHillsModule = await import('@/data/locations/surreyHillsData');
          locationData = surreyHillsModule.surreyHillsLocationData;
          specialSections = surreyHillsModule.surreyHillsSpecialSections || undefined;
          break;
        }

        case 'wantirna': {
          const wantirnaModule = await import('@/data/locations/wantirnaData');
          locationData = wantirnaModule.wantirnaLocationData;
          specialSections = wantirnaModule.wantirnaSpecialSections || undefined;
          break;
        }

        case 'werribee': {
          const werribeeModule = await import('@/data/locations/werribeeData');
          locationData = werribeeModule.werribeeLocationData;
          specialSections = werribeeModule.werribeeSpecialSections || undefined;
          break;
        }

        default:
          throw new Error(`Unknown location: ${slug}`);
      }

      setState({
        data: locationData,
        config,
        specialSections,
        isLoading: false,
        error: null,
      });

      logger.info(`Location data loaded successfully: ${slug}`, {
        location: slug,
        hasSpecialSections: !!specialSections,
        dataSize: JSON.stringify(locationData).length,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load location data';
      
      setState({
        data: null,
        config,
        specialSections: undefined,
        isLoading: false,
        error: errorMessage,
      });

      logger.error(`Failed to load location data: ${slug}`, {
        location: slug,
        error: errorMessage,
      });
    }
  }, [config]);

  // Load data when slug changes
  useEffect(() => {
    if (locationSlug && config) {
      loadLocationData(locationSlug);
    } else if (locationSlug && !config) {
      setState({
        data: null,
        config: null,
        specialSections: undefined,
        isLoading: false,
        error: `Location configuration not found: ${locationSlug}`,
      });
    }
  }, [locationSlug, config, loadLocationData]);

  // Retry function
  const retry = useCallback(() => {
    if (locationSlug) {
      loadLocationData(locationSlug);
    }
  }, [locationSlug, loadLocationData]);

  // Validation function
  const validateLocationData = useCallback((data: CompleteLocationData | null): boolean => {
    if (!data) return false;

    // Check required sections
    const requiredSections = ['hero', 'contact', 'map', 'facilities', 'nearbyAmenities', 'nearbyHospitals', 'cta'];
    
    for (const section of requiredSections) {
      if (!data[section as keyof CompleteLocationData]) {
        logger.warn(`Missing required section: ${section}`, { location: locationSlug });
        return false;
      }
    }

    return true;
  }, [locationSlug]);

  // Computed values
  const isValid = useMemo(() => {
    return validateLocationData(state.data);
  }, [state.data, validateLocationData]);

  const isReady = useMemo(() => {
    return !state.isLoading && !state.error && isValid;
  }, [state.isLoading, state.error, isValid]);

  return {
    // Data
    locationData: state.data,
    config: state.config,
    specialSections: state.specialSections,
    
    // State
    isLoading: state.isLoading,
    error: state.error,
    isValid,
    isReady,
    
    // Actions
    retry,
  };
};

/**
 * Hook for getting location metadata and configuration
 */
export const useLocationMetadata = (locationSlug: string) => {
  const config = useMemo(() => {
    return getLocationConfig(locationSlug);
  }, [locationSlug]);

  const hasSection = useCallback((sectionName: string) => {
    if (!config) return false;
    return config[sectionName as keyof LocationConfig] === true;
  }, [config]);

  return {
    config,
    seoData: config?.seoData || null,
    layoutVariant: config?.layoutVariant || 'standard',
    hasSection,
    isValidLocation: !!config,
  };
};

/**
 * Hook for managing location page state and analytics
 */
export const useLocationPageState = (locationSlug: string) => {
  const [visitCount, setVisitCount] = useState(0);
  const [lastVisited, setLastVisited] = useState<Date | null>(null);
  const [interactionCount, setInteractionCount] = useState(0);
  const [hasTrackedVisit, setHasTrackedVisit] = useState(false);

  useEffect(() => {
    // Only track visit once per location slug change
    if (!hasTrackedVisit) {
      setVisitCount(prev => prev + 1);
      setLastVisited(new Date());
      setHasTrackedVisit(true);

      logger.info(`Location page visited: ${locationSlug}`, {
        location: locationSlug,
        visitCount: visitCount + 1,
        timestamp: new Date().toISOString(),
      });
    }
  }, [locationSlug, hasTrackedVisit, visitCount]);

  // Reset tracking when location changes
  useEffect(() => {
    setHasTrackedVisit(false);
  }, [locationSlug]);

  const trackInteraction = useCallback((action: string, element: string) => {
    setInteractionCount(prev => {
      const newCount = prev + 1;

      logger.userAction(action, element, {
        page: 'location',
        location: locationSlug,
        interactionCount: newCount,
      });

      return newCount;
    });
  }, [locationSlug]);

  return {
    visitCount,
    lastVisited,
    interactionCount,
    trackInteraction,
  };
};

export default useEnhancedLocationData;
