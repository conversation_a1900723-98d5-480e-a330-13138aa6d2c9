import React, { Component, ErrorInfo, ReactNode } from 'react';

import { Button } from '@/components/ui/button';
import { logger } from '@/lib/logger';

/**
 * Standard Error Boundary Component
 * Replaces duplicate error boundary patterns found throughout the codebase
 * Provides consistent error handling with logging and recovery options
 */

export interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorId: string;
}

export interface StandardErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showErrorDetails?: boolean;
  enableRetry?: boolean;
  componentName?: string;
  className?: string;
}

export class StandardErrorBoundary extends Component<
  StandardErrorBoundaryProps,
  ErrorBoundaryState
> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: StandardErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError, componentName = 'Unknown' } = this.props;
    
    // Log the error
    logger.componentError(componentName, error, {
      errorInfo: errorInfo.componentStack,
      retryCount: this.retryCount,
      errorId: this.state.errorId,
    });

    // Error info is logged above but not stored in state since it's not used for rendering

    // Call custom error handler if provided
    if (onError) {
      onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      logger.info(`Retrying component render (attempt ${this.retryCount})`, {
        component: this.props.componentName,
        errorId: this.state.errorId,
      });
      
      this.setState({
        hasError: false,
        error: null,
        errorId: '',
      });
    }
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      const { fallback, showErrorDetails = false, enableRetry = true, className } = this.props;

      // Use custom fallback if provided
      if (fallback) {
        return fallback;
      }

      // Default error UI
      return (
        <div className={`error-boundary p-6 text-center ${className || ''}`} role="alert">
          <div className="max-w-md mx-auto">
            <div className="mb-4">
              <svg
                className="w-16 h-16 mx-auto text-foreground mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
              <h2 className="text-xl font-semibold text-muted-foreground mb-2">
                Something went wrong
              </h2>
              <p className="text-muted-foreground mb-4">
                We're sorry, but something unexpected happened. Please try again.
              </p>
            </div>

            {showErrorDetails && this.state.error && (
              <details className="mb-4 text-left">
                <summary className="cursor-pointer text-sm text-muted-foreground mb-2">
                  Error Details
                </summary>
                <div className="bg-muted p-3 rounded text-xs font-mono">
                  <p className="mb-2">
                    <strong>Error:</strong> {this.state.error.message}
                  </p>
                  <p className="mb-2">
                    <strong>Error ID:</strong> {this.state.errorId}
                  </p>
                  {this.state.error.stack && (
                    <p>
                      <strong>Stack:</strong>
                      <pre className="mt-1 whitespace-pre-wrap">
                        {this.state.error.stack}
                      </pre>
                    </p>
                  )}
                </div>
              </details>
            )}

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {enableRetry && this.retryCount < this.maxRetries && (
                <Button onClick={this.handleRetry} variant="default">
                  Try Again ({this.maxRetries - this.retryCount} attempts left)
                </Button>
              )}
              <Button onClick={this.handleReload} variant="outline">
                Reload Page
              </Button>
            </div>

            {this.retryCount >= this.maxRetries && (
              <p className="mt-4 text-sm text-muted-foreground">
                Maximum retry attempts reached. Please reload the page or contact support.
              </p>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component for wrapping components with error boundary
 */
export const withErrorBoundary = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  errorBoundaryProps?: Omit<StandardErrorBoundaryProps, 'children'>
) => {
  const WithErrorBoundaryComponent = (props: P) => (
    <StandardErrorBoundary
      componentName={WrappedComponent.displayName || WrappedComponent.name}
      {...errorBoundaryProps}
    >
      <WrappedComponent {...props} />
    </StandardErrorBoundary>
  );

  WithErrorBoundaryComponent.displayName = `withErrorBoundary(${
    WrappedComponent.displayName || WrappedComponent.name
  })`;

  return WithErrorBoundaryComponent;
};

/**
 * Hook for error handling in functional components
 */
export const useErrorHandler = (componentName?: string) => {
  const handleError = React.useCallback(
    (error: Error, context?: Record<string, unknown>) => {
      logger.componentError(componentName || 'Unknown', error, context);
      
      // In development, re-throw to trigger error boundary
      if (import.meta.env.DEV) {
        throw error;
      }
    },
    [componentName]
  );

  return { handleError };
};

/**
 * Simple error fallback component for common use cases
 */
export const SimpleErrorFallback: React.FC<{
  error?: Error;
  resetError?: () => void;
  message?: string;
}> = ({ error, resetError, message = 'Something went wrong' }) => (
  <div className="text-center p-6" role="alert">
    <p className="text-enhanced-body mb-4">{message}</p>
    {resetError && (
      <Button onClick={resetError} variant="outline" size="sm">
        Try again
      </Button>
    )}
    {import.meta.env.DEV && error && (
      <details className="mt-4 text-left">
        <summary className="cursor-pointer text-sm text-enhanced-muted">
          Error Details (Development)
        </summary>
        <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto">
          {error.stack}
        </pre>
      </details>
    )}
  </div>
);

export default StandardErrorBoundary;
