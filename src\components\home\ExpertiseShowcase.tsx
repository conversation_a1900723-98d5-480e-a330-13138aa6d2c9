import React from 'react';
import { Link } from 'react-router-dom';

import {
  SectionHeader,
  GridLayout,
  ResponsiveSection,
  ResponsiveContainer,
  CardLayout
} from '@/components/shared';
import {
  BrainConditionsIcon,
  SpinalProblemsIcon,
  NerveProblemsIcon,
  MedicoLegalIcon
} from '@/components/shared/MedicalIcons';
import type { ExpertiseShowcaseProps } from '@/types/homepage';

/**
 * Expertise Showcase Component
 * Displays the specialised neurosurgical procedures section
 * Preserves all original content and styling from Index.tsx lines 439-652
 * Now uses Foundation Phase shared components for consistency
 */
const ExpertiseShowcase: React.FC<ExpertiseShowcaseProps> = ({ expertise }) => {
  return (
    <ResponsiveSection spacing="md">
      <ResponsiveContainer size="lg">
        <SectionHeader
          subtitle="Featured Procedures"
          title="Specialised Neurosurgical Procedures"
          description="Comprehensive range of advanced neurosurgical procedures."
          centered={true}
        />

        <GridLayout columns={4} gap="lg">
          {expertise.map((item) => (
            <ExpertiseCard
              key={item.id}
              expertise={item}
            />
          ))}
        </GridLayout>
      </ResponsiveContainer>
    </ResponsiveSection>
  );
};

/**
 * Individual Expertise Card Component
 * Renders each expertise area with its icon, title, description, and link
 */
interface ExpertiseCardProps {
  expertise: {
    id: string;
    title: string;
    description: string;
    icon: string;
    link: string;
    gradientColors: string;
  };
}

const ExpertiseCard: React.FC<ExpertiseCardProps> = ({ expertise }) => {
  const renderIcon = (iconType: string) => {
    const iconClassName = "w-10 h-10 text-primary drop-shadow-sm";

    switch (iconType) {
      case 'brain':
        return <BrainConditionsIcon className={iconClassName} />;
      
      case 'spine':
        return <SpinalProblemsIcon className={iconClassName} />;

      case 'nerve':
        return <NerveProblemsIcon className={iconClassName} />;

      case 'legal':
        return <MedicoLegalIcon className={iconClassName} />;
      
      default:
        return null;
    }
  };

  return (
    <CardLayout variant="elevated" padding="md" interactive={true} className="text-center">
      <div className="flex justify-center mb-4">
        <div className={`w-20 h-20 rounded-2xl bg-gradient-to-br ${expertise.gradientColors} flex items-center justify-center shadow-lg backdrop-blur-sm border border-primary/10`}>
          {renderIcon(expertise.icon)}
        </div>
      </div>
      <h3 className="text-xl font-semibold mb-3 text-enhanced-subheading">{expertise.title}</h3>
      <p className="text-enhanced-body">
        {expertise.description}
      </p>
      <div className="mt-4">
        <Link to={expertise.link} className="text-primary hover:underline font-medium">
          Learn More
        </Link>
      </div>
    </CardLayout>
  );
};

ExpertiseShowcase.displayName = 'ExpertiseShowcase';

export default ExpertiseShowcase;
