import React from 'react';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';

interface InfoSection {
  title: string;
  content: string[];
  items?: string[];
}

interface AppointmentInformationProps {
  title: string;
  sections: InfoSection[];
}

const AppointmentInformation: React.FC<AppointmentInformationProps> = ({
  title,
  sections
}) => {
  return (
    <div>
      <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{title}</h2>
      <div className="space-y-6">
        {sections.map((section, index) => (
          <Card key={index} className="shadow-md">
            <CardHeader>
              <CardTitle className="text-xl text-primary">
                {section.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {section.content.map((paragraph, pIndex) => (
                <p key={pIndex} className="text-muted-foreground mb-4">
                  {paragraph}
                </p>
              ))}
              {section.items && (
                <ul className="list-disc list-inside text-muted-foreground space-y-2 mb-4">
                  {section.items.map((item, itemIndex) => (
                    <li key={itemIndex}>{item}</li>
                  ))}
                </ul>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default AppointmentInformation;
