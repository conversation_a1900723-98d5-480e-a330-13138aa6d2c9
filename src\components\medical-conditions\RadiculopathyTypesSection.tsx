import React from 'react';

import { MedicalConditionTabs, TabContent } from './MedicalConditionTabs';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const RadiculopathyTypesSection: React.FC = () => {
  const typesContent = (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Cervical Radiculopathy</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-3">
            Compression of nerve roots in the neck region (C1-C8). Most commonly affects
            C6 and C7 nerve roots, causing arm and hand symptoms.
          </p>
          <div className="space-y-2">
            <h5 className="font-semibold text-sm">Common Symptoms:</h5>
            <ul className="text-sm space-y-1">
              <li>• Neck pain radiating to shoulder/arm</li>
              <li>• Numbness in fingers</li>
              <li>• Weakness in specific muscle groups</li>
              <li>• Reduced reflexes</li>
            </ul>
          </div>
          <Badge variant="secondary" className="mt-3">Most Common</Badge>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Thoracic Radiculopathy</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-3">
            Compression of nerve roots in the mid-back region (T1-T12). Less common
            but can cause chest wall pain and intercostal neuralgia.
          </p>
          <div className="space-y-2">
            <h5 className="font-semibold text-sm">Common Symptoms:</h5>
            <ul className="text-sm space-y-1">
              <li>• Band-like chest pain</li>
              <li>• Rib pain (intercostal neuralgia)</li>
              <li>• Abdominal wall pain</li>
              <li>• Sensory changes in trunk</li>
            </ul>
          </div>
          <Badge variant="outline" className="mt-3">Least Common</Badge>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Lumbar Radiculopathy</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-3">
            Compression of nerve roots in the lower back (L1-S1). Often causes
            sciatica when L4, L5, or S1 nerve roots are affected.
          </p>
          <div className="space-y-2">
            <h5 className="font-semibold text-sm">Common Symptoms:</h5>
            <ul className="text-sm space-y-1">
              <li>• Lower back pain to leg</li>
              <li>• Sciatica (L4-S1 roots)</li>
              <li>• Foot drop (L5 root)</li>
              <li>• Weakness in leg muscles</li>
            </ul>
          </div>
          <Badge variant="secondary" className="mt-3">Second Most Common</Badge>
        </CardContent>
      </Card>
    </div>
  );

  const anatomyContent = (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div>
        <h3 className="text-xl font-bold mb-4">Nerve Root Structure</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-semibold text-primary mb-2">Dorsal Root (Sensory)</h4>
            <p className="text-sm text-muted-foreground">
              Carries sensory information from the body to the spinal cord. Contains the
              dorsal root ganglion where sensory nerve cell bodies are located.
            </p>
          </div>
          <div>
            <h4 className="font-semibold text-primary mb-2">Ventral Root (Motor)</h4>
            <p className="text-sm text-muted-foreground">
              Carries motor commands from the spinal cord to muscles. Contains axons
              of motor neurons whose cell bodies are in the spinal cord.
            </p>
          </div>
          <div>
            <h4 className="font-semibold text-primary mb-2">Mixed Spinal Nerve</h4>
            <p className="text-sm text-muted-foreground">
              Formed by the joining of dorsal and ventral roots. Contains both sensory
              and motor fibers that innervate specific body regions.
            </p>
          </div>
          <div>
            <h4 className="font-semibold text-primary mb-2">Neural Foramen</h4>
            <p className="text-sm text-muted-foreground">
              Opening between vertebrae where nerve roots exit the spinal canal.
              Common site of compression in radiculopathy.
            </p>
          </div>
        </div>
      </div>
      <div className="relative">
        <SafeImage
          src="/images/spine-anatomy/nerve-root-anatomy.jpg"
          alt="Nerve root anatomy illustration"
          className="w-full h-auto rounded-lg"
          fallbackSrc="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
        />
      </div>
    </div>
  );

  const dermatomesContent = (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-center">Dermatome Distribution Patterns</h3>
      <p className="text-center text-muted-foreground">
        Each nerve root supplies sensation to specific skin areas (dermatomes) and muscles (myotomes)
      </p>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <h4 className="font-semibold mb-4">Cervical Dermatomes (C5-C8)</h4>
          <div className="space-y-3">
            <div className="p-3 bg-muted rounded-lg">
              <h5 className="font-semibold text-sm">C5 (Deltoid)</h5>
              <p className="text-sm text-muted-foreground">Lateral shoulder, upper arm</p>
            </div>
            <div className="p-3 bg-muted rounded-lg">
              <h5 className="font-semibold text-sm">C6 (Thumb)</h5>
              <p className="text-sm text-muted-foreground">Lateral forearm, thumb, index finger</p>
            </div>
            <div className="p-3 bg-muted rounded-lg">
              <h5 className="font-semibold text-sm">C7 (Middle Finger)</h5>
              <p className="text-sm text-muted-foreground">Middle finger, palm centre</p>
            </div>
            <div className="p-3 bg-muted rounded-lg">
              <h5 className="font-semibold text-sm">C8 (Little Finger)</h5>
              <p className="text-sm text-muted-foreground">Medial forearm, little finger</p>
            </div>
          </div>
        </div>

        <div>
          <h4 className="font-semibold mb-4">Lumbar Dermatomes (L1-S1)</h4>
          <div className="space-y-3">
            <div className="p-3 bg-muted rounded-lg">
              <h5 className="font-semibold text-sm">L4 (Medial Leg)</h5>
              <p className="text-sm text-muted-foreground">Anterior thigh, medial leg, big toe</p>
            </div>
            <div className="p-3 bg-muted rounded-lg">
              <h5 className="font-semibold text-sm">L5 (Dorsal Foot)</h5>
              <p className="text-sm text-muted-foreground">Lateral leg, dorsal foot, first 3 toes</p>
            </div>
            <div className="p-3 bg-muted rounded-lg">
              <h5 className="font-semibold text-sm">S1 (Lateral Foot)</h5>
              <p className="text-sm text-muted-foreground">Posterior leg, lateral foot, little toe</p>
            </div>
          </div>
        </div>
      </div>

      <div className="text-center">
        <SafeImage
          src="/images/spine-anatomy/dermatome-map.jpg"
          alt="Complete dermatome distribution map"
          className="w-full max-w-lg mx-auto h-auto rounded-lg"
          fallbackSrc="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
        />
      </div>
    </div>
  );

  const tabs: TabContent[] = [
    {
      id: 'types',
      label: 'Types by Location',
      content: typesContent
    },
    {
      id: 'anatomy',
      label: 'Nerve Root Anatomy',
      content: anatomyContent
    },
    {
      id: 'dermatomes',
      label: 'Dermatome Patterns',
      content: dermatomesContent
    }
  ];

  return (
    <MedicalConditionTabs
      title="Types of Radiculopathy & Nerve Root Anatomy"
      tabs={tabs}
      defaultTab="types"
    />
  );
};

export default RadiculopathyTypesSection;
