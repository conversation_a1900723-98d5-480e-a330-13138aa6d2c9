export interface AppointmentInfoCard {
  id: string;
  title: string;
  content: {
    descriptions?: string[];
    items?: string[];
  };
}

export const appointmentInfoCards: AppointmentInfoCard[] = [
  {
    id: 'referral-requirements',
    title: 'Referral Requirements',
    content: {
      descriptions: [
        'A referral from your GP or specialist is required for all consultations.',
        'Please ensure your referral is current and includes relevant medical history.'
      ]
    }
  },
  {
    id: 'what-to-bring',
    title: 'What to Bring',
    content: {
      items: [
        'Current referral letter',
        'Medicare card and private health insurance details',
        'List of current medications',
        'Previous imaging studies (MRI, CT scans, X-rays)',
        'Previous medical reports'
      ],
      descriptions: [
        'Having these items ready will help ensure an efficient consultation.'
      ]
    }
  },
  {
    id: 'consultation-process',
    title: 'Consultation Process',
    content: {
      descriptions: [
        'Dr<PERSON> will review your medical history, examine you, and discuss your symptoms.',
        'Treatment options and next steps will be explained in detail.'
      ]
    }
  },
  {
    id: 'telehealth',
    title: 'Telehealth Consultations',
    content: {
      descriptions: [
        'Telehealth consultations are available for follow-up appointments and some initial consultations.',
        'Please discuss telehealth options when booking your appointment.'
      ]
    }
  }
];

export const getAppointmentInfoCardById = (id: string): AppointmentInfoCard | undefined => {
  return appointmentInfoCards.find(card => card.id === id);
};
