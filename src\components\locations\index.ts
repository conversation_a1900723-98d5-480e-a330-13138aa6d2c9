export { default as LocationHero } from './LocationHero';
export { default as LocationContactInfo } from './LocationContactInfo';
export { default as LocationMap } from './LocationMap';
export { default as LocationFacilities } from './LocationFacilities';
export { default as LocationCTA } from './LocationCTA';
export { default as LocationGallery } from './LocationGallery';
export { default as LocationAmenities } from './LocationAmenities';
export { default as NearbyAmenities } from './NearbyAmenities';
export { default as ConditionsTreated } from './ConditionsTreated';
export { default as NearbyHospitals } from './NearbyHospitals';
export { default as OtherConsultingLocations } from './OtherConsultingLocations';
export { default as LocationPricingContact } from './LocationPricingContact';
export { default as TherapeuticInterventions } from './TherapeuticInterventions';
export { default as InsuranceAndFunding } from './InsuranceAndFunding';
export { default as PatientsPrivacy } from './PatientsPrivacy';

// New generic template system
export { default as GenericLocationTemplate } from './GenericLocationTemplate';
export { default as LocationPageWrapper } from './LocationPageWrapper';
