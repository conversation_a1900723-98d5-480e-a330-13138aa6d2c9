import { Search, HelpCircle, Clock, Star } from 'lucide-react';
import React from 'react';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface FAQHeroProps {
  title: string;
  subtitle: string;
  description: string;
  imageSrc: string;
  imageAlt: string;
  totalQuestions?: number;
  categories?: number;
}

const FAQHero: React.FC<FAQHeroProps> = ({
  title,
  subtitle,
  description,
  imageSrc,
  imageAlt,
  totalQuestions = 33,
  categories = 6
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <div>
      {/* Enhanced Hero Section */}
      <div className={cn(
        "relative overflow-hidden",
        deviceInfo.isMobile ? "py-mobile-xl" : "py-24"
      )}>
        {/* Background with theme-aware styling */}
        <div className="absolute inset-0 section-background-alt" />
        <div className="absolute inset-0 opacity-20 bg-muted/50" />

        <div className={cn(
          "relative z-10",
          deviceInfo.isMobile ? "mobile-container" : "container"
        )}>
          <div className={cn(
            "text-center mx-auto",
            deviceInfo.isMobile ? "max-w-full" : "max-w-4xl"
          )}>
            {/* Premium Badge */}
            <div className="flex justify-center mb-8">
              <Badge
                variant="secondary"
                className="px-6 py-3 text-sm font-semibold bg-gradient-to-r from-primary/10 to-info-light/10 text-primary border-primary/20 shadow-lg shadow-primary/10 backdrop-blur-sm"
              >
                <HelpCircle className="h-5 w-5 mr-3" />
                Comprehensive FAQ Guide
              </Badge>
            </div>

            {/* Premium Main Title */}
            <h1 className={cn(
              "font-extrabold bg-gradient-to-r from-muted via-muted to-muted dark:from-muted dark:via-muted dark:to-muted bg-clip-text text-transparent leading-tight",
              deviceInfo.isMobile
                ? "text-4xl mb-6"
                : "text-6xl lg:text-7xl mb-8"
            )}>
              {title.split(' ').slice(0, -1).join(' ')}{' '}
              <span className="block bg-gradient-to-r from-primary via-info-light to-info-light bg-clip-text text-transparent">
                {title.split(' ').slice(-1)[0]}
              </span>
            </h1>

            {/* Enhanced Subtitle */}
            <p className={cn(
              "text-muted-foreground dark:text-muted-foreground leading-relaxed font-medium max-w-3xl mx-auto",
              deviceInfo.isMobile ? "text-lg mb-8" : "text-xl mb-10"
            )}>
              {subtitle}
              <span className="text-primary font-semibold"> Contact our office directly.</span>
            </p>

            {/* Premium Quick Stats */}
            <div className={cn(
              "flex justify-center mb-10",
              deviceInfo.isMobile ? "gap-4 flex-wrap" : "gap-8"
            )}>
              <div className="text-center group">
                <div className="p-5 bg-gradient-to-br from-white/80 to-muted/80 dark:from-muted/80 dark:to-muted/80 rounded-2xl border border-border/60 dark:border-border/60 shadow-lg backdrop-blur-sm group-hover:scale-105 transition-transform duration-300">
                  <div className="text-enhanced-heading text-3xl font-bold bg-gradient-to-r from-primary to-info-light bg-clip-text text-transparent">{totalQuestions}</div>
                  <div className="text-sm text-muted-foreground dark:text-muted-foreground font-medium">Questions</div>
                </div>
              </div>
              <div className="text-center group">
                <div className="p-5 bg-gradient-to-br from-white/80 to-muted/80 dark:from-muted/80 dark:to-muted/80 rounded-2xl border border-border/60 dark:border-border/60 shadow-lg backdrop-blur-sm group-hover:scale-105 transition-transform duration-300">
                  <div className="text-enhanced-heading text-3xl font-bold bg-gradient-to-r from-primary to-info-light bg-clip-text text-transparent">{categories}</div>
                  <div className="text-sm text-muted-foreground dark:text-muted-foreground font-medium">Categories</div>
                </div>
              </div>
              <div className="text-center group">
                <div className="p-5 bg-gradient-to-br from-white/80 to-muted/80 dark:from-muted/80 dark:to-muted/80 rounded-2xl border border-border/60 dark:border-border/60 shadow-lg backdrop-blur-sm group-hover:scale-105 transition-transform duration-300">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 fill-yellow-400 text-info" />
                    ))}
                  </div>
                  <div className="text-sm text-muted-foreground dark:text-muted-foreground font-medium">Expert Answers</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Introduction Section */}
      <section className={cn(
        "relative",
        deviceInfo.isMobile ? "py-8" : "py-16"
      )}>
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8 items-center">
              {/* Content */}
              <div className="space-y-6">
                <div className="prose prose-slate dark:prose-invert max-w-none">
                  <p className="text-lg leading-relaxed text-muted-foreground dark:text-muted-foreground">
                    {description}
                  </p>
                </div>

                {/* Quick Navigation Hints */}
                <div className="bg-muted dark:bg-muted rounded-xl p-6 border">
                  <h3 className="font-semibold text-muted-foreground dark:text-muted-foreground mb-4 flex items-center gap-2">
                    <Search className="h-5 w-5 text-primary" />
                    How to Use This FAQ
                  </h3>
                  <ul className="space-y-2 text-sm text-muted-foreground dark:text-muted-foreground">
                    <li className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full" />
                      Browse categories in the sidebar
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full" />
                      Use the search box to find specific topics
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full" />
                      Click questions to expand detailed answers
                    </li>
                  </ul>
                </div>
              </div>

              {/* Enhanced Featured Image */}
              <div className="relative">
                <div className="relative rounded-2xl overflow-hidden shadow-xl">
                  <SafeImage
                    src={imageSrc}
                    alt={imageAlt}
                    className="w-full h-auto max-h-[400px] object-cover"
                    fallbackSrc="/images/medical-consulting.jpg"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />

                  {/* Floating badge */}
                  <div className="absolute top-4 right-4">
                    <Badge className="badge-info shadow-lg">
                      <Clock className="h-3 w-3 mr-1" />
                      Expert Guidance
                    </Badge>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full blur-xl" />
                <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-info/10 rounded-full blur-xl" />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

FAQHero.displayName = 'FAQHero';

export default FAQHero;
