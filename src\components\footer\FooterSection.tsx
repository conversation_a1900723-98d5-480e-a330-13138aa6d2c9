import React from 'react';
import { Link } from 'react-router-dom';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { FooterSection as FooterSectionType } from '@/data/footer/footerData';
import { cn } from '@/lib/utils';

interface FooterSectionProps {
  section: FooterSectionType;
  animationDelay?: number;
}

const FooterSection: React.FC<FooterSectionProps> = ({ 
  section, 
  animationDelay = 200 
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <nav 
      aria-labelledby={`${section.id}-heading`} 
      className={cn(
        deviceInfo.isMobile 
          ? "mobile-fade-in" 
          : `animate-fade-in [animation-delay:${animationDelay}ms]`
      )}
    >
      <h3
        id={`${section.id}-heading`}
        className={cn(
          "font-bold mb-mobile-md",
          deviceInfo.isMobile ? "mobile-subheading" : "text-xl mb-4"
        )}
      >
        {section.title}
      </h3>
      <ul className={cn(
        deviceInfo.isMobile ? "space-y-mobile-sm" : "space-y-2"
      )}>
        {section.links.map((link) => (
          <li key={link.path}>
            <Link
              to={link.path}
              className={cn(
                "text-muted-foreground transition-colors touch-feedback",
                deviceInfo.isMobile
                  ? "mobile-text block py-mobile-xs"
                  : "hover:text-primary",
                // Special styling for "View All Locations" link
                link.name === "View All Locations" && "text-primary hover:underline"
              )}
            >
              {link.name}
            </Link>
          </li>
        ))}
      </ul>
    </nav>
  );
};

FooterSection.displayName = 'FooterSection';

export default FooterSection;
