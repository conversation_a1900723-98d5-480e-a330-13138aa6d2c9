import { useCallback, useEffect, useRef, useState } from 'react';

import { logWarning } from '@/lib/dev-console';

export type AsyncDataState = 'idle' | 'loading' | 'success' | 'error' | 'empty';

export interface AsyncDataOptions<T> {
  initialData?: T;
  retryAttempts?: number;
  retryDelay?: number;
  timeout?: number;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  onEmpty?: () => void;
  validateData?: (data: T) => boolean;
  transformData?: (data: unknown) => T;
}

export interface UseAsyncDataReturn<T> {
  data: T | null;
  state: AsyncDataState;
  error: Error | null;
  isLoading: boolean;
  isEmpty: boolean;
  hasError: boolean;
  isSuccess: boolean;
  retry: () => Promise<void>;
  refresh: () => Promise<void>;
  reset: () => void;
  execute: (loadFn: () => Promise<T>) => Promise<void>;
}

/**
 * Hook for managing async data loading with comprehensive state management
 */
export function useAsyncData<T>(
  loadFunction?: () => Promise<T>,
  options: AsyncDataOptions<T> = {}
): UseAsyncDataReturn<T> {
  const {
    initialData = null,
    retryAttempts = 3,
    retryDelay = 1000,
    timeout = 30000,
    onSuccess,
    onError,
    onEmpty,
    validateData,
    transformData
  } = options;

  const [data, setData] = useState<T | null>(initialData);
  const [state, setState] = useState<AsyncDataState>('idle');
  const [error, setError] = useState<Error | null>(null);
  const [attemptCount, setAttemptCount] = useState(0);

  const abortControllerRef = useRef<AbortController | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const currentLoadFunctionRef = useRef<(() => Promise<T>) | null>(loadFunction || null);

  // Derived states
  const isLoading = state === 'loading';
  const isEmpty = state === 'empty';
  const hasError = state === 'error';
  const isSuccess = state === 'success';

  // Cleanup function
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // Execute data loading
  const execute = useCallback(async (loadFn: () => Promise<T>) => {
    // Store the load function for retries
    currentLoadFunctionRef.current = loadFn;

    // Cleanup any existing requests
    cleanup();

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    setState('loading');
    setError(null);

    try {
      // Set timeout
      timeoutRef.current = setTimeout(() => {
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
      }, timeout);

      // Execute the load function
      let result = await loadFn();

      // Check if request was aborted
      if (signal.aborted) {
        return;
      }

      // Transform data if transformer provided
      if (transformData) {
        result = transformData(result);
      }

      // Validate data if validator provided
      if (validateData && !validateData(result)) {
        throw new Error('Data validation failed');
      }

      // Check if data is empty
      const isDataEmpty =
        result === null ||
        result === undefined ||
        (Array.isArray(result) && result.length === 0) ||
        (typeof result === 'string' && result.trim().length === 0) ||
        (typeof result === 'object' && Object.keys(result as Record<string, unknown>).length === 0);

      if (isDataEmpty) {
        setState('empty');
        setData(null);
        onEmpty?.();
      } else {
        setState('success');
        setData(result);
        onSuccess?.(result);
      }

      setAttemptCount(0);
    } catch (err) {
      if (signal.aborted) {
        return;
      }

      const error = err instanceof Error ? err : new Error('Unknown error occurred');
      setError(error);
      setState('error');
      onError?.(error);
    } finally {
      cleanup();
    }
  }, [cleanup, timeout, transformData, validateData, onSuccess, onError, onEmpty]);

  // Retry function with exponential backoff
  const retry = useCallback(async () => {
    if (!currentLoadFunctionRef.current) {
      logWarning('No load function available for retry');
      return;
    }

    if (attemptCount >= retryAttempts) {
      logWarning(`Maximum retry attempts (${retryAttempts}) reached`);
      return;
    }

    setAttemptCount(prev => prev + 1);

    // Add delay for retries with exponential backoff
    if (attemptCount > 0) {
      await new Promise(resolve =>
        setTimeout(resolve, Math.pow(2, attemptCount) * retryDelay)
      );
    }

    await execute(currentLoadFunctionRef.current);
  }, [attemptCount, retryAttempts, retryDelay, execute]);

  // Refresh function (same as retry but resets attempt count)
  const refresh = useCallback(async () => {
    if (!currentLoadFunctionRef.current) {
      logWarning('No load function available for refresh');
      return;
    }

    setAttemptCount(0);
    await execute(currentLoadFunctionRef.current);
  }, [execute]);

  // Reset function
  const reset = useCallback(() => {
    cleanup();
    setData(initialData);
    setState('idle');
    setError(null);
    setAttemptCount(0);
    currentLoadFunctionRef.current = null;
  }, [cleanup, initialData]);

  // Auto-execute on mount if load function provided
  useEffect(() => {
    if (loadFunction) {
      execute(loadFunction);
    }

    // Cleanup on unmount
    return cleanup;
  }, [loadFunction, execute, cleanup]);

  // Update load function reference when it changes
  useEffect(() => {
    currentLoadFunctionRef.current = loadFunction || null;
  }, [loadFunction]);

  return {
    data,
    state,
    error,
    isLoading,
    isEmpty,
    hasError,
    isSuccess,
    retry,
    refresh,
    reset,
    execute
  };
}

/**
 * Hook for loading medical data with specific validation
 */
export function useMedicalData<T>(
  loadFunction: () => Promise<T>,
  options: AsyncDataOptions<T> & {
    requiredFields?: string[];
  } = {}
): UseAsyncDataReturn<T> {
  const { requiredFields = [], ...baseOptions } = options;

  const validateMedicalData = useCallback((data: T): boolean => {
    if (!data || typeof data !== 'object') {
      return false;
    }

    // Check required fields
    for (const field of requiredFields) {
      if (!(field in (data as Record<string, unknown>))) {
        return false;
      }
    }

    return true;
  }, [requiredFields]);

  return useAsyncData(loadFunction, {
    ...baseOptions,
    validateData: validateMedicalData
  });
}

/**
 * Hook for loading paginated data
 */
export function usePaginatedAsyncData<T>(
  loadFunction: (page: number, limit: number) => Promise<{ items: T[]; total: number; hasMore: boolean }>,
  options: AsyncDataOptions<T[]> & {
    pageSize?: number;
    initialPage?: number;
  } = {}
): UseAsyncDataReturn<T[]> & {
  page: number;
  total: number;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  loadPage: (page: number) => Promise<void>;
} {
  const { pageSize = 10, initialPage = 1, ...baseOptions } = options;

  const [page, setPage] = useState(initialPage);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [allItems, setAllItems] = useState<T[]>([]);

  const paginatedLoadFunction = useCallback(async () => {
    const result = await loadFunction(page, pageSize);

    setTotal(result.total);
    setHasMore(result.hasMore);

    if (page === 1) {
      setAllItems(result.items);
    } else {
      setAllItems(prev => [...prev, ...result.items]);
    }

    return result.items;
  }, [loadFunction, page, pageSize]);

  const asyncData = useAsyncData(paginatedLoadFunction, baseOptions);

  const loadMore = useCallback(async () => {
    if (hasMore && !asyncData.isLoading) {
      setPage(prev => prev + 1);
    }
  }, [hasMore, asyncData.isLoading]);

  const loadPage = useCallback(async (targetPage: number) => {
    setPage(targetPage);
    if (targetPage === 1) {
      setAllItems([]);
    }
  }, []);

  return {
    ...asyncData,
    data: allItems,
    page,
    total,
    hasMore,
    loadMore,
    loadPage
  };
}
