import { render, screen, fireEvent } from '@testing-library/react';
import React from 'react';
import { describe, it, expect, vi } from 'vitest';

import EmptyState, {
  NoResultsState,
  NetworkErrorState,
  LoadingErrorState,
  CreateFirstState
} from '@/components/EmptyState';
import { DeviceProvider } from '@/contexts/DeviceContext';

// Test wrapper with DeviceProvider
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <DeviceProvider>
    {children}
  </DeviceProvider>
);

describe('EmptyState', () => {
  describe('Basic Rendering', () => {
    it('renders default empty state correctly', () => {
      render(
        <TestWrapper>
          <EmptyState />
        </TestWrapper>
      );

      expect(screen.getByText('No Data Available')).toBeInTheDocument();
      expect(screen.getByText(/There is currently no data to display/)).toBeInTheDocument();
    });

    it('renders with custom title and description', () => {
      render(
        <TestWrapper>
          <EmptyState
            title="Custom Title"
            description="Custom description text"
          />
        </TestWrapper>
      );

      expect(screen.getByText('Custom Title')).toBeInTheDocument();
      expect(screen.getByText('Custom description text')).toBeInTheDocument();
    });

    it('renders with actions', () => {
      const mockAction = vi.fn();

      render(
        <TestWrapper>
          <EmptyState
            actions={[
              {
                label: 'Test Action',
                onClick: mockAction,
                variant: 'default'
              }
            ]}
          />
        </TestWrapper>
      );

      const button = screen.getByRole('button', { name: 'Test Action' });
      expect(button).toBeInTheDocument();

      fireEvent.click(button);
      expect(mockAction).toHaveBeenCalledTimes(1);
    });

    it('renders with card wrapper when showCard is true', () => {
      const { container } = render(
        <TestWrapper>
          <EmptyState showCard={true} />
        </TestWrapper>
      );

      expect(container.querySelector('.border-dashed')).toBeInTheDocument();
    });

    it('renders with custom icon', () => {
      const CustomIcon = () => <div data-testid="custom-icon">Custom</div>;

      render(
        <TestWrapper>
          <EmptyState icon={CustomIcon} />
        </TestWrapper>
      );

      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    });
  });

  describe('Different Types', () => {
    it('renders no-results type correctly', () => {
      render(
        <TestWrapper>
          <EmptyState type="no-results" />
        </TestWrapper>
      );

      expect(screen.getByText('No Results Found')).toBeInTheDocument();
      expect(screen.getByText(/We couldn't find any results/)).toBeInTheDocument();
    });

    it('renders network-error type correctly', () => {
      render(
        <TestWrapper>
          <EmptyState type="network-error" />
        </TestWrapper>
      );

      expect(screen.getByText('Connection Error')).toBeInTheDocument();
      expect(screen.getByText(/Unable to load content due to a network issue/)).toBeInTheDocument();
    });

    it('renders loading-error type correctly', () => {
      render(
        <TestWrapper>
          <EmptyState type="loading-error" />
        </TestWrapper>
      );

      expect(screen.getByText('Loading Error')).toBeInTheDocument();
      expect(screen.getByText(/Something went wrong while loading/)).toBeInTheDocument();
    });

    it('renders no-reviews type correctly', () => {
      render(
        <TestWrapper>
          <EmptyState type="no-reviews" />
        </TestWrapper>
      );

      expect(screen.getByText('No Reviews Yet')).toBeInTheDocument();
      expect(screen.getByText(/Be the first to leave a review/)).toBeInTheDocument();
    });

    it('renders no-testimonials type correctly', () => {
      render(
        <TestWrapper>
          <EmptyState type="no-testimonials" />
        </TestWrapper>
      );

      expect(screen.getByText('No Testimonials Available')).toBeInTheDocument();
      expect(screen.getByText(/Patient testimonials will be displayed/)).toBeInTheDocument();
    });

    it('renders no-procedures type correctly', () => {
      render(
        <TestWrapper>
          <EmptyState type="no-procedures" />
        </TestWrapper>
      );

      expect(screen.getByText('No Procedures Found')).toBeInTheDocument();
      expect(screen.getByText(/No medical procedures match/)).toBeInTheDocument();
    });

    it('renders no-conditions type correctly', () => {
      render(
        <TestWrapper>
          <EmptyState type="no-conditions" />
        </TestWrapper>
      );

      expect(screen.getByText('No Conditions Found')).toBeInTheDocument();
      expect(screen.getByText(/No medical conditions match/)).toBeInTheDocument();
    });
  });

  describe('Sizes', () => {
    it('renders small size correctly', () => {
      const { container } = render(
        <TestWrapper>
          <EmptyState size="sm" />
        </TestWrapper>
      );

      // Check for mobile or desktop padding classes
      const element = container.querySelector('.py-8, .py-12');
      expect(element).toBeInTheDocument();
    });

    it('renders medium size correctly', () => {
      const { container } = render(
        <TestWrapper>
          <EmptyState size="md" />
        </TestWrapper>
      );

      expect(container.querySelector('.py-12, .py-16')).toBeInTheDocument();
    });

    it('renders large size correctly', () => {
      const { container } = render(
        <TestWrapper>
          <EmptyState size="lg" />
        </TestWrapper>
      );

      expect(container.querySelector('.py-16, .py-24')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <TestWrapper>
          <EmptyState />
        </TestWrapper>
      );

      const container = screen.getByRole('status');
      expect(container).toHaveAttribute('aria-live', 'polite');
    });

    it('has proper heading structure', () => {
      render(
        <TestWrapper>
          <EmptyState title="Test Title" />
        </TestWrapper>
      );

      const heading = screen.getByRole('heading', { level: 3 });
      expect(heading).toHaveTextContent('Test Title');
    });

    it('has accessible action buttons', () => {
      const mockAction = vi.fn();

      render(
        <TestWrapper>
          <EmptyState
            actions={[
              {
                label: 'Accessible Action',
                onClick: mockAction
              }
            ]}
          />
        </TestWrapper>
      );

      const button = screen.getByRole('button', { name: 'Accessible Action' });
      expect(button).toBeInTheDocument();
    });
  });
});

describe('NoResultsState', () => {
  it('renders with search term', () => {
    render(
      <TestWrapper>
        <NoResultsState
          searchTerm="test query"
          onClearSearch={vi.fn()}
        />
      </TestWrapper>
    );

    expect(screen.getByText('No results for "test query"')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Clear Search' })).toBeInTheDocument();
  });

  it('calls onClearSearch when clear button is clicked', () => {
    const mockClearSearch = vi.fn();

    render(
      <TestWrapper>
        <NoResultsState
          searchTerm="test"
          onClearSearch={mockClearSearch}
        />
      </TestWrapper>
    );

    fireEvent.click(screen.getByRole('button', { name: 'Clear Search' }));
    expect(mockClearSearch).toHaveBeenCalledTimes(1);
  });
});

describe('NetworkErrorState', () => {
  it('renders with retry button', () => {
    const mockRetry = vi.fn();

    render(
      <TestWrapper>
        <NetworkErrorState onRetry={mockRetry} />
      </TestWrapper>
    );

    expect(screen.getByText('Connection Error')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Try Again' })).toBeInTheDocument();
  });

  it('calls onRetry when retry button is clicked', () => {
    const mockRetry = vi.fn();

    render(
      <TestWrapper>
        <NetworkErrorState onRetry={mockRetry} />
      </TestWrapper>
    );

    fireEvent.click(screen.getByRole('button', { name: 'Try Again' }));
    expect(mockRetry).toHaveBeenCalledTimes(1);
  });
});

describe('LoadingErrorState', () => {
  it('renders with refresh button', () => {
    const mockRetry = vi.fn();

    render(
      <TestWrapper>
        <LoadingErrorState onRetry={mockRetry} />
      </TestWrapper>
    );

    expect(screen.getByText('Loading Error')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Refresh Page' })).toBeInTheDocument();
  });

  it('calls onRetry when refresh button is clicked', () => {
    const mockRetry = vi.fn();

    render(
      <TestWrapper>
        <LoadingErrorState onRetry={mockRetry} />
      </TestWrapper>
    );

    fireEvent.click(screen.getByRole('button', { name: 'Refresh Page' }));
    expect(mockRetry).toHaveBeenCalledTimes(1);
  });
});

describe('CreateFirstState', () => {
  it('renders with create button', () => {
    const mockCreate = vi.fn();

    render(
      <TestWrapper>
        <CreateFirstState
          itemType="Procedure"
          onCreate={mockCreate}
        />
      </TestWrapper>
    );

    expect(screen.getByText('No Procedures Yet')).toBeInTheDocument();
    expect(screen.getByText(/Get started by creating your first procedure/)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Create First Procedure' })).toBeInTheDocument();
  });

  it('calls onCreate when create button is clicked', () => {
    const mockCreate = vi.fn();

    render(
      <TestWrapper>
        <CreateFirstState
          itemType="Item"
          onCreate={mockCreate}
        />
      </TestWrapper>
    );

    fireEvent.click(screen.getByRole('button', { name: 'Create First Item' }));
    expect(mockCreate).toHaveBeenCalledTimes(1);
  });
});

describe('Custom Content', () => {
  it('renders children when provided', () => {
    render(
      <TestWrapper>
        <EmptyState>
          <div data-testid="custom-content">Custom content</div>
        </EmptyState>
      </TestWrapper>
    );

    expect(screen.getByTestId('custom-content')).toBeInTheDocument();
    expect(screen.getByText('Custom content')).toBeInTheDocument();
  });

  it('renders illustration when provided', () => {
    const { container } = render(
      <TestWrapper>
        <EmptyState illustration="/test-image.png" />
      </TestWrapper>
    );

    // Image has aria-hidden="true" so we need to query by selector
    const image = container.querySelector('img');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', '/test-image.png');
    expect(image).toHaveAttribute('alt', '');
  });
});

describe('Multiple Actions', () => {
  it('renders multiple action buttons', () => {
    const mockAction1 = vi.fn();
    const mockAction2 = vi.fn();

    render(
      <TestWrapper>
        <EmptyState
          actions={[
            {
              label: 'Primary Action',
              onClick: mockAction1,
              variant: 'default'
            },
            {
              label: 'Secondary Action',
              onClick: mockAction2,
              variant: 'outline'
            }
          ]}
        />
      </TestWrapper>
    );

    expect(screen.getByRole('button', { name: 'Primary Action' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Secondary Action' })).toBeInTheDocument();
  });

  it('calls correct action handlers', () => {
    const mockAction1 = vi.fn();
    const mockAction2 = vi.fn();

    render(
      <TestWrapper>
        <EmptyState
          actions={[
            {
              label: 'Action 1',
              onClick: mockAction1
            },
            {
              label: 'Action 2',
              onClick: mockAction2
            }
          ]}
        />
      </TestWrapper>
    );

    fireEvent.click(screen.getByRole('button', { name: 'Action 1' }));
    expect(mockAction1).toHaveBeenCalledTimes(1);
    expect(mockAction2).not.toHaveBeenCalled();

    fireEvent.click(screen.getByRole('button', { name: 'Action 2' }));
    expect(mockAction2).toHaveBeenCalledTimes(1);
  });
});
