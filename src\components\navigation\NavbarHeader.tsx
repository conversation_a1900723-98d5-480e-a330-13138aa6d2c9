import React from 'react';
import { Link } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import SkipLink from '@/components/SkipLink';
import { cn } from '@/lib/utils';

interface NavbarHeaderProps {
  scrolled: boolean;
  children: React.ReactNode;
}

const NavbarHeader: React.FC<NavbarHeaderProps> = ({ scrolled, children }) => {
  return (
    <header className={cn("fixed top-0 left-0 right-0 z-50 transition-all duration-300 border-0",
      scrolled ? "bg-background/95 backdrop-blur-lg py-3 shadow-md" : "bg-background/90 backdrop-blur-sm py-5")}>
      <SkipLink />
      <nav className="container mx-auto px-4 sm:px-6 lg:px-8" aria-label="Main navigation">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link to="/" className="flex items-center">
              <SafeImage
                src="/logo/logo.png"
                alt="miNEURO Logo"
                className="h-10 w-auto"
                fallbackSrc="/logo/logo-white.png"
              />
            </Link>
          </div>

          {/* Navigation */}
          <div className="flex-1 flex justify-center">
            {children}
          </div>
        </div>
      </nav>
    </header>
  );
};

NavbarHeader.displayName = 'NavbarHeader';

export default NavbarHeader;
