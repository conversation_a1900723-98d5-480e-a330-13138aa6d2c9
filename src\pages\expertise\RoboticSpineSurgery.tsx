import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import ExpertiseCallToAction from '@/components/expertise/ExpertiseCallToAction';
import ExpertiseHero from '@/components/expertise/ExpertiseHero';
import ExpertiseSidebar from '@/components/expertise/ExpertiseSidebar';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { roboticSpineSurgeryData } from '@/data/expertise/roboticSpineSurgeryData';

/**
 * Robotic Spine Surgery Component
 * Unified implementation combining comprehensive content with modular architecture
 * Includes all sections from both original and refactored versions plus documentation content
 */

const RoboticSpineSurgery: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const data = roboticSpineSurgeryData;

  // Enhanced content from documentation
  const roboticSystemComponents = [
    {
      title: 'Robotic Arms and Instrumentation',
      features: ['Multi-axis robotic arms', 'Precision instrument control', 'Tremor elimination technology', 'Consistent placement accuracy']
    },
    {
      title: 'Navigation and Imaging',
      features: ['Integrated navigation system', 'Real-time imaging feedback', '3D surgical planning', 'Continuous monitoring']
    },
    {
      title: 'AI-Assisted Planning',
      features: ['Virtual surgery rehearsal', 'Optimal trajectory planning', 'Risk assessment', 'Outcome prediction']
    }
  ];

  const surgicalApplications = [
    {
      category: 'Spinal Fusion Procedures',
      procedures: ['Pedicle screw insertion', 'Rod placement', 'Cage positioning', 'Hardware optimization']
    },
    {
      category: 'Complex Spinal Procedures',
      procedures: ['Deformity correction', 'Tumor resection', 'Revision surgery', 'Minimally invasive techniques']
    }
  ];

  const precisionFeatures = [
    {
      title: 'Submillimeter Accuracy',
      description: 'Robotic precision measurement with consistent placement, reduced variability, and enhanced safety margins.'
    },
    {
      title: 'Quality Assurance',
      description: 'Real-time verification with immediate feedback, error prevention, and outcome optimization.'
    }
  ];

  const patientBenefits = [
    {
      category: 'Enhanced Safety',
      benefits: ['Reduced complication rates', 'Improved accuracy', 'Better outcomes', 'Faster recovery']
    },
    {
      category: 'Minimally Invasive Approach',
      benefits: ['Smaller incisions', 'Less tissue damage', 'Reduced blood loss', 'Shorter hospital stays']
    }
  ];

  const clinicalOutcomes = [
    {
      metric: 'Accuracy',
      value: '99.2%',
      description: 'Screw placement accuracy'
    },
    {
      metric: 'Complications',
      value: '<2%',
      description: 'Reduced complication rates'
    },
    {
      metric: 'Satisfaction',
      value: '95%+',
      description: 'Patient satisfaction scores'
    },
    {
      metric: 'Recovery',
      value: '40%',
      description: 'Faster recovery time'
    }
  ];

  return (
    <StandardPageLayout title="Robotic Spine Surgery" showHeader={false}>
      <ExpertiseHero
        title={data.hero.title}
        subtitle={data.hero.subtitle}
        backgroundImage={data.hero.backgroundImage}
      />

      <div className="flex-1">

        {/* Main Content */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                {/* Overview Section */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.overview.title}</h2>
                  {data.overview.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-6">
                      {paragraph}
                    </p>
                  ))}
                </div>

                {/* History Section */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.history.title}</h2>
                  {data.history.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-4">
                      {paragraph}
                    </p>
                  ))}

                  <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                    <SafeImage
                      src={data.images.overview}
                      alt="Robotic Spine Surgery System Overview"
                      className="w-full h-full object-cover"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  </div>
                </div>

                {/* Robotic System Components */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Robotic System Components</h2>
                  <p className="text-muted-foreground mb-6">
                    The sophisticated robotic surgical system consists of multiple integrated components working together to provide unparalleled precision:
                  </p>

                  <div className="space-y-6">
                    {roboticSystemComponents.map((component, index) => (
                      <div key={index} className="border rounded-lg p-6 medical-card">
                        <h3 className="text-lg font-semibold mb-3 text-primary">{component.title}</h3>
                        <div className="grid grid-cols-2 gap-2">
                          {component.features.map((feature, featureIndex) => (
                            <div key={featureIndex} className="flex items-center text-sm text-muted-foreground">
                              <span className="w-2 h-2 bg-primary rounded-full mr-2"></span>
                              {feature}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* How It Works */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.howItWorks.title}</h2>
                  <p className="text-muted-foreground mb-4">{data.howItWorks.introduction}</p>
                  <ol className="list-decimal list-inside text-muted-foreground mb-8 space-y-2">
                    {data.howItWorks.steps.map((step, index) => (
                      <li key={index}>{step}</li>
                    ))}
                  </ol>

                  {data.howItWorks.sections.map((section, index) => (
                    <div key={index} className="mb-6">
                      <h3 className="text-xl font-semibold mb-3">{section.title}</h3>
                      {section.content.map((paragraph, pIndex) => (
                        <p key={pIndex} className="text-muted-foreground mb-4">
                          {paragraph}
                        </p>
                      ))}
                    </div>
                  ))}
                </div>

                {/* Surgical Applications */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Surgical Applications</h2>
                  <p className="text-muted-foreground mb-6">
                    Robotic spine surgery is utilized across various complex spinal procedures:
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {surgicalApplications.map((application, index) => (
                      <div key={index} className="bg-muted/30 p-6 rounded-lg">
                        <h3 className="text-lg font-semibold mb-4">{application.category}</h3>
                        <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                          {application.procedures.map((procedure, procIndex) => (
                            <li key={procIndex}>{procedure}</li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Precision and Accuracy */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Precision and Accuracy</h2>
                  <p className="text-muted-foreground mb-6">
                    The exceptional precision of robotic surgery ensures optimal surgical outcomes:
                  </p>

                  <div className="space-y-6">
                    {precisionFeatures.map((feature, index) => (
                      <div key={index} className="bg-primary/10 p-6 rounded-lg">
                        <h3 className="text-lg font-semibold mb-3 text-primary">{feature.title}</h3>
                        <p className="text-muted-foreground">{feature.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Patient Benefits */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Patient Benefits</h2>
                  <p className="text-muted-foreground mb-6">
                    Robotic spine surgery offers significant advantages for patients:
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {patientBenefits.map((benefit, index) => (
                      <div key={index} className="bg-success border border-success p-6 rounded-lg">
                        <h3 className="text-lg font-semibold mb-4 text-success">{benefit.category}</h3>
                        <ul className="list-disc pl-5 space-y-2 text-success">
                          {benefit.benefits.map((item, itemIndex) => (
                            <li key={itemIndex}>{item}</li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Clinical Outcomes */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Clinical Outcomes</h2>
                  <p className="text-muted-foreground mb-6">
                    Evidence-based results demonstrate the effectiveness of robotic spine surgery:
                  </p>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {clinicalOutcomes.map((outcome, index) => (
                      <div key={index} className="text-center p-4 bg-primary/5 rounded-lg">
                        <div className="text-enhanced-heading text-2xl font-bold text-primary mb-2">{outcome.value}</div>
                        <div className="text-sm font-semibold mb-1">{outcome.metric}</div>
                        <div className="text-xs text-muted-foreground">{outcome.description}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Applications */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">{data.applications.title}</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {data.applications.items.map((item, index) => (
                      <div key={index} className="card p-6 rounded-lg shadow-md medical-card">
                        <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                        <p className="text-muted-foreground">{item.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Neuromonitoring in Robotic Spine Surgery</h2>
                <p className="text-muted-foreground mb-4">
                  Intraoperative multimodality monitoring is an essential part of robotic and minimally invasive spine surgery. Electromyography (EMG) and somatosensory (SSEP) and motor evoked potentials (MEP) allow to record the electrical activity of muscles and to assess the health and function of the spinal cord, nerves and muscles.
                </p>
                <p className="text-muted-foreground mb-4">
                  EMG is regularly used in robotic procedures to confirm good placement of pedicle screws and to avoid nerve impingement. Recording of the electrical muscle activity assesses the nerve proximity and location. <a href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">Dr Aliashkevich</a> uses both stimulated and free-run EMG to ensure safe surgery and to reduce the risk of postoperative complications.
                </p>
                <p className="text-muted-foreground mb-4">
                  SSEP and MEP monitor the function of the spinal cord and to recognise early possible changes caused by reduced blood flow (ischemia), compression, manipulations, or body positioning during surgery.
                </p>

                <p className="text-muted-foreground mb-4">
                  EMG, MEP and/or SSEP monitoring can be safely used during minimally invasive and robotic surgery on the cervical, thoracic, or lumbar spine for procedures including:
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <ul className="list-disc list-inside text-muted-foreground space-y-1">
                    <li>Lumbar and thoracic microdiscectomy</li>
                    <li>Cervical, thoracic and lumbar laminectomy</li>
                    <li>Foraminotomy</li>
                    <li>Spinal decompression</li>
                    <li>Cervical and lumbar disc replacement</li>
                    <li>Anterior cervical discectomy and fusion (ACDF)</li>
                    <li>Occipito-cervical fusion</li>
                    <li>Cervical, thoracic and lumbar corpectomy</li>
                  </ul>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1">
                    <li>Lumbar fusions (ALIF, PLIF, TLIF, XLIF, OLIF)</li>
                    <li>Pedicle screw instrumentation</li>
                    <li>Cervical, thoracic and lumbar laminoplasty</li>
                    <li>Posterior cervical thoracic and lumbar fusion</li>
                    <li>Removal of AVM, cavernomas and tumours</li>
                    <li>Minimally invasive surgery</li>
                    <li>Scoliosis surgery</li>
                    <li>Vertebroplasty and kyphoplasty</li>
                  </ul>
                </div>
                <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                  <SafeImage
                    src="/images/neuromonitoring-EMG-MEP-SSEP-spinal-nerve-root-stimulation.jpg"
                    alt="Neuromonitoring in Spine Surgery - EMG, MEP, and SSEP monitoring"
                    className="w-full h-full object-cover"
                    fallbackSrc="/images/medical-consulting.jpg"
                  />
                </div>

                {/* Specialised Applications */}
                <div className="mb-8">
                  <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Specialised Robotic Surgery Information</h2>
                  <p className="text-muted-foreground mb-6">
                    Explore comprehensive information about robotic spine surgery technology and clinical procedures:
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Technology Card */}
                    <div className="card p-8 rounded-lg shadow-lg bg-muted border-l-4 border-l-purple-500">
                      <div className="flex items-center mb-4">
                        <div className="w-12 h-12 bg-medical-blue rounded-lg flex items-center justify-center mr-4">
                          <span className="text-primary-foreground text-2xl">🤖</span>
                        </div>
                        <h3 className="text-xl font-bold text-medical-blue">Robotic Surgery Technology</h3>
                      </div>
                      <p className="text-medical-blue mb-6">
                        Advanced robotic platforms and AI-assisted surgical planning for precision spine surgery with cutting-edge technology.
                      </p>
                      <ul className="list-disc pl-5 space-y-2 text-medical-blue mb-6 text-sm">
                        <li>State-of-the-art robotic systems (Mazor X, ExcelsiusGPS, ROSA)</li>
                        <li>Multi-axis robotic arms and navigation</li>
                        <li>AI-assisted surgical planning</li>
                        <li>Precision capabilities and safety features</li>
                        <li>Advanced system components</li>
                      </ul>
                      <Button asChild className="w-full bg-medical-blue hover:bg-medical-blue">
                        <Link to="/expertise/robotic-spine-surgery/technology">Explore Technology</Link>
                      </Button>
                    </div>

                    {/* Procedures Card */}
                    <div className="card p-8 rounded-lg shadow-lg bg-muted border-l-4 border-l-orange-500">
                      <div className="flex items-center mb-4">
                        <div className="w-12 h-12 bg-info rounded-lg flex items-center justify-center mr-4">
                          <span className="text-primary-foreground text-2xl">🏥</span>
                        </div>
                        <h3 className="text-xl font-bold text-info">Robotic Surgery Procedures</h3>
                      </div>
                      <p className="text-info mb-6">
                        Evidence-based clinical applications and documented advantages of robotic-assisted spine surgery procedures.
                      </p>
                      <ul className="list-disc pl-5 space-y-2 text-info mb-6 text-sm">
                        <li>Clinical applications and evidence-based outcomes</li>
                        <li>Spinal conditions that benefit most</li>
                        <li>Documented advantages over traditional techniques</li>
                        <li>Major robotic platforms in use</li>
                        <li>Patient benefits and clinical outcomes</li>
                      </ul>
                      <Button asChild className="w-full bg-info hover:bg-info">
                        <Link to="/expertise/robotic-spine-surgery/procedures">Explore Procedures</Link>
                      </Button>
                    </div>
                  </div>
                </div>

                <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Benefits of Robotic Spine Surgery</h2>
                <p className="text-muted-foreground mb-4">
                  Robotic spine surgery can often be performed in difficult clinical scenarios when open surgery is deemed too risky, e.g. in elderly, obese or medically unfit patients. It causes less operative trauma and blood loss, resulting in reduced postoperative pain and scarring.
                </p>
                <p className="text-muted-foreground mb-4">
                  Please note, however, that not every patient can be considered as a candidate for image-guided or robotic surgery. Some simpler procedures such as microdiscectomy, laminectomy, artificial disc replacement, anterior cervical discectomy and fusion are still the domain of traditional microsurgery.
                </p>
                <p className="text-muted-foreground mb-4">
                  The advantages of robotic surgery are the same as for any other navigated spinal surgery:
                </p>
                <ul className="list-disc list-inside text-muted-foreground mb-8 space-y-2">
                  <li>Better quality of treatment, including improved precision of screw placement (98% accuracy compared to 92% using fluoroscopy based methods)</li>
                  <li>Improved outcomes</li>
                  <li>Decreased incision size with better cosmetic results</li>
                  <li>Reduced risk of infection and wound healing problems</li>
                  <li>Reduced risk of medical complications, e.g. chest infections and deep venous thrombosis</li>
                  <li>Reduced radiation exposure to the patient and operating theatre staff</li>
                  <li>Increased safety and avoidance of critical structures during the approach</li>
                  <li>Shorter operation time</li>
                  <li>Diminished reliance on pain medications after surgery</li>
                  <li>Faster postoperative recovery</li>
                  <li>Earlier and more efficient physiotherapy and rehabilitation</li>
                  <li>Faster return to normal activities and work</li>
                  <li>Shorter hospital stay</li>
                  <li>Reduced overall cost of treatment</li>
                </ul>

                <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Conditions Treated with Robotic Spine Surgery</h2>
                <p className="text-muted-foreground mb-4">
                  <a href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">Dr Aliashkevich</a> utilizes robotic spine surgery to treat a variety of conditions, including:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div className="card p-6 rounded-lg shadow-md medical-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">Degenerative Disc Disease</h3>
                    <p className="text-muted-foreground">
                      Precise treatment of worn spinal discs causing pain and limited mobility.
                    </p>
                  </div>

                  <div className="card p-6 rounded-lg shadow-md medical-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">Spinal Stenosis</h3>
                    <p className="text-muted-foreground">
                      Accurate decompression of narrowed spinal canals that compress nerves.
                    </p>
                  </div>

                  <div className="card p-6 rounded-lg shadow-md medical-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">Spondylolisthesis</h3>
                    <p className="text-muted-foreground">
                      Precise correction of vertebral slippage to restore spinal alignment and stability.
                    </p>
                  </div>

                  <div className="card p-6 rounded-lg shadow-md medical-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">Spinal Deformities</h3>
                    <p className="text-muted-foreground">
                      Enhanced accuracy for complex curvature corrections in conditions like scoliosis.
                    </p>
                  </div>

                  <div className="card p-6 rounded-lg shadow-md medical-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">Spinal Fractures</h3>
                    <p className="text-muted-foreground">
                      Precise stabilization of broken vertebrae with minimal disruption to surrounding tissues.
                    </p>
                  </div>

                  <div className="card p-6 rounded-lg shadow-md medical-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">Failed Back Surgery</h3>
                    <p className="text-muted-foreground">
                      Accurate revision procedures for patients who have had unsuccessful previous spine surgeries.
                    </p>
                  </div>
                </div>

                <h2 className="text-enhanced-heading text-2xl font-bold mb-6">Why Choose Dr Aliashkevich for Robotic Spine Surgery?</h2>
                <p className="text-muted-foreground mb-4">
                  <a href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">Dr Aliashkevich</a> is at the forefront of robotic spine surgery and offers:
                </p>
                <ul className="list-disc list-inside text-muted-foreground mb-8 space-y-2">
                  <li>Specialised training and expertise in robotic spine surgery systems</li>
                  <li>Experience with the latest robotic technologies and techniques</li>
                  <li>Commitment to minimally invasive approaches whenever possible</li>
                  <li>Personalised treatment plans tailored to each patient's unique anatomy and condition</li>
                  <li>Comprehensive pre-operative planning using advanced imaging</li>
                  <li>Dedication to achieving optimal surgical outcomes with reduced recovery times</li>
                  <li>Experience with the Mazor Renaissance robotic system</li>
                  <li>Expertise in complex spinal conditions requiring precise instrumentation</li>
                  <li>Integration of neuromonitoring for enhanced safety during procedures</li>
                  <li>Ongoing education in the latest advances in robotic spine surgery</li>
                </ul>

                <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                  <SafeImage
                    src="/images/robotic-spine-surgery-radiology-successful-neurosurgery-spine-surgery-advanced-technology-maximum-precision.jpg"
                    alt="Successful robotic spine surgery with precise screw placement"
                    className="w-full h-full object-cover"
                    fallbackSrc="/images/medical-consulting.jpg"
                  />
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <ExpertiseSidebar sections={data.sidebar} />
              </div>
            </div>
          </div>
        </section>
        {/* Call to Action */}
        <ExpertiseCallToAction
          title={data.cta.title}
          description={data.cta.description}
          primaryButton={data.cta.primaryButton}
          secondaryButton={data.cta.secondaryButton}
        />
      </div>
    </StandardPageLayout>
  );
};

RoboticSpineSurgery.displayName = 'RoboticSpineSurgery';

export default RoboticSpineSurgery;
