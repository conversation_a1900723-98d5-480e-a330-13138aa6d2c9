import React from 'react';

interface IntroductionSectionProps {
  title: string;
  paragraphs: string[];
  image: {
    src: string;
    alt: string;
    caption: string;
  };
}

/**
 * IntroductionSection Component
 * Introduction section with image and content
 * Preserves exact styling and layout from original implementation
 */
const IntroductionSection: React.FC<IntroductionSectionProps> = ({
  title,
  paragraphs,
  image
}) => {
  return (
    <section className="py-20 bg-gradient-to-br from-background via-background/95 to-muted/30">
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-16 items-center max-w-6xl mx-auto">
          <div>
            <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-8 text-foreground">{title}</h2>
            {paragraphs.map((paragraph, index) => (
              <p key={index} className="text-foreground/80 mb-6 text-lg leading-relaxed font-medium">
                {paragraph}
              </p>
            ))}
          </div>
          <div className="space-y-4">
            <div className="rounded-xl overflow-hidden shadow-xl border border-border/30">
              <img
                src={image.src}
                alt={image.alt}
                className="w-full h-auto"
              />
            </div>
            <div className="p-4 rounded-lg bg-muted/50 border border-border/50">
              <p className="text-base font-semibold text-muted-foreground leading-relaxed">
                {image.caption}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

IntroductionSection.displayName = 'IntroductionSection';

export default IntroductionSection;
