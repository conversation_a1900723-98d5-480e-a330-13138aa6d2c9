import { Brain, Bone, Network, Layers, Info, ChevronDown, ChevronUp, Activity, Zap, Waves, CheckCircle, AlertTriangle } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface SpinalStructure {
  structure: string;
  description: string;
  normalFunction: string[];
  myelopathyEffects: string[];
}

interface SpinalAnatomySectionProps {
  title: string;
  description: string;
  spinalStructures: SpinalStructure[];
}

export function SpinalAnatomySection({ 
  title, 
  description, 
  spinalStructures 
}: SpinalAnatomySectionProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedStructure, setExpandedStructure] = useState<string | null>(null);

  const toggleExpanded = (structure: string) => {
    setExpandedStructure(expandedStructure === structure ? null : structure);
  };

  const getStructureIcon = (structure: string) => {
    if (structure.includes('Spinal Cord')) return Brain;
    if (structure.includes('Vertebrae')) return Bone;
    if (structure.includes('Disc')) return Layers;
    if (structure.includes('Ligament')) return Network;
    return Activity;
  };

  const getStructureColor = (structure: string) => {
    if (structure.includes('Spinal Cord')) return 'text-foreground bg-info-light border-info/30';
    if (structure.includes('Vertebrae')) return 'text-foreground bg-success-light border-success/30';
    if (structure.includes('Disc')) return 'text-foreground bg-medical-blue-light border-medical-blue/30';
    if (structure.includes('Ligament')) return 'text-foreground bg-info-light border-info/30';
    return 'text-muted-foreground bg-muted border-border/30';
  };

  const getStructureImportance = (structure: string) => {
    if (structure.includes('Spinal Cord')) {
      return { level: 'Neural Tissue', colour: 'badge-info' };
    }
    if (structure.includes('Vertebrae')) {
      return { level: 'Bony Structure', colour: 'badge-routine' };
    }
    if (structure.includes('Disc')) {
      return { level: 'Soft Tissue', colour: 'badge-medical' };
    }
    return { level: 'Supporting Structure', colour: 'badge-info' };
  };

  return (
    <section className={cn(
      "section-spacing section-background-alt",
      deviceInfo.isMobile ? "px-4" : ""
    )}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className={cn(
            "text-enhanced-heading font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-enhanced-body max-w-3xl mx-auto leading-relaxed",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Cervical Spine Overview */}
        <div className="mb-12">
          <Card className="medical-card">
            <CardContent className="pt-8 pb-8">
              <div className={cn(
                "grid gap-8 items-center",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div className="space-y-6">
                  <h3 className="text-enhanced-heading text-xl font-semibold mb-4">Cervical Spine Anatomy and Spinal Cord Function</h3>
                  <p className="text-enhanced-body mb-6 leading-relaxed">
                    The cervical spine consists of seven vertebrae (C1-C7) that protect the spinal cord
                    while allowing neck movement. In cervical myelopathy, compression of the spinal cord
                    disrupts the transmission of signals between the brain and the rest of the body.
                  </p>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 rounded-lg bg-info-light border border-info/30">
                      <div className="w-4 h-4 bg-info rounded-full flex-shrink-0"></div>
                      <span className="text-enhanced-body text-sm font-medium">Cervical Spinal Cord (Neural Pathways)</span>
                    </div>
                    <div className="flex items-center gap-3 p-3 rounded-lg bg-success-light border border-success/30">
                      <div className="w-4 h-4 bg-success rounded-full flex-shrink-0"></div>
                      <span className="text-enhanced-body text-sm font-medium">Cervical Vertebrae (Bony Protection)</span>
                    </div>
                    <div className="flex items-center gap-3 p-3 rounded-lg bg-medical-blue-light border border-medical-blue/30">
                      <div className="w-4 h-4 bg-medical-blue rounded-full flex-shrink-0"></div>
                      <span className="text-enhanced-body text-sm font-medium">Intervertebral Discs (Shock Absorption)</span>
                    </div>
                    <div className="flex items-center gap-3 p-3 rounded-lg bg-info-light border border-info/30">
                      <div className="w-4 h-4 bg-info rounded-full flex-shrink-0"></div>
                      <span className="text-enhanced-body text-sm font-medium">Ligaments (Stability and Support)</span>
                    </div>
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="space-y-4 max-w-lg">
                    <div className="relative group">
                      <img
                        src="/images/spine-conditions/cervical-myelopathy-anatomy.jpg"
                        alt="Cervical myelopathy anatomy showing spinal cord compression"
                        className="rounded-xl shadow-xl max-w-full h-auto transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-background/30 via-transparent to-transparent rounded-xl"></div>
                    </div>
                    <div className="p-3 rounded-lg bg-muted/50 border border-border/50">
                      <p className="text-xs font-medium text-muted-foreground leading-relaxed text-center">
                        Cervical spine anatomy showing normal and compressed spinal cord
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Spinal Structure Details */}
        <div className="space-y-6">
          {spinalStructures.map((structure, index) => {
            const Icon = getStructureIcon(structure.structure);
            const isExpanded = expandedStructure === structure.structure;
            const structureImportance = getStructureImportance(structure.structure);

            return (
              <Card key={index} className={cn(
                "transition-all duration-300 hover:shadow-xl backdrop-blur-sm",
                getStructureColor(structure.structure),
                isExpanded && "ring-2 ring-primary/20"
              )}>
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="p-3 rounded-xl bg-background/90 dark:bg-muted/90 shadow-lg backdrop-blur-sm">
                        <Icon className="h-6 w-6 text-current" />
                      </div>
                      <div className="space-y-1">
                        <CardTitle className="text-lg font-bold text-current leading-tight">
                          {structure.structure}
                        </CardTitle>
                        <CardDescription className="text-sm text-current/80 font-medium leading-relaxed">
                          {structure.description}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Badge className={structureImportance.colour}>
                        {structureImportance.level}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(structure.structure)}
                        className="flex items-center gap-2 enhanced-hover"
                      >
                        <Info className="h-4 w-4" />
                        <span className="text-xs font-medium">
                          {isExpanded ? 'Less' : 'More'}
                        </span>
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0 pb-6">
                      <div className={cn(
                        "grid gap-8",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        {/* Normal Functions */}
                        <div className="space-y-4">
                          <h4 className="text-enhanced-strong font-bold mb-4 flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-success" />
                            Normal Functions
                          </h4>
                          <ul className="space-y-3">
                            {structure.normalFunction.map((func, idx) => (
                              <li key={idx} className="flex items-start gap-3 p-3 rounded-lg medical-card-inner">
                                <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm font-medium leading-relaxed">{func}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Myelopathy Effects */}
                        <div className="space-y-4">
                          <h4 className="text-enhanced-strong font-bold mb-4 flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4 text-foreground" />
                            Effects in Myelopathy
                          </h4>
                          <ul className="space-y-3">
                            {structure.myelopathyEffects.map((effect, idx) => (
                              <li key={idx} className="flex items-start gap-3 p-3 rounded-lg medical-card-inner">
                                <div className="w-2 h-2 bg-muted rounded-full mt-2 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm font-medium leading-relaxed">{effect}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Additional Information */}
                      <div className="mt-8 p-6 medical-card-inner rounded-xl">
                        <h4 className="text-enhanced-heading font-bold mb-3 flex items-center gap-2">
                          <Brain className="h-4 w-4" />
                          Clinical Significance
                        </h4>
                        <p className="text-enhanced-body text-sm leading-relaxed">
                          {structure.structure.includes('Spinal Cord') &&
                            "The cervical spinal cord contains critical pathways for motor and sensory function. Compression at this level can cause symptoms affecting both upper and lower extremities, making early recognition and treatment essential to prevent permanent neurological damage."
                          }
                          {structure.structure.includes('Vertebrae') &&
                            "The cervical vertebrae provide protection for the spinal cord while allowing neck mobility. Degenerative changes including bone spur formation and loss of normal alignment are common causes of spinal cord compression in cervical myelopathy."
                          }
                          {structure.structure.includes('Disc') &&
                            "Intervertebral discs act as shock absorbers and maintain proper spacing between vertebrae. Disc degeneration and herniation are major contributors to cervical myelopathy, particularly at the C5-C6 and C6-C7 levels."
                          }
                          {structure.structure.includes('Ligament') &&
                            "The ligamentum flavum provides stability to the spine but can thicken with age and buckle into the spinal canal. This is a common cause of posterior compression in cervical myelopathy, particularly in elderly patients."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Compression Mechanism */}
        <div className="mt-12">
          <Card className="medical-card">
            <CardHeader className="text-center pb-8">
              <CardTitle className="text-enhanced-heading flex items-center justify-center gap-3 text-xl">
                <Waves className="h-6 w-6 text-info" />
                Spinal Cord Compression Mechanism
              </CardTitle>
              <CardDescription className="text-enhanced-body text-base max-w-2xl mx-auto leading-relaxed">
                Understanding how cervical myelopathy develops and progresses
              </CardDescription>
            </CardHeader>
            <CardContent className="pb-8">
              <div className={cn(
                "grid gap-8",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="text-center group">
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-primary text-primary-foreground rounded-2xl flex items-center justify-center font-bold mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                      1
                    </div>
                    <div className="absolute -inset-2 bg-info/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                  <h4 className="text-enhanced-strong font-bold mb-3">Initial Compression</h4>
                  <p className="text-enhanced-body text-sm leading-relaxed">
                    Degenerative changes or trauma cause narrowing of the spinal canal
                  </p>
                </div>
                <div className="text-center group">
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-primary text-primary-foreground rounded-2xl flex items-center justify-center font-bold mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                      2
                    </div>
                    <div className="absolute -inset-2 bg-info/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                  <h4 className="text-enhanced-strong font-bold mb-3">Cord Dysfunction</h4>
                  <p className="text-enhanced-body text-sm leading-relaxed">
                    Pressure on spinal cord disrupts normal signal transmission
                  </p>
                </div>
                <div className="text-center group">
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-primary text-primary-foreground rounded-2xl flex items-center justify-center font-bold mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                      3
                    </div>
                    <div className="absolute -inset-2 bg-muted/40 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                  <h4 className="text-enhanced-strong font-bold mb-3">Progressive Damage</h4>
                  <p className="text-enhanced-body text-sm leading-relaxed">
                    Continued compression leads to irreversible neurological damage
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Warning About Progression */}
        <div className="mt-12">
          <Card className="medical-card bg-muted-light border border-border/70">
            <CardHeader className="pb-4">
              <CardTitle className="text-enhanced-heading flex items-center gap-3 text-foreground text-lg">
                <div className="p-2 bg-muted-light text-foreground-foreground rounded-lg">
                  <Zap className="h-5 w-5" />
                </div>
                Progressive Nature of Cervical Myelopathy
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="medical-card-inner rounded-xl p-6">
                <p className="text-enhanced-body text-foreground text-sm leading-relaxed font-medium">
                  Cervical myelopathy is typically a progressive condition that worsens over time without treatment.
                  The spinal cord has limited ability to recover from compression injury, making early recognition
                  and appropriate intervention crucial for preventing permanent neurological damage and maintaining
                  quality of life.
                </p>
                <div className="mt-4 p-4 bg-muted-light rounded-lg border border-border/70">
                  <p className="text-xs text-foreground font-semibold">
                    ⚠️ Early intervention is key to preventing irreversible damage
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default SpinalAnatomySection;
